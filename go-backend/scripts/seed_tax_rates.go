package main

import (
	"fmt"
	"log"
	"time"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("Seeding tax rates...")

	// Sample branch ID - you might need to adjust this based on your data
	branchID := "test-branch-123"

	// Check if we already have tax rates for this branch
	var existingCount int64
	db.Model(&models.TaxRate{}).Where("branch_id = ?", branchID).Count(&existingCount)
	
	if existingCount > 0 {
		fmt.Printf("Found %d existing tax rates for branch %s\n", existingCount, branchID)
		return
	}

	// Create sample tax rates
	taxRates := []models.TaxRate{
		{
			ID:          uuid.New().String(),
			BranchID:    branchID,
			Name:        "Sales Tax",
			Rate:        decimal.NewFromFloat(0.08), // 8%
			Description: stringPtr("Standard sales tax for goods and services"),
			IsActive:    true,
			IsDefault:   true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New().String(),
			BranchID:    branchID,
			Name:        "VAT",
			Rate:        decimal.NewFromFloat(0.20), // 20%
			Description: stringPtr("Value Added Tax for European transactions"),
			IsActive:    true,
			IsDefault:   false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New().String(),
			BranchID:    branchID,
			Name:        "Service Tax",
			Rate:        decimal.NewFromFloat(0.05), // 5%
			Description: stringPtr("Tax applied to service-based transactions"),
			IsActive:    true,
			IsDefault:   false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			ID:          uuid.New().String(),
			BranchID:    branchID,
			Name:        "Luxury Tax",
			Rate:        decimal.NewFromFloat(0.15), // 15%
			Description: stringPtr("Higher tax rate for luxury items"),
			IsActive:    false,
			IsDefault:   false,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	// Insert tax rates
	for _, taxRate := range taxRates {
		if err := db.Create(&taxRate).Error; err != nil {
			log.Printf("Failed to create tax rate %s: %v", taxRate.Name, err)
		} else {
			fmt.Printf("Created tax rate: %s (%.2f%%)\n", taxRate.Name, taxRate.Rate.InexactFloat64()*100)
		}
	}

	fmt.Println("Tax rates seeding completed!")
}

func stringPtr(s string) *string {
	return &s
}
