package dto

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/shopspring/decimal"
)

// CreateCreditNoteRequest represents the request payload for creating a credit note
type CreateCreditNoteRequest struct {
	CustomerID       *string                       `json:"customerId,omitempty"`
	InvoiceID        *string                       `json:"invoiceId,omitempty"`
	CreditNoteNumber *string                       `json:"creditNoteNumber,omitempty"` // Optional, will be auto-generated if not provided
	Type             string                        `json:"type" binding:"required" validate:"oneof=CustomerRefund SupplierRefund WriteOff ReturnCredit Adjustment"`
	IssueDate        time.Time                     `json:"issueDate" binding:"required"`
	Reason           string                        `json:"reason" binding:"required" validate:"min=1,max=500"`
	Notes            *string                       `json:"notes,omitempty" validate:"omitempty,max=1000"`
	Status           *string                       `json:"status,omitempty" validate:"omitempty,oneof=Draft Open Closed Void"`
	Items            []CreateCreditNoteItemRequest `json:"items" binding:"required,min=1"`
}

// CreateCreditNoteItemRequest represents the request payload for creating a credit note item
type CreateCreditNoteItemRequest struct {
	Description string          `json:"description" binding:"required" validate:"min=1,max=255"`
	Quantity    decimal.Decimal `json:"quantity" binding:"required" validate:"gt=0"`
	UnitPrice   decimal.Decimal `json:"unitPrice" binding:"required" validate:"gte=0"`
	TaxRateID   *string         `json:"taxRateId,omitempty"`
	TaxAmount   decimal.Decimal `json:"taxAmount" validate:"gte=0"`
	SortOrder   *int            `json:"sortOrder,omitempty"`
}

// UpdateCreditNoteRequest represents the request payload for updating a credit note
type UpdateCreditNoteRequest struct {
	CustomerID       *string    `json:"customerId,omitempty"`
	InvoiceID        *string    `json:"invoiceId,omitempty"`
	CreditNoteNumber *string    `json:"creditNoteNumber,omitempty"`
	Type             *string    `json:"type,omitempty" validate:"omitempty,oneof=CustomerRefund SupplierRefund WriteOff ReturnCredit Adjustment"`
	IssueDate        *time.Time `json:"issueDate,omitempty"`
	Reason           *string    `json:"reason,omitempty" validate:"omitempty,min=1,max=500"`
	Notes            *string    `json:"notes,omitempty" validate:"omitempty,max=1000"`
	Status           *string    `json:"status,omitempty" validate:"omitempty,oneof=Draft Open Closed Void"`
}

// UpdateCreditNoteStatusRequest represents the request payload for updating credit note status
type UpdateCreditNoteStatusRequest struct {
	Status string `json:"status" binding:"required" validate:"oneof=Draft Open Closed Void"`
}

// ApplyCreditRequest represents the request payload for applying credit to an invoice
type ApplyCreditRequest struct {
	InvoiceID string          `json:"invoiceId" binding:"required"`
	Amount    decimal.Decimal `json:"amount" binding:"required" validate:"gt=0"`
}

// CreditNoteItemResponse represents the response payload for credit note items
type CreditNoteItemResponse struct {
	ID          string          `json:"id"`
	Description string          `json:"description"`
	Quantity    string          `json:"quantity"`
	UnitPrice   string          `json:"unitPrice"`
	TotalPrice  string          `json:"totalPrice"`
	TaxAmount   string          `json:"taxAmount"`
	SortOrder   int             `json:"sortOrder"`
	TaxRate     *TaxRateSummary `json:"taxRate,omitempty"`
}

// TaxRateSummary represents a summary of tax rate information
type TaxRateSummary struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Rate        string  `json:"rate"`
	Description *string `json:"description,omitempty"`
	IsActive    bool    `json:"isActive"`
}

// CreditApplicationResponse represents the response payload for credit applications
type CreditApplicationResponse struct {
	ID          string          `json:"id"`
	InvoiceID   string          `json:"invoiceId"`
	Amount      string          `json:"amount"`
	AppliedDate time.Time       `json:"appliedDate"`
	Invoice     *InvoiceSummary `json:"invoice,omitempty"`
}

// InvoiceSummary represents a summary of invoice information
type InvoiceSummary struct {
	ID            string    `json:"id"`
	InvoiceNumber string    `json:"invoiceNumber"`
	TotalAmount   string    `json:"totalAmount"`
	Status        string    `json:"status"`
	IssueDate     time.Time `json:"issueDate"`
}

// CreditNoteResponse represents the response payload for credit note operations
type CreditNoteResponse struct {
	ID               string                      `json:"id"`
	MerchantID       string                      `json:"merchantId"`
	CustomerID       *string                     `json:"customerId,omitempty"`
	InvoiceID        *string                     `json:"invoiceId,omitempty"`
	CreditNoteNumber string                      `json:"creditNoteNumber"`
	Status           string                      `json:"status"`
	Type             string                      `json:"type"`
	IssueDate        time.Time                   `json:"issueDate"`
	Reason           string                      `json:"reason"`
	Notes            *string                     `json:"notes,omitempty"`
	Amount           string                      `json:"amount"`
	TaxAmount        string                      `json:"taxAmount"`
	TotalAmount      string                      `json:"totalAmount"`
	AppliedAmount    string                      `json:"appliedAmount"`
	BalanceAmount    string                      `json:"balanceAmount"`
	Currency         string                      `json:"currency"`
	ExchangeRate     string                      `json:"exchangeRate"`
	CreatedAt        time.Time                   `json:"createdAt"`
	UpdatedAt        time.Time                   `json:"updatedAt"`
	Merchant         *MerchantSummary            `json:"merchant,omitempty"`
	Customer         *CustomerSummary            `json:"customer,omitempty"`
	Invoice          *InvoiceSummary             `json:"invoice,omitempty"`
	Items            []CreditNoteItemResponse    `json:"items"`
	Applications     []CreditApplicationResponse `json:"applications"`
}

// CreditNoteListResponse represents the response for listing credit notes
type CreditNoteListResponse struct {
	CreditNotes []CreditNoteResponse `json:"creditNotes"`
	Pagination  PaginationResponse   `json:"pagination"`
	Summary     *CreditNoteSummary   `json:"summary,omitempty"`
}

// CreditNoteSummary represents summary statistics for credit notes
type CreditNoteSummary struct {
	TotalCreditNotes  int64  `json:"totalCreditNotes"`
	DraftCreditNotes  int64  `json:"draftCreditNotes"`
	OpenCreditNotes   int64  `json:"openCreditNotes"`
	ClosedCreditNotes int64  `json:"closedCreditNotes"`
	VoidCreditNotes   int64  `json:"voidCreditNotes"`
	TotalAmount       string `json:"totalAmount"`
	AppliedAmount     string `json:"appliedAmount"`
	BalanceAmount     string `json:"balanceAmount"`
}

// CreditNoteFilterRequest represents filter parameters for credit notes
type CreditNoteFilterRequest struct {
	Page      int        `json:"page" form:"page"`
	Limit     int        `json:"limit" form:"limit"`
	Search    string     `json:"search" form:"search"`
	Status    string     `json:"status" form:"status"`
	Type      string     `json:"type" form:"type"`
	StartDate *time.Time `json:"startDate" form:"startDate"`
	EndDate   *time.Time `json:"endDate" form:"endDate"`
}

// GenerateCreditNoteNumberResponse represents the response for generating credit note numbers
type GenerateCreditNoteNumberResponse struct {
	CreditNoteNumber string `json:"creditNoteNumber"`
}

// ToCreditNoteResponse converts a models.CreditNote to CreditNoteResponse
func ToCreditNoteResponse(creditNote *models.CreditNote) *CreditNoteResponse {
	response := &CreditNoteResponse{
		ID:               creditNote.ID,
		MerchantID:       creditNote.BranchID, // TODO: Update response struct to use BranchID
		CustomerID:       creditNote.CustomerID,
		InvoiceID:        creditNote.InvoiceID,
		CreditNoteNumber: creditNote.CreditNoteNumber,
		Status:           string(creditNote.Status),
		Type:             string(creditNote.Type),
		IssueDate:        creditNote.IssueDate,
		Reason:           creditNote.Reason,
		Notes:            creditNote.Notes,
		Amount:           creditNote.Amount.String(),
		TaxAmount:        creditNote.TaxAmount.String(),
		TotalAmount:      creditNote.TotalAmount.String(),
		AppliedAmount:    creditNote.AppliedAmount.String(),
		BalanceAmount:    creditNote.BalanceAmount.String(),
		Currency:         creditNote.Currency,
		ExchangeRate:     creditNote.ExchangeRate.String(),
		CreatedAt:        creditNote.CreatedAt,
		UpdatedAt:        creditNote.UpdatedAt,
	}

	// Add merchant information if loaded
	if creditNote.Branch.ID != "" {
		response.Merchant = &MerchantSummary{
			ID:   creditNote.Branch.ID,
			Name: creditNote.Branch.Name,
		}
	}

	// Add customer information if loaded
	if creditNote.Customer != nil && creditNote.Customer.ID != "" {
		response.Customer = &CustomerSummary{
			ID:    creditNote.Customer.ID,
			Name:  creditNote.Customer.Name,
			Email: creditNote.Customer.Email,
			Phone: creditNote.Customer.Phone,
		}
	}

	// Add invoice information if loaded
	if creditNote.Invoice != nil && creditNote.Invoice.ID != "" {
		response.Invoice = &InvoiceSummary{
			ID:            creditNote.Invoice.ID,
			InvoiceNumber: creditNote.Invoice.InvoiceNumber,
			TotalAmount:   creditNote.Invoice.TotalAmount.String(),
			Status:        string(creditNote.Invoice.Status),
			IssueDate:     creditNote.Invoice.IssueDate,
		}
	}

	// Add items
	items := make([]CreditNoteItemResponse, len(creditNote.Items))
	for i, item := range creditNote.Items {
		itemResponse := CreditNoteItemResponse{
			ID:          item.ID,
			Description: item.Description,
			Quantity:    item.Quantity.String(),
			UnitPrice:   item.UnitPrice.String(),
			TotalPrice:  item.TotalPrice.String(),
			TaxAmount:   item.TaxAmount.String(),
			SortOrder:   item.SortOrder,
		}

		// Add tax rate information if loaded
		if item.TaxRate != nil && item.TaxRate.ID != "" {
			itemResponse.TaxRate = &TaxRateSummary{
				ID:          item.TaxRate.ID,
				Name:        item.TaxRate.Name,
				Rate:        item.TaxRate.Rate.String(),
				Description: item.TaxRate.Description,
				IsActive:    item.TaxRate.IsActive,
			}
		}

		items[i] = itemResponse
	}
	response.Items = items

	// Add applications
	applications := make([]CreditApplicationResponse, len(creditNote.Applications))
	for i, app := range creditNote.Applications {
		appResponse := CreditApplicationResponse{
			ID:          app.ID,
			InvoiceID:   app.InvoiceID,
			Amount:      app.Amount.String(),
			AppliedDate: app.AppliedDate,
		}

		// Add invoice information if loaded
		if app.Invoice.ID != "" {
			appResponse.Invoice = &InvoiceSummary{
				ID:            app.Invoice.ID,
				InvoiceNumber: app.Invoice.InvoiceNumber,
				TotalAmount:   app.Invoice.TotalAmount.String(),
				Status:        string(app.Invoice.Status),
				IssueDate:     app.Invoice.IssueDate,
			}
		}

		applications[i] = appResponse
	}
	response.Applications = applications

	return response
}

// ToCreditNoteListResponse converts a slice of models.CreditNote to CreditNoteListResponse
func ToCreditNoteListResponse(creditNotes []models.CreditNote, total int64, page, limit int, includeSummary bool) *CreditNoteListResponse {
	responses := make([]CreditNoteResponse, len(creditNotes))
	for i, creditNote := range creditNotes {
		responses[i] = *ToCreditNoteResponse(&creditNote)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	response := &CreditNoteListResponse{
		CreditNotes: responses,
		Pagination: PaginationResponse{
			Total:           total,
			Page:            page,
			Limit:           limit,
			TotalPages:      totalPages,
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
		},
	}

	// Add summary if requested
	if includeSummary {
		response.Summary = calculateCreditNoteSummary(creditNotes)
	}

	return response
}

// calculateCreditNoteSummary calculates summary statistics for credit notes
func calculateCreditNoteSummary(creditNotes []models.CreditNote) *CreditNoteSummary {
	summary := &CreditNoteSummary{
		TotalCreditNotes: int64(len(creditNotes)),
	}

	var totalAmount, appliedAmount, balanceAmount decimal.Decimal

	for _, creditNote := range creditNotes {
		switch creditNote.Status {
		case models.CreditNoteStatusDraft:
			summary.DraftCreditNotes++
		case models.CreditNoteStatusOpen:
			summary.OpenCreditNotes++
		case models.CreditNoteStatusClosed:
			summary.ClosedCreditNotes++
		case models.CreditNoteStatusVoid:
			summary.VoidCreditNotes++
		}

		totalAmount = totalAmount.Add(creditNote.TotalAmount)
		appliedAmount = appliedAmount.Add(creditNote.AppliedAmount)
		balanceAmount = balanceAmount.Add(creditNote.BalanceAmount)
	}

	summary.TotalAmount = totalAmount.String()
	summary.AppliedAmount = appliedAmount.String()
	summary.BalanceAmount = balanceAmount.String()

	return summary
}

// ToCreateCreditNoteModel converts CreateCreditNoteRequest to models.CreditNote and items
func (req *CreateCreditNoteRequest) ToCreateCreditNoteModel(branchID string) (*models.CreditNote, []models.CreditNoteItem, error) {
	creditNote := &models.CreditNote{
		BranchID:   branchID, // TODO: Update parameter to use branchID
		CustomerID: req.CustomerID,
		InvoiceID:  req.InvoiceID,
		Reason:     req.Reason,
		Notes:      req.Notes,
		IssueDate:  req.IssueDate,
		Status:     models.CreditNoteStatusDraft, // Default status
	}

	// Set credit note number if provided
	if req.CreditNoteNumber != nil {
		creditNote.CreditNoteNumber = *req.CreditNoteNumber
	}

	// Set type
	switch req.Type {
	case "CustomerRefund":
		creditNote.Type = models.CreditNoteTypeCustomerRefund
	case "SupplierRefund":
		creditNote.Type = models.CreditNoteTypeSupplierRefund
	case "WriteOff":
		creditNote.Type = models.CreditNoteTypeWriteOff
	case "ReturnCredit":
		creditNote.Type = models.CreditNoteTypeReturnCredit
	case "Adjustment":
		creditNote.Type = models.CreditNoteTypeAdjustment
	default:
		return nil, nil, fmt.Errorf("invalid credit note type: %s", req.Type)
	}

	// Set status if provided
	if req.Status != nil {
		switch *req.Status {
		case "Draft":
			creditNote.Status = models.CreditNoteStatusDraft
		case "Open":
			creditNote.Status = models.CreditNoteStatusOpen
		case "Closed":
			creditNote.Status = models.CreditNoteStatusClosed
		case "Void":
			creditNote.Status = models.CreditNoteStatusVoid
		default:
			creditNote.Status = models.CreditNoteStatusDraft
		}
	}

	// Convert items
	items := make([]models.CreditNoteItem, len(req.Items))
	for i, itemReq := range req.Items {
		items[i] = models.CreditNoteItem{
			Description: itemReq.Description,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			TaxRateID:   itemReq.TaxRateID,
			TaxAmount:   itemReq.TaxAmount,
			SortOrder:   i + 1, // Default sort order
		}

		// Set sort order if provided
		if itemReq.SortOrder != nil {
			items[i].SortOrder = *itemReq.SortOrder
		}
	}

	return creditNote, items, nil
}

// ToUpdateCreditNoteModel converts UpdateCreditNoteRequest to models.CreditNote
func (req *UpdateCreditNoteRequest) ToUpdateCreditNoteModel() *models.CreditNote {
	creditNote := &models.CreditNote{}

	if req.CustomerID != nil {
		creditNote.CustomerID = req.CustomerID
	}
	if req.InvoiceID != nil {
		creditNote.InvoiceID = req.InvoiceID
	}
	if req.CreditNoteNumber != nil {
		creditNote.CreditNoteNumber = *req.CreditNoteNumber
	}
	if req.IssueDate != nil {
		creditNote.IssueDate = *req.IssueDate
	}
	if req.Reason != nil {
		creditNote.Reason = *req.Reason
	}
	if req.Notes != nil {
		creditNote.Notes = req.Notes
	}
	if req.Type != nil {
		switch *req.Type {
		case "CustomerRefund":
			creditNote.Type = models.CreditNoteTypeCustomerRefund
		case "SupplierRefund":
			creditNote.Type = models.CreditNoteTypeSupplierRefund
		case "WriteOff":
			creditNote.Type = models.CreditNoteTypeWriteOff
		case "ReturnCredit":
			creditNote.Type = models.CreditNoteTypeReturnCredit
		case "Adjustment":
			creditNote.Type = models.CreditNoteTypeAdjustment
		}
	}
	if req.Status != nil {
		switch *req.Status {
		case "Draft":
			creditNote.Status = models.CreditNoteStatusDraft
		case "Open":
			creditNote.Status = models.CreditNoteStatusOpen
		case "Closed":
			creditNote.Status = models.CreditNoteStatusClosed
		case "Void":
			creditNote.Status = models.CreditNoteStatusVoid
		}
	}

	return creditNote
}

// ToCreditNoteSummaryResponse converts service CreditNoteSummary to DTO
func ToCreditNoteSummaryResponse(summary *services.CreditNoteSummary) *CreditNoteSummary {
	return &CreditNoteSummary{
		TotalCreditNotes:  summary.TotalCreditNotes,
		DraftCreditNotes:  summary.DraftCreditNotes,
		OpenCreditNotes:   summary.OpenCreditNotes,
		ClosedCreditNotes: summary.ClosedCreditNotes,
		VoidCreditNotes:   summary.VoidCreditNotes,
		TotalAmount:       summary.TotalAmount,
		AppliedAmount:     summary.AppliedAmount,
		BalanceAmount:     summary.BalanceAmount,
	}
}

// GetValidStatuses returns all valid credit note statuses
func GetValidCreditNoteStatuses() []string {
	return []string{"Draft", "Open", "Closed", "Void"}
}

// GetValidTypes returns all valid credit note types
func GetValidCreditNoteTypes() []string {
	return []string{"CustomerRefund", "SupplierRefund", "WriteOff", "ReturnCredit", "Adjustment"}
}

// ValidateCreditNoteStatus validates if a credit note status is valid
func ValidateCreditNoteStatus(status string) error {
	validStatuses := GetValidCreditNoteStatuses()
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}
	return fmt.Errorf("invalid credit note status: %s. Valid statuses are: %v", status, validStatuses)
}

// ValidateCreditNoteType validates if a credit note type is valid
func ValidateCreditNoteType(noteType string) error {
	validTypes := GetValidCreditNoteTypes()
	for _, validType := range validTypes {
		if noteType == validType {
			return nil
		}
	}
	return fmt.Errorf("invalid credit note type: %s. Valid types are: %v", noteType, validTypes)
}

// FormatCreditNoteStatus formats a credit note status for display
func FormatCreditNoteStatus(status models.CreditNoteStatus) string {
	switch status {
	case models.CreditNoteStatusDraft:
		return "Draft"
	case models.CreditNoteStatusOpen:
		return "Open"
	case models.CreditNoteStatusClosed:
		return "Closed"
	case models.CreditNoteStatusVoid:
		return "Void"
	default:
		return string(status)
	}
}

// FormatCreditNoteType formats a credit note type for display
func FormatCreditNoteType(noteType models.CreditNoteType) string {
	switch noteType {
	case models.CreditNoteTypeCustomerRefund:
		return "Customer Refund"
	case models.CreditNoteTypeSupplierRefund:
		return "Supplier Refund"
	case models.CreditNoteTypeWriteOff:
		return "Write-Off"
	case models.CreditNoteTypeReturnCredit:
		return "Return Credit"
	case models.CreditNoteTypeAdjustment:
		return "Adjustment"
	default:
		return string(noteType)
	}
}

// GetCreditNoteStatusColor returns a color code for credit note status (for UI purposes)
func GetCreditNoteStatusColor(status models.CreditNoteStatus) string {
	switch status {
	case models.CreditNoteStatusDraft:
		return "gray"
	case models.CreditNoteStatusOpen:
		return "blue"
	case models.CreditNoteStatusClosed:
		return "green"
	case models.CreditNoteStatusVoid:
		return "red"
	default:
		return "gray"
	}
}

// IsCreditNoteStatusFinal checks if a credit note status is final (cannot be changed)
func IsCreditNoteStatusFinal(status models.CreditNoteStatus) bool {
	return status == models.CreditNoteStatusClosed || status == models.CreditNoteStatusVoid
}
