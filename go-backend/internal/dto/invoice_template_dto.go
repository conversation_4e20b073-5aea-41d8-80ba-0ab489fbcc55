package dto

import (
	"fmt"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/shopspring/decimal"
)

// CreateInvoiceTemplateRequest represents the request payload for creating an invoice template
type CreateInvoiceTemplateRequest struct {
	Name        string                             `json:"name" binding:"required" validate:"min=1,max=255"`
	Description *string                            `json:"description,omitempty" validate:"omitempty,max=1000"`
	Terms       *string                            `json:"terms,omitempty" validate:"omitempty,max=1000"`
	Notes       *string                            `json:"notes,omitempty" validate:"omitempty,max=1000"`
	IsDefault   *bool                              `json:"isDefault,omitempty"`
	IsActive    *bool                              `json:"isActive,omitempty"`
	Items       []CreateInvoiceTemplateItemRequest `json:"items" binding:"required,min=1"`
}

// CreateInvoiceTemplateItemRequest represents the request payload for creating an invoice template item
type CreateInvoiceTemplateItemRequest struct {
	Description string          `json:"description" binding:"required" validate:"min=1,max=500"`
	Quantity    decimal.Decimal `json:"quantity" binding:"required" validate:"gt=0"`
	UnitPrice   decimal.Decimal `json:"unitPrice" binding:"required" validate:"gte=0"`
	TaxRateID   *string         `json:"taxRateId,omitempty"`
	SortOrder   *int            `json:"sortOrder,omitempty"`
}

// UpdateInvoiceTemplateRequest represents the request payload for updating an invoice template
type UpdateInvoiceTemplateRequest struct {
	Name        *string `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Description *string `json:"description,omitempty" validate:"omitempty,max=1000"`
	Terms       *string `json:"terms,omitempty" validate:"omitempty,max=1000"`
	Notes       *string `json:"notes,omitempty" validate:"omitempty,max=1000"`
	IsDefault   *bool   `json:"isDefault,omitempty"`
	IsActive    *bool   `json:"isActive,omitempty"`
}

// UpdateInvoiceTemplateItemsRequest represents the request payload for updating template items
type UpdateInvoiceTemplateItemsRequest struct {
	Items []CreateInvoiceTemplateItemRequest `json:"items" binding:"required"`
}

// CloneInvoiceTemplateRequest represents the request payload for cloning a template
type CloneInvoiceTemplateRequest struct {
	Name string `json:"name" binding:"required" validate:"min=1,max=255"`
}

// InvoiceTemplateItemResponse represents the response payload for invoice template items
type InvoiceTemplateItemResponse struct {
	ID          string          `json:"id"`
	Description string          `json:"description"`
	Quantity    string          `json:"quantity"`
	UnitPrice   string          `json:"unitPrice"`
	SortOrder   int             `json:"sortOrder"`
	TaxRate     *TaxRateSummary `json:"taxRate,omitempty"`
}

// InvoiceTemplateResponse represents the response payload for invoice template operations
type InvoiceTemplateResponse struct {
	ID          string                        `json:"id"`
	MerchantID  string                        `json:"merchantId"`
	Name        string                        `json:"name"`
	Description *string                       `json:"description,omitempty"`
	Terms       *string                       `json:"terms,omitempty"`
	Notes       *string                       `json:"notes,omitempty"`
	IsDefault   bool                          `json:"isDefault"`
	IsActive    bool                          `json:"isActive"`
	CreatedAt   string                        `json:"createdAt"`
	UpdatedAt   string                        `json:"updatedAt"`
	Merchant    *MerchantSummary              `json:"merchant,omitempty"`
	Items       []InvoiceTemplateItemResponse `json:"items"`
}

// InvoiceTemplateListResponse represents the response for listing invoice templates
type InvoiceTemplateListResponse struct {
	Templates  []InvoiceTemplateResponse `json:"templates"`
	Pagination PaginationResponse        `json:"pagination"`
	Summary    *InvoiceTemplateSummary   `json:"summary,omitempty"`
}

// InvoiceTemplateSummary represents summary statistics for invoice templates
type InvoiceTemplateSummary struct {
	TotalTemplates    int64 `json:"totalTemplates"`
	ActiveTemplates   int64 `json:"activeTemplates"`
	InactiveTemplates int64 `json:"inactiveTemplates"`
	DefaultTemplates  int64 `json:"defaultTemplates"`
}

// InvoiceTemplateFilterRequest represents filter parameters for invoice templates
type InvoiceTemplateFilterRequest struct {
	Page       int    `json:"page" form:"page"`
	Limit      int    `json:"limit" form:"limit"`
	Search     string `json:"search" form:"search"`
	ActiveOnly bool   `json:"activeOnly" form:"activeOnly"`
}

// ToInvoiceTemplateResponse converts a models.InvoiceTemplate to InvoiceTemplateResponse
func ToInvoiceTemplateResponse(template *models.InvoiceTemplate) *InvoiceTemplateResponse {
	response := &InvoiceTemplateResponse{
		ID:          template.ID,
		MerchantID:  template.BranchID, // TODO: Update response struct to use BranchID
		Name:        template.Name,
		Description: template.Description,
		Terms:       template.Terms,
		Notes:       template.Notes,
		IsDefault:   template.IsDefault,
		IsActive:    template.IsActive,
		CreatedAt:   template.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   template.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Add merchant information if loaded
	if template.Branch.ID != "" {
		response.Merchant = &MerchantSummary{
			ID:   template.Branch.ID,
			Name: template.Branch.Name,
		}
	}

	// Add items
	items := make([]InvoiceTemplateItemResponse, len(template.Items))
	for i, item := range template.Items {
		itemResponse := InvoiceTemplateItemResponse{
			ID:          item.ID,
			Description: item.Description,
			Quantity:    item.Quantity.String(),
			UnitPrice:   item.UnitPrice.String(),
			SortOrder:   item.SortOrder,
		}

		// Add tax rate information if loaded
		if item.TaxRate != nil && item.TaxRate.ID != "" {
			itemResponse.TaxRate = &TaxRateSummary{
				ID:          item.TaxRate.ID,
				Name:        item.TaxRate.Name,
				Rate:        item.TaxRate.Rate.String(),
				Description: item.TaxRate.Description,
				IsActive:    item.TaxRate.IsActive,
			}
		}

		items[i] = itemResponse
	}
	response.Items = items

	return response
}

// ToInvoiceTemplateListResponse converts a slice of models.InvoiceTemplate to InvoiceTemplateListResponse
func ToInvoiceTemplateListResponse(templates []models.InvoiceTemplate, total int64, page, limit int, includeSummary bool) *InvoiceTemplateListResponse {
	responses := make([]InvoiceTemplateResponse, len(templates))
	for i, template := range templates {
		responses[i] = *ToInvoiceTemplateResponse(&template)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	response := &InvoiceTemplateListResponse{
		Templates: responses,
		Pagination: PaginationResponse{
			Total:           total,
			Page:            page,
			Limit:           limit,
			TotalPages:      totalPages,
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
		},
	}

	// Add summary if requested
	if includeSummary {
		response.Summary = calculateInvoiceTemplateSummary(templates)
	}

	return response
}

// calculateInvoiceTemplateSummary calculates summary statistics for invoice templates
func calculateInvoiceTemplateSummary(templates []models.InvoiceTemplate) *InvoiceTemplateSummary {
	summary := &InvoiceTemplateSummary{
		TotalTemplates: int64(len(templates)),
	}

	for _, template := range templates {
		if template.IsActive {
			summary.ActiveTemplates++
		} else {
			summary.InactiveTemplates++
		}

		if template.IsDefault {
			summary.DefaultTemplates++
		}
	}

	return summary
}

// ToCreateInvoiceTemplateModel converts CreateInvoiceTemplateRequest to models.InvoiceTemplate and items
func (req *CreateInvoiceTemplateRequest) ToCreateInvoiceTemplateModel(branchID string) (*models.InvoiceTemplate, []models.InvoiceTemplateItem, error) {
	template := &models.InvoiceTemplate{
		BranchID:    branchID, // TODO: Update parameter to use branchID
		Name:        req.Name,
		Description: req.Description,
		Terms:       req.Terms,
		Notes:       req.Notes,
		IsDefault:   false, // Default to false
		IsActive:    true,  // Default to true
	}

	// Set IsDefault if provided
	if req.IsDefault != nil {
		template.IsDefault = *req.IsDefault
	}

	// Set IsActive if provided
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}

	// Convert items
	items := make([]models.InvoiceTemplateItem, len(req.Items))
	for i, itemReq := range req.Items {
		items[i] = models.InvoiceTemplateItem{
			Description: itemReq.Description,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			TaxRateID:   itemReq.TaxRateID,
			SortOrder:   i + 1, // Default sort order
		}

		// Set sort order if provided
		if itemReq.SortOrder != nil {
			items[i].SortOrder = *itemReq.SortOrder
		}
	}

	return template, items, nil
}

// ToUpdateInvoiceTemplateModel converts UpdateInvoiceTemplateRequest to models.InvoiceTemplate
func (req *UpdateInvoiceTemplateRequest) ToUpdateInvoiceTemplateModel() *models.InvoiceTemplate {
	template := &models.InvoiceTemplate{}

	if req.Name != nil {
		template.Name = *req.Name
	}
	if req.Description != nil {
		template.Description = req.Description
	}
	if req.Terms != nil {
		template.Terms = req.Terms
	}
	if req.Notes != nil {
		template.Notes = req.Notes
	}
	if req.IsDefault != nil {
		template.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	}

	return template
}

// ToInvoiceTemplateItemsModel converts CreateInvoiceTemplateItemRequest slice to models.InvoiceTemplateItem slice
func ToInvoiceTemplateItemsModel(items []CreateInvoiceTemplateItemRequest) []models.InvoiceTemplateItem {
	result := make([]models.InvoiceTemplateItem, len(items))
	for i, itemReq := range items {
		result[i] = models.InvoiceTemplateItem{
			Description: itemReq.Description,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			TaxRateID:   itemReq.TaxRateID,
			SortOrder:   i + 1, // Default sort order
		}

		// Set sort order if provided
		if itemReq.SortOrder != nil {
			result[i].SortOrder = *itemReq.SortOrder
		}
	}
	return result
}

// ToInvoiceTemplateSummaryResponse converts service InvoiceTemplateSummary to DTO
func ToInvoiceTemplateSummaryResponse(summary *services.InvoiceTemplateSummary) *InvoiceTemplateSummary {
	return &InvoiceTemplateSummary{
		TotalTemplates:    summary.TotalTemplates,
		ActiveTemplates:   summary.ActiveTemplates,
		InactiveTemplates: summary.InactiveTemplates,
		DefaultTemplates:  summary.DefaultTemplates,
	}
}

// ValidateInvoiceTemplate validates invoice template data
func ValidateInvoiceTemplate(template *CreateInvoiceTemplateRequest) error {
	if template.Name == "" {
		return fmt.Errorf("template name is required")
	}
	if len(template.Name) > 255 {
		return fmt.Errorf("template name cannot exceed 255 characters")
	}
	if len(template.Items) == 0 {
		return fmt.Errorf("at least one template item is required")
	}

	// Validate each item
	for i, item := range template.Items {
		if err := ValidateInvoiceTemplateItem(&item, i); err != nil {
			return fmt.Errorf("item %d: %w", i+1, err)
		}
	}

	return nil
}

// ValidateInvoiceTemplateItem validates invoice template item data
func ValidateInvoiceTemplateItem(item *CreateInvoiceTemplateItemRequest, index int) error {
	if item.Description == "" {
		return fmt.Errorf("item description is required")
	}
	if len(item.Description) > 500 {
		return fmt.Errorf("item description cannot exceed 500 characters")
	}
	if item.Quantity.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("item quantity must be greater than zero")
	}
	if item.UnitPrice.LessThan(decimal.Zero) {
		return fmt.Errorf("item unit price cannot be negative")
	}

	return nil
}

// GetInvoiceTemplateStatusText returns a human-readable status text
func GetInvoiceTemplateStatusText(isActive, isDefault bool) string {
	if isDefault {
		return "Default"
	}
	if isActive {
		return "Active"
	}
	return "Inactive"
}

// GetInvoiceTemplateStatusColor returns a color code for template status (for UI purposes)
func GetInvoiceTemplateStatusColor(isActive, isDefault bool) string {
	if isDefault {
		return "green"
	}
	if isActive {
		return "blue"
	}
	return "gray"
}
