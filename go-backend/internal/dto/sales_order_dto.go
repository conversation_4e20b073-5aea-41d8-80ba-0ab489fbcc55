package dto

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"
)

// CreateSalesOrderRequest represents the request payload for creating a sales order
type CreateSalesOrderRequest struct {
	CustomerID   string     `json:"customerId" binding:"required"`
	OrderNumber  *string    `json:"orderNumber,omitempty"` // Optional, will be auto-generated if not provided
	OrderDate    time.Time  `json:"orderDate" binding:"required"`
	DeliveryDate *time.Time `json:"deliveryDate,omitempty"`
	Status       *string    `json:"status,omitempty" validate:"omitempty,oneof=Draft Confirmed Processing Completed Cancelled"`
	Notes        *string    `json:"notes,omitempty" validate:"omitempty,max=1000"`
}

// UpdateSalesOrderRequest represents the request payload for updating a sales order
type UpdateSalesOrderRequest struct {
	CustomerID   *string    `json:"customerId,omitempty"`
	OrderNumber  *string    `json:"orderNumber,omitempty"`
	OrderDate    *time.Time `json:"orderDate,omitempty"`
	DeliveryDate *time.Time `json:"deliveryDate,omitempty"`
	Status       *string    `json:"status,omitempty" validate:"omitempty,oneof=Draft Confirmed Processing Completed Cancelled"`
	Notes        *string    `json:"notes,omitempty" validate:"omitempty,max=1000"`
}

// UpdateSalesOrderStatusRequest represents the request payload for updating sales order status
type UpdateSalesOrderStatusRequest struct {
	Status string `json:"status" binding:"required" validate:"oneof=Draft Confirmed Processing Completed Cancelled"`
}

// SalesOrderResponse represents the response payload for sales order operations
type SalesOrderResponse struct {
	ID           string           `json:"id"`
	MerchantID   string           `json:"merchantId"`
	CustomerID   string           `json:"customerId"`
	OrderNumber  string           `json:"orderNumber"`
	OrderDate    time.Time        `json:"orderDate"`
	DeliveryDate *time.Time       `json:"deliveryDate,omitempty"`
	Status       string           `json:"status"`
	Notes        *string          `json:"notes,omitempty"`
	CreatedAt    time.Time        `json:"createdAt"`
	UpdatedAt    time.Time        `json:"updatedAt"`
	Merchant     *MerchantSummary `json:"merchant,omitempty"`
	Customer     *CustomerSummary `json:"customer,omitempty"`
}

// CustomerSummary represents a summary of customer information
type CustomerSummary struct {
	ID    string  `json:"id"`
	Name  string  `json:"name"`
	Email *string `json:"email,omitempty"`
	Phone *string `json:"phone,omitempty"`
}

// SalesOrderListResponse represents the response for listing sales orders
type SalesOrderListResponse struct {
	SalesOrders []SalesOrderResponse `json:"salesOrders"`
	Pagination  PaginationResponse   `json:"pagination"`
	Summary     *SalesOrderSummary   `json:"summary,omitempty"`
}

// SalesOrderSummary represents summary statistics for sales orders
type SalesOrderSummary struct {
	TotalOrders      int64 `json:"totalOrders"`
	DraftOrders      int64 `json:"draftOrders"`
	ConfirmedOrders  int64 `json:"confirmedOrders"`
	ProcessingOrders int64 `json:"processingOrders"`
	CompletedOrders  int64 `json:"completedOrders"`
	CancelledOrders  int64 `json:"cancelledOrders"`
}

// SalesOrderFilterRequest represents filter parameters for sales orders
type SalesOrderFilterRequest struct {
	Page      int        `json:"page" form:"page"`
	Limit     int        `json:"limit" form:"limit"`
	Search    string     `json:"search" form:"search"`
	Status    string     `json:"status" form:"status"`
	StartDate *time.Time `json:"startDate" form:"startDate"`
	EndDate   *time.Time `json:"endDate" form:"endDate"`
}

// GenerateOrderNumberResponse represents the response for generating order numbers
type GenerateOrderNumberResponse struct {
	OrderNumber string `json:"orderNumber"`
}

// ToSalesOrderResponse converts a models.SalesOrder to SalesOrderResponse
func ToSalesOrderResponse(order *models.SalesOrder) *SalesOrderResponse {
	response := &SalesOrderResponse{
		ID:           order.ID,
		MerchantID:   order.BranchID, // TODO: Update response struct to use BranchID
		CustomerID:   order.CustomerID,
		OrderNumber:  order.OrderNumber,
		OrderDate:    order.OrderDate,
		DeliveryDate: order.DeliveryDate,
		Status:       order.Status,
		Notes:        order.Notes,
		CreatedAt:    order.CreatedAt,
		UpdatedAt:    order.UpdatedAt,
	}

	// Add merchant information if loaded
	if order.Branch.ID != "" {
		response.Merchant = &MerchantSummary{
			ID:   order.Branch.ID,
			Name: order.Branch.Name,
		}
	}

	// Add customer information if loaded
	if order.Customer.ID != "" {
		response.Customer = &CustomerSummary{
			ID:    order.Customer.ID,
			Name:  order.Customer.Name,
			Email: order.Customer.Email,
			Phone: order.Customer.Phone,
		}
	}

	return response
}

// ToSalesOrderListResponse converts a slice of models.SalesOrder to SalesOrderListResponse
func ToSalesOrderListResponse(orders []models.SalesOrder, total int64, page, limit int, includeSummary bool) *SalesOrderListResponse {
	responses := make([]SalesOrderResponse, len(orders))
	for i, order := range orders {
		responses[i] = *ToSalesOrderResponse(&order)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	response := &SalesOrderListResponse{
		SalesOrders: responses,
		Pagination: PaginationResponse{
			Total:           total,
			Page:            page,
			Limit:           limit,
			TotalPages:      totalPages,
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
		},
	}

	// Add summary if requested
	if includeSummary {
		response.Summary = calculateSalesOrderSummary(orders)
	}

	return response
}

// calculateSalesOrderSummary calculates summary statistics for sales orders
func calculateSalesOrderSummary(orders []models.SalesOrder) *SalesOrderSummary {
	summary := &SalesOrderSummary{
		TotalOrders: int64(len(orders)),
	}

	for _, order := range orders {
		switch order.Status {
		case "Draft":
			summary.DraftOrders++
		case "Confirmed":
			summary.ConfirmedOrders++
		case "Processing":
			summary.ProcessingOrders++
		case "Completed":
			summary.CompletedOrders++
		case "Cancelled":
			summary.CancelledOrders++
		}
	}

	return summary
}

// ToCreateSalesOrderModel converts CreateSalesOrderRequest to models.SalesOrder
func (req *CreateSalesOrderRequest) ToCreateSalesOrderModel(branchID string) (*models.SalesOrder, error) {
	order := &models.SalesOrder{
		BranchID:     branchID, // TODO: Update parameter to use branchID
		CustomerID:   req.CustomerID,
		OrderDate:    req.OrderDate,
		DeliveryDate: req.DeliveryDate,
		Notes:        req.Notes,
		Status:       "Draft", // Default status
	}

	// Set order number if provided
	if req.OrderNumber != nil {
		order.OrderNumber = *req.OrderNumber
	}

	// Set status if provided
	if req.Status != nil {
		order.Status = *req.Status
	}

	return order, nil
}

// ToUpdateSalesOrderModel converts UpdateSalesOrderRequest to models.SalesOrder
func (req *UpdateSalesOrderRequest) ToUpdateSalesOrderModel() *models.SalesOrder {
	order := &models.SalesOrder{}

	if req.CustomerID != nil {
		order.CustomerID = *req.CustomerID
	}
	if req.OrderNumber != nil {
		order.OrderNumber = *req.OrderNumber
	}
	if req.OrderDate != nil {
		order.OrderDate = *req.OrderDate
	}
	if req.DeliveryDate != nil {
		order.DeliveryDate = req.DeliveryDate
	}
	if req.Status != nil {
		order.Status = *req.Status
	}
	if req.Notes != nil {
		order.Notes = req.Notes
	}

	return order
}

// ToSalesOrderSummaryResponse converts service SalesOrderSummary to DTO
func ToSalesOrderSummaryResponse(summary *SalesOrderSummary) *SalesOrderSummary {
	// Since both service and DTO use the same structure, we can return it directly
	return summary
}

// ValidateStatusTransition validates if a status transition is allowed
func ValidateStatusTransition(currentStatus, newStatus string) error {
	validTransitions := map[string][]string{
		"Draft":      {"Confirmed", "Cancelled"},
		"Confirmed":  {"Processing", "Cancelled"},
		"Processing": {"Completed", "Cancelled"},
		"Completed":  {}, // No transitions from completed
		"Cancelled":  {}, // No transitions from cancelled
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return fmt.Errorf("invalid current status: %s", currentStatus)
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return nil
		}
	}

	return fmt.Errorf("invalid status transition from %s to %s", currentStatus, newStatus)
}

// GetValidStatuses returns all valid sales order statuses
func GetValidStatuses() []string {
	return []string{"Draft", "Confirmed", "Processing", "Completed", "Cancelled"}
}

// GetValidStatusTransitions returns valid status transitions for a given status
func GetValidStatusTransitions(currentStatus string) []string {
	validTransitions := map[string][]string{
		"Draft":      {"Confirmed", "Cancelled"},
		"Confirmed":  {"Processing", "Cancelled"},
		"Processing": {"Completed", "Cancelled"},
		"Completed":  {},
		"Cancelled":  {},
	}

	if transitions, exists := validTransitions[currentStatus]; exists {
		return transitions
	}
	return []string{}
}
