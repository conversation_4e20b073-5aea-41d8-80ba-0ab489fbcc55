package dto

import (
	"time"

	"adc-account-backend/internal/models"
)

// CreateBankAccountRequest represents the request payload for creating a bank account
type CreateBankAccountRequest struct {
	AccountName         string  `json:"accountName" binding:"required" validate:"min=1,max=255"`
	BankName            string  `json:"bankName" binding:"required" validate:"min=1,max=255"`
	AccountNumber       string  `json:"accountNumber" binding:"required" validate:"min=1,max=100"`
	AccountNumberMasked *string `json:"accountNumberMasked,omitempty" validate:"omitempty,max=50"`
	RoutingNumber       *string `json:"routingNumber,omitempty" validate:"omitempty,max=50"`
	AccountType         *string `json:"accountType,omitempty" validate:"omitempty,max=50"`
	Currency            string  `json:"currency" validate:"omitempty,len=3"`
	ChartOfAccountID    string  `json:"chartOfAccountId" binding:"required"`
	Metadata            *string `json:"metadata,omitempty"`
}

// UpdateBankAccountRequest represents the request payload for updating a bank account
type UpdateBankAccountRequest struct {
	AccountName         *string `json:"accountName,omitempty" validate:"omitempty,min=1,max=255"`
	BankName            *string `json:"bankName,omitempty" validate:"omitempty,min=1,max=255"`
	AccountNumberMasked *string `json:"accountNumberMasked,omitempty" validate:"omitempty,max=50"`
	Currency            *string `json:"currency,omitempty" validate:"omitempty,len=3"`
	ChartOfAccountID    *string `json:"chartOfAccountId,omitempty"`
	Metadata            *string `json:"metadata,omitempty"`
}

// BankAccountResponse represents the response payload for bank account operations
type BankAccountResponse struct {
	ID                  string                 `json:"id"`
	MerchantID          string                 `json:"merchantId"`
	AccountName         string                 `json:"accountName"`
	BankName            *string                `json:"bankName,omitempty"`
	AccountNumberMasked *string                `json:"accountNumberMasked,omitempty"`
	Currency            string                 `json:"currency"`
	ChartOfAccountID    string                 `json:"chartOfAccountId"`
	CreatedAt           time.Time              `json:"createdAt"`
	UpdatedAt           time.Time              `json:"updatedAt"`
	Metadata            *string                `json:"metadata,omitempty"`
	Merchant            *MerchantSummary       `json:"merchant,omitempty"`
	ChartOfAccount      *ChartOfAccountSummary `json:"chartOfAccount,omitempty"`
}

// MerchantSummary represents a summary of merchant information
type MerchantSummary struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// ChartOfAccountSummary represents a summary of chart of account information
type ChartOfAccountSummary struct {
	ID   string `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
	Type string `json:"type"`
}

// BankAccountListResponse represents the response for listing bank accounts
type BankAccountListResponse struct {
	BankAccounts []BankAccountResponse `json:"bankAccounts"`
	Pagination   PaginationResponse    `json:"pagination"`
}

// PaginationResponse represents pagination information
type PaginationResponse struct {
	Total           int64 `json:"total"`
	Page            int   `json:"page"`
	Limit           int   `json:"limit"`
	TotalPages      int   `json:"totalPages"`
	HasNextPage     bool  `json:"hasNextPage"`
	HasPreviousPage bool  `json:"hasPreviousPage"`
}

// ToBankAccountResponse converts a models.BankAccount to BankAccountResponse
func ToBankAccountResponse(bankAccount *models.BankAccount) *BankAccountResponse {
	// Helper function to safely dereference pointers
	var currency string
	if bankAccount.Currency != nil {
		currency = *bankAccount.Currency
	}

	var createdAt, updatedAt time.Time
	if bankAccount.CreatedAt != nil {
		createdAt = *bankAccount.CreatedAt
	}
	if bankAccount.UpdatedAt != nil {
		updatedAt = *bankAccount.UpdatedAt
	}

	response := &BankAccountResponse{
		ID:                  bankAccount.ID,
		MerchantID:          bankAccount.BranchID, // TODO: Update response struct to use BranchID
		AccountName:         bankAccount.AccountName,
		BankName:            &bankAccount.BankName,
		AccountNumberMasked: bankAccount.AccountNumberMasked,
		Currency:            currency,
		ChartOfAccountID:    bankAccount.ChartOfAccountID,
		CreatedAt:           createdAt,
		UpdatedAt:           updatedAt,
		Metadata:            bankAccount.Metadata,
	}

	// Add merchant information if loaded
	if bankAccount.Branch.ID != "" {
		response.Merchant = &MerchantSummary{
			ID:   bankAccount.Branch.ID,
			Name: bankAccount.Branch.Name,
		}
	}

	// Add chart of account information if loaded
	if bankAccount.ChartOfAccount.ID != "" {
		response.ChartOfAccount = &ChartOfAccountSummary{
			ID:   bankAccount.ChartOfAccount.ID,
			Code: bankAccount.ChartOfAccount.Code,
			Name: bankAccount.ChartOfAccount.Name,
			Type: string(bankAccount.ChartOfAccount.Type),
		}
	}

	return response
}

// ToBankAccountListResponse converts a slice of models.BankAccount to BankAccountListResponse
func ToBankAccountListResponse(bankAccounts []models.BankAccount, total int64, page, limit int) *BankAccountListResponse {
	responses := make([]BankAccountResponse, len(bankAccounts))
	for i, bankAccount := range bankAccounts {
		responses[i] = *ToBankAccountResponse(&bankAccount)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	return &BankAccountListResponse{
		BankAccounts: responses,
		Pagination: PaginationResponse{
			Total:           total,
			Page:            page,
			Limit:           limit,
			TotalPages:      totalPages,
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
		},
	}
}

// ToCreateBankAccountModel converts CreateBankAccountRequest to models.BankAccount
func (req *CreateBankAccountRequest) ToCreateBankAccountModel(branchID string) (*models.BankAccount, error) {
	defaultCurrency := "USD"
	currency := &defaultCurrency
	if req.Currency != "" {
		currency = &req.Currency
	}

	bankAccount := &models.BankAccount{
		BranchID:            branchID, // TODO: Update parameter to use branchID
		AccountName:         req.AccountName,
		BankName:            req.BankName,
		AccountNumber:       req.AccountNumber,
		AccountNumberMasked: req.AccountNumberMasked,
		RoutingNumber:       req.RoutingNumber,
		AccountType:         req.AccountType,
		Currency:            currency,
		ChartOfAccountID:    req.ChartOfAccountID,
		Metadata:            req.Metadata,
	}

	return bankAccount, nil
}

// ToCreateBankAccountModelForUser converts CreateBankAccountRequest to models.BankAccount for user-based creation
func (req *CreateBankAccountRequest) ToCreateBankAccountModelForUser(branchID string) (*models.BankAccount, error) {
	defaultCurrency := "USD"
	currency := &defaultCurrency
	if req.Currency != "" {
		currency = &req.Currency
	}

	bankAccount := &models.BankAccount{
		BranchID:            branchID,
		AccountName:         req.AccountName,
		BankName:            req.BankName,
		AccountNumber:       req.AccountNumber,
		AccountNumberMasked: req.AccountNumberMasked,
		RoutingNumber:       req.RoutingNumber,
		AccountType:         req.AccountType,
		Currency:            currency,
		ChartOfAccountID:    req.ChartOfAccountID,
		Metadata:            req.Metadata,
	}

	return bankAccount, nil
}

// ToUpdateBankAccountModel converts UpdateBankAccountRequest to models.BankAccount
func (req *UpdateBankAccountRequest) ToUpdateBankAccountModel() *models.BankAccount {
	bankAccount := &models.BankAccount{}

	if req.AccountName != nil {
		bankAccount.AccountName = *req.AccountName
	}
	if req.BankName != nil {
		bankAccount.BankName = *req.BankName
	}
	if req.AccountNumberMasked != nil {
		bankAccount.AccountNumberMasked = req.AccountNumberMasked
	}
	if req.Currency != nil {
		bankAccount.Currency = req.Currency
	}
	if req.ChartOfAccountID != nil {
		bankAccount.ChartOfAccountID = *req.ChartOfAccountID
	}
	if req.Metadata != nil {
		bankAccount.Metadata = req.Metadata
	}

	return bankAccount
}
