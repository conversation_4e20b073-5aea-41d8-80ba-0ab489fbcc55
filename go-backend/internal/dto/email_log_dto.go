package dto

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"
)

// CreateEmailLogRequest represents the request payload for creating an email log
type CreateEmailLogRequest struct {
	TemplateID *string `json:"templateId,omitempty"`
	ToEmail    string  `json:"toEmail" binding:"required" validate:"email"`
	FromEmail  string  `json:"fromEmail" binding:"required" validate:"email"`
	Subject    string  `json:"subject" binding:"required" validate:"min=1,max=255"`
	Body       string  `json:"body" binding:"required" validate:"min=1"`
	Status     *string `json:"status,omitempty" validate:"omitempty,oneof=Sent Failed Scheduled"`
}

// UpdateEmailLogRequest represents the request payload for updating an email log
type UpdateEmailLogRequest struct {
	TemplateID   *string `json:"templateId,omitempty"`
	ToEmail      *string `json:"toEmail,omitempty" validate:"omitempty,email"`
	FromEmail    *string `json:"fromEmail,omitempty" validate:"omitempty,email"`
	Subject      *string `json:"subject,omitempty" validate:"omitempty,min=1,max=255"`
	Body         *string `json:"body,omitempty" validate:"omitempty,min=1"`
	Status       *string `json:"status,omitempty" validate:"omitempty,oneof=Sent Failed Scheduled"`
	ErrorMessage *string `json:"errorMessage,omitempty" validate:"omitempty,max=1000"`
}

// UpdateEmailLogStatusRequest represents the request payload for updating email log status
type UpdateEmailLogStatusRequest struct {
	Status       string  `json:"status" binding:"required" validate:"oneof=Sent Failed Scheduled"`
	ErrorMessage *string `json:"errorMessage,omitempty" validate:"omitempty,max=1000"`
}

// EmailLogResponse represents the response payload for email log operations
type EmailLogResponse struct {
	ID           string                `json:"id"`
	MerchantID   string                `json:"merchantId"`
	TemplateID   *string               `json:"templateId,omitempty"`
	ToEmail      string                `json:"toEmail"`
	FromEmail    string                `json:"fromEmail"`
	Subject      string                `json:"subject"`
	Body         string                `json:"body"`
	Status       string                `json:"status"`
	SentAt       *time.Time            `json:"sentAt,omitempty"`
	ErrorMessage *string               `json:"errorMessage,omitempty"`
	CreatedAt    time.Time             `json:"createdAt"`
	Merchant     *MerchantSummary      `json:"merchant,omitempty"`
	Template     *EmailTemplateSummary `json:"template,omitempty"`
}

// EmailTemplateSummary represents a summary of email template information
type EmailTemplateSummary struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Type      string `json:"type"`
	Subject   string `json:"subject"`
	IsDefault bool   `json:"isDefault"`
	IsActive  bool   `json:"isActive"`
}

// EmailLogListResponse represents the response for listing email logs
type EmailLogListResponse struct {
	EmailLogs  []EmailLogResponse `json:"emailLogs"`
	Pagination PaginationResponse `json:"pagination"`
	Summary    *EmailLogSummary   `json:"summary,omitempty"`
}

// EmailLogSummary represents summary statistics for email logs
type EmailLogSummary struct {
	TotalEmails     int64   `json:"totalEmails"`
	SentEmails      int64   `json:"sentEmails"`
	FailedEmails    int64   `json:"failedEmails"`
	ScheduledEmails int64   `json:"scheduledEmails"`
	SuccessRate     float64 `json:"successRate"`
}

// EmailLogFilterRequest represents filter parameters for email logs
type EmailLogFilterRequest struct {
	Page      int        `json:"page" form:"page"`
	Limit     int        `json:"limit" form:"limit"`
	Search    string     `json:"search" form:"search"`
	Status    string     `json:"status" form:"status"`
	StartDate *time.Time `json:"startDate" form:"startDate"`
	EndDate   *time.Time `json:"endDate" form:"endDate"`
}

// ToEmailLogResponse converts a models.EmailLog to EmailLogResponse
func ToEmailLogResponse(log *models.EmailLog) *EmailLogResponse {
	response := &EmailLogResponse{
		ID:           log.ID,
		MerchantID:   log.BranchID, // TODO: Update response struct to use BranchID
		TemplateID:   log.TemplateID,
		ToEmail:      log.ToEmail,
		FromEmail:    log.FromEmail,
		Subject:      log.Subject,
		Body:         log.Body,
		Status:       string(log.Status),
		SentAt:       log.SentAt,
		ErrorMessage: log.ErrorMessage,
		CreatedAt:    log.CreatedAt,
	}

	// Add merchant information if loaded
	if log.Branch.ID != "" {
		response.Merchant = &MerchantSummary{
			ID:   log.Branch.ID,
			Name: log.Branch.Name,
		}
	}

	// Add template information if loaded
	if log.Template != nil && log.Template.ID != "" {
		response.Template = &EmailTemplateSummary{
			ID:        log.Template.ID,
			Name:      log.Template.Name,
			Type:      log.Template.Type,
			Subject:   log.Template.Subject,
			IsDefault: log.Template.IsDefault,
			IsActive:  log.Template.IsActive,
		}
	}

	return response
}

// ToEmailLogListResponse converts a slice of models.EmailLog to EmailLogListResponse
func ToEmailLogListResponse(logs []models.EmailLog, total int64, page, limit int, includeSummary bool) *EmailLogListResponse {
	responses := make([]EmailLogResponse, len(logs))
	for i, log := range logs {
		responses[i] = *ToEmailLogResponse(&log)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNextPage := page < totalPages
	hasPreviousPage := page > 1

	response := &EmailLogListResponse{
		EmailLogs: responses,
		Pagination: PaginationResponse{
			Total:           total,
			Page:            page,
			Limit:           limit,
			TotalPages:      totalPages,
			HasNextPage:     hasNextPage,
			HasPreviousPage: hasPreviousPage,
		},
	}

	// Add summary if requested
	if includeSummary {
		response.Summary = calculateEmailLogSummary(logs)
	}

	return response
}

// calculateEmailLogSummary calculates summary statistics for email logs
func calculateEmailLogSummary(logs []models.EmailLog) *EmailLogSummary {
	summary := &EmailLogSummary{
		TotalEmails: int64(len(logs)),
	}

	for _, log := range logs {
		switch log.Status {
		case models.EmailStatusSent:
			summary.SentEmails++
		case models.EmailStatusFailed:
			summary.FailedEmails++
		case models.EmailStatusScheduled:
			summary.ScheduledEmails++
		}
	}

	// Calculate success rate
	if summary.TotalEmails > 0 {
		summary.SuccessRate = float64(summary.SentEmails) / float64(summary.TotalEmails) * 100
	}

	return summary
}

// ToCreateEmailLogModel converts CreateEmailLogRequest to models.EmailLog
func (req *CreateEmailLogRequest) ToCreateEmailLogModel(branchID string) (*models.EmailLog, error) {
	log := &models.EmailLog{
		BranchID:   branchID, // TODO: Update parameter to use branchID
		TemplateID: req.TemplateID,
		ToEmail:    req.ToEmail,
		FromEmail:  req.FromEmail,
		Subject:    req.Subject,
		Body:       req.Body,
		Status:     models.EmailStatusScheduled, // Default status
	}

	// Set status if provided
	if req.Status != nil {
		switch *req.Status {
		case "Sent":
			log.Status = models.EmailStatusSent
		case "Failed":
			log.Status = models.EmailStatusFailed
		case "Scheduled":
			log.Status = models.EmailStatusScheduled
		default:
			log.Status = models.EmailStatusScheduled
		}
	}

	return log, nil
}

// ToUpdateEmailLogModel converts UpdateEmailLogRequest to models.EmailLog
func (req *UpdateEmailLogRequest) ToUpdateEmailLogModel() *models.EmailLog {
	log := &models.EmailLog{}

	if req.TemplateID != nil {
		log.TemplateID = req.TemplateID
	}
	if req.ToEmail != nil {
		log.ToEmail = *req.ToEmail
	}
	if req.FromEmail != nil {
		log.FromEmail = *req.FromEmail
	}
	if req.Subject != nil {
		log.Subject = *req.Subject
	}
	if req.Body != nil {
		log.Body = *req.Body
	}
	if req.ErrorMessage != nil {
		log.ErrorMessage = req.ErrorMessage
	}
	if req.Status != nil {
		switch *req.Status {
		case "Sent":
			log.Status = models.EmailStatusSent
		case "Failed":
			log.Status = models.EmailStatusFailed
		case "Scheduled":
			log.Status = models.EmailStatusScheduled
		}
	}

	return log
}

// ToEmailLogSummaryResponse converts service EmailLogSummary to DTO
func ToEmailLogSummaryResponse(summary *services.EmailLogSummary) *EmailLogSummary {
	return &EmailLogSummary{
		TotalEmails:     summary.TotalEmails,
		SentEmails:      summary.SentEmails,
		FailedEmails:    summary.FailedEmails,
		ScheduledEmails: summary.ScheduledEmails,
		SuccessRate:     summary.SuccessRate,
	}
}

// GetValidEmailStatuses returns all valid email statuses
func GetValidEmailStatuses() []string {
	return []string{"Sent", "Failed", "Scheduled"}
}

// ValidateEmailStatus validates if an email status is valid
func ValidateEmailStatus(status string) error {
	validStatuses := GetValidEmailStatuses()
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}
	return fmt.Errorf("invalid email status: %s. Valid statuses are: %v", status, validStatuses)
}

// ValidateEmailAddress performs basic email validation
func ValidateEmailAddress(email string) error {
	if len(email) < 5 {
		return fmt.Errorf("email address too short")
	}

	atCount := 0
	for _, char := range email {
		if char == '@' {
			atCount++
		}
	}

	if atCount != 1 {
		return fmt.Errorf("email address must contain exactly one @ symbol")
	}

	if email[0] == '@' || email[len(email)-1] == '@' {
		return fmt.Errorf("email address cannot start or end with @")
	}

	return nil
}

// FormatEmailStatus formats an email status for display
func FormatEmailStatus(status models.EmailStatus) string {
	switch status {
	case models.EmailStatusSent:
		return "Sent"
	case models.EmailStatusFailed:
		return "Failed"
	case models.EmailStatusScheduled:
		return "Scheduled"
	default:
		return string(status)
	}
}

// GetEmailStatusColor returns a color code for email status (for UI purposes)
func GetEmailStatusColor(status models.EmailStatus) string {
	switch status {
	case models.EmailStatusSent:
		return "green"
	case models.EmailStatusFailed:
		return "red"
	case models.EmailStatusScheduled:
		return "blue"
	default:
		return "gray"
	}
}

// IsEmailStatusFinal checks if an email status is final (cannot be changed)
func IsEmailStatusFinal(status models.EmailStatus) bool {
	return status == models.EmailStatusSent || status == models.EmailStatusFailed
}

// GetEmailLogMetrics calculates various metrics for email logs
func GetEmailLogMetrics(logs []models.EmailLog) map[string]interface{} {
	metrics := make(map[string]interface{})

	total := len(logs)
	sent := 0
	failed := 0
	scheduled := 0

	for _, log := range logs {
		switch log.Status {
		case models.EmailStatusSent:
			sent++
		case models.EmailStatusFailed:
			failed++
		case models.EmailStatusScheduled:
			scheduled++
		}
	}

	metrics["total"] = total
	metrics["sent"] = sent
	metrics["failed"] = failed
	metrics["scheduled"] = scheduled

	if total > 0 {
		metrics["successRate"] = float64(sent) / float64(total) * 100
		metrics["failureRate"] = float64(failed) / float64(total) * 100
	} else {
		metrics["successRate"] = 0.0
		metrics["failureRate"] = 0.0
	}

	return metrics
}
