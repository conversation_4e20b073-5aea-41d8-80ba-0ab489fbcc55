package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CreditNoteService handles credit note-related business logic
type CreditNoteService struct {
	db *gorm.DB
}

// NewCreditNoteService creates a new CreditNoteService
func NewCreditNoteService(db *gorm.DB) *CreditNoteService {
	return &CreditNoteService{db: db}
}

// GetAllCreditNotes retrieves all credit notes with pagination
func (s *CreditNoteService) GetAllCreditNotes(page, limit int, search string) ([]models.CreditNote, int64, error) {
	var creditNotes []models.CreditNote
	var total int64

	query := s.db.Model(&models.CreditNote{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON credit_notes.customer_id = customers.id").
			Where("credit_notes.credit_note_number ILIKE ? OR customers.name ILIKE ? OR credit_notes.reason ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count credit notes: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("issue_date DESC, created_at DESC").
		Find(&creditNotes).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch credit notes: %w", err)
	}

	return creditNotes, total, nil
}

// GetCreditNoteByID retrieves a credit note by ID
func (s *CreditNoteService) GetCreditNoteByID(id string) (*models.CreditNote, error) {
	var creditNote models.CreditNote

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("Items").Preload("Items.TaxRate").Preload("Applications").Preload("Applications.Invoice").
		First(&creditNote, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("credit note not found")
		}
		return nil, fmt.Errorf("failed to fetch credit note: %w", err)
	}

	return &creditNote, nil
}

// GetCreditNoteByNumber retrieves a credit note by number and merchant
func (s *CreditNoteService) GetCreditNoteByNumber(branchID, creditNoteNumber string) (*models.CreditNote, error) {
	var creditNote models.CreditNote

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("Items").Preload("Items.TaxRate").Preload("Applications").Preload("Applications.Invoice").
		First(&creditNote, "branch_id = ? AND credit_note_number = ?", branchID, creditNoteNumber).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("credit note not found")
		}
		return nil, fmt.Errorf("failed to fetch credit note: %w", err)
	}

	return &creditNote, nil
}

// GetCreditNotesByMerchant retrieves credit notes for a specific merchant
func (s *CreditNoteService) GetCreditNotesByMerchant(branchID string, page, limit int, search, status, noteType string, startDate, endDate *time.Time) ([]models.CreditNote, int64, error) {
	var creditNotes []models.CreditNote
	var total int64

	query := s.db.Model(&models.CreditNote{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON credit_notes.customer_id = customers.id").
			Where("credit_notes.credit_note_number ILIKE ? OR customers.name ILIKE ? OR credit_notes.reason ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply type filter if provided
	if noteType != "" {
		query = query.Where("type = ?", noteType)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("issue_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("issue_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count credit notes: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("issue_date DESC, created_at DESC").
		Find(&creditNotes).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch credit notes: %w", err)
	}

	return creditNotes, total, nil
}

// GetCreditNotesByCustomer retrieves credit notes for a specific customer
func (s *CreditNoteService) GetCreditNotesByCustomer(customerID string, page, limit int, status string) ([]models.CreditNote, int64, error) {
	var creditNotes []models.CreditNote
	var total int64

	query := s.db.Model(&models.CreditNote{}).Where("customer_id = ?", customerID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count credit notes: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("issue_date DESC, created_at DESC").
		Find(&creditNotes).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch credit notes: %w", err)
	}

	return creditNotes, total, nil
}

// CreateCreditNote creates a new credit note with items
func (s *CreditNoteService) CreateCreditNote(creditNote *models.CreditNote, items []models.CreditNoteItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", creditNote.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate customer exists and belongs to branch if provided
		if creditNote.CustomerID != nil {
			var customer models.Customer
			if err := tx.First(&customer, "id = ? AND branch_id = ?", *creditNote.CustomerID, creditNote.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("customer not found or does not belong to branch")
				}
				return fmt.Errorf("failed to validate customer: %w", err)
			}
		}

		// Validate invoice exists and belongs to branch if provided
		if creditNote.InvoiceID != nil {
			var invoice models.Invoice
			if err := tx.First(&invoice, "id = ? AND branch_id = ?", *creditNote.InvoiceID, creditNote.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("invoice not found or does not belong to branch")
				}
				return fmt.Errorf("failed to validate invoice: %w", err)
			}
		}

		// Check if credit note number already exists for this branch
		var existingCreditNote models.CreditNote
		if err := tx.First(&existingCreditNote, "branch_id = ? AND credit_note_number = ?", creditNote.BranchID, creditNote.CreditNoteNumber).Error; err == nil {
			return fmt.Errorf("credit note number already exists for this branch")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check credit note number uniqueness: %w", err)
		}

		// Calculate totals from items
		var subtotal, taxTotal decimal.Decimal
		for _, item := range items {
			itemTotal := item.Quantity.Mul(item.UnitPrice)
			subtotal = subtotal.Add(itemTotal)
			taxTotal = taxTotal.Add(item.TaxAmount)
		}

		// Set calculated amounts
		creditNote.Amount = subtotal
		creditNote.TaxAmount = taxTotal
		creditNote.TotalAmount = subtotal.Add(taxTotal)
		creditNote.BalanceAmount = creditNote.TotalAmount
		creditNote.AppliedAmount = decimal.Zero

		// Set default status if not provided
		if creditNote.Status == "" {
			creditNote.Status = models.CreditNoteStatusDraft
		}

		// Create the credit note
		if err := tx.Create(creditNote).Error; err != nil {
			return fmt.Errorf("failed to create credit note: %w", err)
		}

		// Create credit note items
		for i := range items {
			items[i].CreditNoteID = creditNote.ID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
			// Calculate total price for the item
			items[i].TotalPrice = items[i].Quantity.Mul(items[i].UnitPrice)
		}

		if err := tx.Create(&items).Error; err != nil {
			return fmt.Errorf("failed to create credit note items: %w", err)
		}

		return nil
	})
}

// UpdateCreditNote updates an existing credit note
func (s *CreditNoteService) UpdateCreditNote(id string, updates *models.CreditNote) (*models.CreditNote, error) {
	var creditNote models.CreditNote

	// Check if credit note exists
	if err := s.db.First(&creditNote, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("credit note not found")
		}
		return nil, fmt.Errorf("failed to fetch credit note: %w", err)
	}

	// Prevent updates to closed or void credit notes
	if creditNote.Status == models.CreditNoteStatusClosed || creditNote.Status == models.CreditNoteStatusVoid {
		return nil, fmt.Errorf("cannot update closed or void credit note")
	}

	// If updating customer ID, validate the new customer
	if updates.CustomerID != nil && (creditNote.CustomerID == nil || *updates.CustomerID != *creditNote.CustomerID) {
		var customer models.Customer
		if err := s.db.First(&customer, "id = ? AND branch_id = ?", *updates.CustomerID, creditNote.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("customer not found or does not belong to branch")
			}
			return nil, fmt.Errorf("failed to validate customer: %w", err)
		}
	}

	// If updating invoice ID, validate the new invoice
	if updates.InvoiceID != nil && (creditNote.InvoiceID == nil || *updates.InvoiceID != *creditNote.InvoiceID) {
		var invoice models.Invoice
		if err := s.db.First(&invoice, "id = ? AND branch_id = ?", *updates.InvoiceID, creditNote.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("invoice not found or does not belong to branch")
			}
			return nil, fmt.Errorf("failed to validate invoice: %w", err)
		}
	}

	// If updating credit note number, check uniqueness
	if updates.CreditNoteNumber != "" && updates.CreditNoteNumber != creditNote.CreditNoteNumber {
		var existingCreditNote models.CreditNote
		if err := s.db.First(&existingCreditNote, "branch_id = ? AND credit_note_number = ? AND id != ?",
			creditNote.BranchID, updates.CreditNoteNumber, id).Error; err == nil {
			return nil, fmt.Errorf("credit note number already exists for this branch")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to check credit note number uniqueness: %w", err)
		}
	}

	// Update the credit note
	if err := s.db.Model(&creditNote).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update credit note: %w", err)
	}

	// Fetch updated credit note with relationships
	if err := s.db.Preload("Branch").Preload("Customer").Preload("Invoice").
		Preload("Items").Preload("Items.TaxRate").Preload("Applications").Preload("Applications.Invoice").
		First(&creditNote, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated credit note: %w", err)
	}

	return &creditNote, nil
}

// UpdateCreditNoteStatus updates the status of a credit note
func (s *CreditNoteService) UpdateCreditNoteStatus(id string, status models.CreditNoteStatus) (*models.CreditNote, error) {
	var creditNote models.CreditNote

	// Check if credit note exists
	if err := s.db.First(&creditNote, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("credit note not found")
		}
		return nil, fmt.Errorf("failed to fetch credit note: %w", err)
	}

	// Validate status transition
	if !s.isValidStatusTransition(creditNote.Status, status) {
		return nil, fmt.Errorf("invalid status transition from %s to %s", creditNote.Status, status)
	}

	// Update status
	creditNote.Status = status
	if err := s.db.Save(&creditNote).Error; err != nil {
		return nil, fmt.Errorf("failed to update credit note status: %w", err)
	}

	return &creditNote, nil
}

// DeleteCreditNote deletes a credit note
func (s *CreditNoteService) DeleteCreditNote(id string) error {
	var creditNote models.CreditNote

	// Check if credit note exists
	if err := s.db.First(&creditNote, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("credit note not found")
		}
		return fmt.Errorf("failed to fetch credit note: %w", err)
	}

	// Prevent deletion of applied credit notes
	if creditNote.AppliedAmount.GreaterThan(decimal.Zero) {
		return fmt.Errorf("cannot delete credit note with applied amounts")
	}

	// Prevent deletion of closed credit notes
	if creditNote.Status == models.CreditNoteStatusClosed {
		return fmt.Errorf("cannot delete closed credit note")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete credit note items first
		if err := tx.Where("credit_note_id = ?", id).Delete(&models.CreditNoteItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete credit note items: %w", err)
		}

		// Delete credit applications
		if err := tx.Where("credit_note_id = ?", id).Delete(&models.CreditApplication{}).Error; err != nil {
			return fmt.Errorf("failed to delete credit applications: %w", err)
		}

		// Delete the credit note
		if err := tx.Delete(&creditNote).Error; err != nil {
			return fmt.Errorf("failed to delete credit note: %w", err)
		}

		return nil
	})
}

// ApplyCreditToInvoice applies credit note amount to an invoice
func (s *CreditNoteService) ApplyCreditToInvoice(creditNoteID, invoiceID string, amount decimal.Decimal) (*models.CreditApplication, error) {
	var application *models.CreditApplication
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get credit note
		var creditNote models.CreditNote
		if err := tx.First(&creditNote, "id = ?", creditNoteID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("credit note not found")
			}
			return fmt.Errorf("failed to fetch credit note: %w", err)
		}

		// Get invoice
		var invoice models.Invoice
		if err := tx.First(&invoice, "id = ? AND branch_id = ?", invoiceID, creditNote.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("invoice not found or does not belong to branch")
			}
			return fmt.Errorf("failed to fetch invoice: %w", err)
		}

		// Validate credit note status
		if creditNote.Status != models.CreditNoteStatusOpen {
			return fmt.Errorf("credit note must be open to apply credit")
		}

		// Validate amount
		if amount.LessThanOrEqual(decimal.Zero) {
			return fmt.Errorf("application amount must be greater than zero")
		}

		if amount.GreaterThan(creditNote.BalanceAmount) {
			return fmt.Errorf("application amount cannot exceed credit note balance")
		}

		// Create credit application
		application = &models.CreditApplication{
			CreditNoteID: creditNoteID,
			InvoiceID:    invoiceID,
			Amount:       amount,
			AppliedDate:  time.Now(),
		}

		if err := tx.Create(application).Error; err != nil {
			return fmt.Errorf("failed to create credit application: %w", err)
		}

		// Update credit note applied and balance amounts
		newAppliedAmount := creditNote.AppliedAmount.Add(amount)
		newBalanceAmount := creditNote.TotalAmount.Sub(newAppliedAmount)

		if err := tx.Model(&creditNote).Updates(map[string]interface{}{
			"applied_amount": newAppliedAmount,
			"balance_amount": newBalanceAmount,
		}).Error; err != nil {
			return fmt.Errorf("failed to update credit note amounts: %w", err)
		}

		// Update credit note status if fully applied
		if newBalanceAmount.IsZero() {
			if err := tx.Model(&creditNote).Update("status", models.CreditNoteStatusClosed).Error; err != nil {
				return fmt.Errorf("failed to update credit note status: %w", err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return application, nil
}

// GenerateCreditNoteNumber generates a unique credit note number for a branch
func (s *CreditNoteService) GenerateCreditNoteNumber(branchID string) (string, error) {
	// Get the current year
	year := time.Now().Year()

	// Get the count of credit notes for this branch this year
	var count int64
	startOfYear := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endOfYear := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.UTC)

	if err := s.db.Model(&models.CreditNote{}).
		Where("branch_id = ? AND issue_date >= ? AND issue_date < ?", branchID, startOfYear, endOfYear).
		Count(&count).Error; err != nil {
		return "", fmt.Errorf("failed to count existing credit notes: %w", err)
	}

	// Generate credit note number: CN-YYYY-NNNN
	creditNoteNumber := fmt.Sprintf("CN-%d-%04d", year, count+1)

	// Ensure uniqueness (in case of concurrent requests)
	for {
		var existingCreditNote models.CreditNote
		if err := s.db.First(&existingCreditNote, "branch_id = ? AND credit_note_number = ?", branchID, creditNoteNumber).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break // Credit note number is unique
			}
			return "", fmt.Errorf("failed to check credit note number uniqueness: %w", err)
		}
		// Credit note number exists, increment and try again
		count++
		creditNoteNumber = fmt.Sprintf("CN-%d-%04d", year, count+1)
	}

	return creditNoteNumber, nil
}

// GetCreditNoteSummary gets summary statistics for credit notes
func (s *CreditNoteService) GetCreditNoteSummary(branchID string, startDate, endDate *time.Time) (*CreditNoteSummary, error) {
	query := s.db.Model(&models.CreditNote{}).Where("branch_id = ?", branchID)

	if startDate != nil {
		query = query.Where("issue_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("issue_date <= ?", *endDate)
	}

	var summary CreditNoteSummary

	// Get total count
	if err := query.Count(&summary.TotalCreditNotes).Error; err != nil {
		return nil, fmt.Errorf("failed to count credit notes: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.CreditNoteStatusDraft):
			summary.DraftCreditNotes = sc.Count
		case string(models.CreditNoteStatusOpen):
			summary.OpenCreditNotes = sc.Count
		case string(models.CreditNoteStatusClosed):
			summary.ClosedCreditNotes = sc.Count
		case string(models.CreditNoteStatusVoid):
			summary.VoidCreditNotes = sc.Count
		}
	}

	// Get total amounts
	var totalAmount, appliedAmount, balanceAmount decimal.Decimal
	if err := query.Select("COALESCE(SUM(total_amount), 0) as total, COALESCE(SUM(applied_amount), 0) as applied, COALESCE(SUM(balance_amount), 0) as balance").
		Scan(&struct {
			Total   decimal.Decimal `gorm:"column:total"`
			Applied decimal.Decimal `gorm:"column:applied"`
			Balance decimal.Decimal `gorm:"column:balance"`
		}{Total: totalAmount, Applied: appliedAmount, Balance: balanceAmount}).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate amounts: %w", err)
	}

	summary.TotalAmount = totalAmount.String()
	summary.AppliedAmount = appliedAmount.String()
	summary.BalanceAmount = balanceAmount.String()

	return &summary, nil
}

// CreditNoteSummary represents summary statistics for credit notes
type CreditNoteSummary struct {
	TotalCreditNotes  int64  `json:"totalCreditNotes"`
	DraftCreditNotes  int64  `json:"draftCreditNotes"`
	OpenCreditNotes   int64  `json:"openCreditNotes"`
	ClosedCreditNotes int64  `json:"closedCreditNotes"`
	VoidCreditNotes   int64  `json:"voidCreditNotes"`
	TotalAmount       string `json:"totalAmount"`
	AppliedAmount     string `json:"appliedAmount"`
	BalanceAmount     string `json:"balanceAmount"`
}

// isValidStatusTransition checks if a status transition is valid
func (s *CreditNoteService) isValidStatusTransition(currentStatus, newStatus models.CreditNoteStatus) bool {
	validTransitions := map[models.CreditNoteStatus][]models.CreditNoteStatus{
		models.CreditNoteStatusDraft:  {models.CreditNoteStatusOpen, models.CreditNoteStatusVoid},
		models.CreditNoteStatusOpen:   {models.CreditNoteStatusClosed, models.CreditNoteStatusVoid},
		models.CreditNoteStatusClosed: {}, // No transitions from closed
		models.CreditNoteStatusVoid:   {}, // No transitions from void
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// ValidateCreditNote validates credit note data
func (s *CreditNoteService) ValidateCreditNote(creditNote *models.CreditNote) error {
	if creditNote.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if creditNote.CreditNoteNumber == "" {
		return fmt.Errorf("credit note number is required")
	}
	if creditNote.IssueDate.IsZero() {
		return fmt.Errorf("issue date is required")
	}
	if creditNote.Reason == "" {
		return fmt.Errorf("reason is required")
	}
	if creditNote.TotalAmount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("total amount must be greater than zero")
	}

	// Validate status
	validStatuses := []models.CreditNoteStatus{
		models.CreditNoteStatusDraft,
		models.CreditNoteStatusOpen,
		models.CreditNoteStatusClosed,
		models.CreditNoteStatusVoid,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if creditNote.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", creditNote.Status)
	}

	// Validate type
	validTypes := []models.CreditNoteType{
		models.CreditNoteTypeCustomerRefund,
		models.CreditNoteTypeSupplierRefund,
		models.CreditNoteTypeWriteOff,
		models.CreditNoteTypeReturnCredit,
		models.CreditNoteTypeAdjustment,
	}
	isValidType := false
	for _, noteType := range validTypes {
		if creditNote.Type == noteType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid type: %s", creditNote.Type)
	}

	return nil
}
