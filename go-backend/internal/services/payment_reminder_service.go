package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// PaymentReminderService handles payment reminder-related business logic
type PaymentReminderService struct {
	db *gorm.DB
}

// NewPaymentReminderService creates a new PaymentReminderService
func NewPaymentReminderService(db *gorm.DB) *PaymentReminderService {
	return &PaymentReminderService{db: db}
}

// GetAllPaymentReminders retrieves all payment reminders with pagination
func (s *PaymentReminderService) GetAllPaymentReminders(page, limit int, search string) ([]models.PaymentReminder, int64, error) {
	var paymentReminders []models.PaymentReminder
	var total int64

	query := s.db.Model(&models.PaymentReminder{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON payment_reminders.customer_id = customers.id").
			Joins("LEFT JOIN invoices ON payment_reminders.invoice_id = invoices.id").
			Where("customers.name ILIKE ? OR customers.email ILIKE ? OR invoices.invoice_number ILIKE ? OR payment_reminders.subject ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count payment reminders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Offset(offset).Limit(limit).
		Order("scheduled_date ASC, created_at DESC").
		Find(&paymentReminders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch payment reminders: %w", err)
	}

	return paymentReminders, total, nil
}

// GetPaymentReminderByID retrieves a payment reminder by ID
func (s *PaymentReminderService) GetPaymentReminderByID(id string) (*models.PaymentReminder, error) {
	var paymentReminder models.PaymentReminder

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		First(&paymentReminder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment reminder not found")
		}
		return nil, fmt.Errorf("failed to fetch payment reminder: %w", err)
	}

	return &paymentReminder, nil
}

// GetPaymentRemindersByMerchant retrieves payment reminders for a specific merchant
func (s *PaymentReminderService) GetPaymentRemindersByMerchant(branchID string, page, limit int, search, status, reminderType string, startDate, endDate *time.Time) ([]models.PaymentReminder, int64, error) {
	var paymentReminders []models.PaymentReminder
	var total int64

	query := s.db.Model(&models.PaymentReminder{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON payment_reminders.customer_id = customers.id").
			Joins("LEFT JOIN invoices ON payment_reminders.invoice_id = invoices.id").
			Where("customers.name ILIKE ? OR customers.email ILIKE ? OR invoices.invoice_number ILIKE ? OR payment_reminders.subject ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply type filter if provided
	if reminderType != "" {
		query = query.Where("type = ?", reminderType)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("scheduled_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("scheduled_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count payment reminders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Offset(offset).Limit(limit).
		Order("scheduled_date ASC, created_at DESC").
		Find(&paymentReminders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch payment reminders: %w", err)
	}

	return paymentReminders, total, nil
}

// GetPaymentRemindersByCustomer retrieves payment reminders for a specific customer
func (s *PaymentReminderService) GetPaymentRemindersByCustomer(customerID string, page, limit int, status string) ([]models.PaymentReminder, int64, error) {
	var paymentReminders []models.PaymentReminder
	var total int64

	query := s.db.Model(&models.PaymentReminder{}).Where("customer_id = ?", customerID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count payment reminders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Offset(offset).Limit(limit).
		Order("scheduled_date ASC, created_at DESC").
		Find(&paymentReminders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch payment reminders: %w", err)
	}

	return paymentReminders, total, nil
}

// GetPaymentRemindersByInvoice retrieves payment reminders for a specific invoice
func (s *PaymentReminderService) GetPaymentRemindersByInvoice(invoiceID string, page, limit int) ([]models.PaymentReminder, int64, error) {
	var paymentReminders []models.PaymentReminder
	var total int64

	query := s.db.Model(&models.PaymentReminder{}).Where("invoice_id = ?", invoiceID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count payment reminders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Offset(offset).Limit(limit).
		Order("scheduled_date ASC, created_at DESC").
		Find(&paymentReminders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch payment reminders: %w", err)
	}

	return paymentReminders, total, nil
}

// CreatePaymentReminder creates a new payment reminder
func (s *PaymentReminderService) CreatePaymentReminder(paymentReminder *models.PaymentReminder) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", paymentReminder.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate customer exists and belongs to branch
		var customer models.Customer
		if err := tx.First(&customer, "id = ? AND branch_id = ?", paymentReminder.CustomerID, paymentReminder.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate customer: %w", err)
		}

		// Validate invoice exists and belongs to branch and customer
		var invoice models.Invoice
		if err := tx.First(&invoice, "id = ? AND branch_id = ? AND customer_id = ?",
			paymentReminder.InvoiceID, paymentReminder.BranchID, paymentReminder.CustomerID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("invoice not found or does not belong to merchant and customer")
			}
			return fmt.Errorf("failed to validate invoice: %w", err)
		}

		// Check if invoice is already paid
		if invoice.Status == models.InvoiceStatusPaid {
			return fmt.Errorf("cannot create payment reminder for paid invoice")
		}

		// Set default values
		if paymentReminder.Status == "" {
			paymentReminder.Status = models.PaymentReminderStatusScheduled
		}
		if paymentReminder.Type == "" {
			paymentReminder.Type = "payment_due"
		}

		// Validate payment reminder
		if err := s.ValidatePaymentReminder(paymentReminder); err != nil {
			return err
		}

		// Create the payment reminder
		if err := tx.Create(paymentReminder).Error; err != nil {
			return fmt.Errorf("failed to create payment reminder: %w", err)
		}

		return nil
	})
}

// UpdatePaymentReminder updates an existing payment reminder
func (s *PaymentReminderService) UpdatePaymentReminder(id string, updates *models.PaymentReminder) (*models.PaymentReminder, error) {
	var paymentReminder models.PaymentReminder

	// Check if payment reminder exists
	if err := s.db.First(&paymentReminder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment reminder not found")
		}
		return nil, fmt.Errorf("failed to fetch payment reminder: %w", err)
	}

	// Prevent updates to sent reminders
	if paymentReminder.Status == models.PaymentReminderStatusSent {
		return nil, fmt.Errorf("cannot update sent payment reminder")
	}

	var updatedReminder *models.PaymentReminder
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating invoice ID, validate the new invoice
		if updates.InvoiceID != "" && updates.InvoiceID != paymentReminder.InvoiceID {
			var invoice models.Invoice
			if err := tx.First(&invoice, "id = ? AND branch_id = ? AND customer_id = ?",
				updates.InvoiceID, paymentReminder.BranchID, paymentReminder.CustomerID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("invoice not found or does not belong to branch and customer")
				}
				return fmt.Errorf("failed to validate invoice: %w", err)
			}

			// Check if invoice is already paid
			if invoice.Status == models.InvoiceStatusPaid {
				return fmt.Errorf("cannot set payment reminder for paid invoice")
			}
		}

		// Update the payment reminder
		if err := tx.Model(&paymentReminder).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update payment reminder: %w", err)
		}

		// Fetch updated payment reminder with relationships
		if err := tx.Preload("Merchant").Preload("Customer").Preload("Invoice").
			First(&paymentReminder, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated payment reminder: %w", err)
		}

		updatedReminder = &paymentReminder
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedReminder, nil
}

// DeletePaymentReminder deletes a payment reminder
func (s *PaymentReminderService) DeletePaymentReminder(id string) error {
	var paymentReminder models.PaymentReminder

	// Check if payment reminder exists
	if err := s.db.First(&paymentReminder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("payment reminder not found")
		}
		return fmt.Errorf("failed to fetch payment reminder: %w", err)
	}

	// Prevent deletion of sent reminders
	if paymentReminder.Status == models.PaymentReminderStatusSent {
		return fmt.Errorf("cannot delete sent payment reminder")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete the payment reminder
		if err := tx.Delete(&paymentReminder).Error; err != nil {
			return fmt.Errorf("failed to delete payment reminder: %w", err)
		}

		return nil
	})
}

// MarkPaymentReminderAsSent marks a payment reminder as sent
func (s *PaymentReminderService) MarkPaymentReminderAsSent(id string) (*models.PaymentReminder, error) {
	var paymentReminder models.PaymentReminder

	// Check if payment reminder exists
	if err := s.db.First(&paymentReminder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment reminder not found")
		}
		return nil, fmt.Errorf("failed to fetch payment reminder: %w", err)
	}

	// Check if reminder is scheduled
	if paymentReminder.Status != models.PaymentReminderStatusScheduled {
		return nil, fmt.Errorf("can only mark scheduled payment reminders as sent")
	}

	// Update status and sent date
	now := time.Now()
	updates := map[string]interface{}{
		"status":  models.PaymentReminderStatusSent,
		"sent_at": &now,
	}

	if err := s.db.Model(&paymentReminder).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to mark payment reminder as sent: %w", err)
	}

	return &paymentReminder, nil
}

// CancelPaymentReminder cancels a scheduled payment reminder
func (s *PaymentReminderService) CancelPaymentReminder(id string) (*models.PaymentReminder, error) {
	var paymentReminder models.PaymentReminder

	// Check if payment reminder exists
	if err := s.db.First(&paymentReminder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("payment reminder not found")
		}
		return nil, fmt.Errorf("failed to fetch payment reminder: %w", err)
	}

	// Check if reminder can be cancelled
	if paymentReminder.Status == models.PaymentReminderStatusSent {
		return nil, fmt.Errorf("cannot cancel sent payment reminder")
	}

	if paymentReminder.Status == models.PaymentReminderStatusCancelled {
		return nil, fmt.Errorf("payment reminder is already cancelled")
	}

	// Update status
	if err := s.db.Model(&paymentReminder).Update("status", models.PaymentReminderStatusCancelled).Error; err != nil {
		return nil, fmt.Errorf("failed to cancel payment reminder: %w", err)
	}

	return &paymentReminder, nil
}

// GetDuePaymentReminders retrieves payment reminders that are due to be sent
func (s *PaymentReminderService) GetDuePaymentReminders(branchID string) ([]models.PaymentReminder, error) {
	var paymentReminders []models.PaymentReminder

	now := time.Now()
	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Where("branch_id = ? AND status = ? AND scheduled_date <= ?",
			branchID, models.PaymentReminderStatusScheduled, now).
		Order("scheduled_date ASC").
		Find(&paymentReminders).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch due payment reminders: %w", err)
	}

	return paymentReminders, nil
}

// GetUpcomingPaymentReminders retrieves upcoming payment reminders
func (s *PaymentReminderService) GetUpcomingPaymentReminders(branchID string, days int) ([]models.PaymentReminder, error) {
	var paymentReminders []models.PaymentReminder

	now := time.Now()
	futureDate := now.AddDate(0, 0, days)

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Where("branch_id = ? AND status = ? AND scheduled_date BETWEEN ? AND ?",
			branchID, models.PaymentReminderStatusScheduled, now, futureDate).
		Order("scheduled_date ASC").
		Find(&paymentReminders).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch upcoming payment reminders: %w", err)
	}

	return paymentReminders, nil
}

// CreateAutomaticReminders creates automatic payment reminders for overdue invoices
func (s *PaymentReminderService) CreateAutomaticReminders(branchID string, reminderType string, daysAfterDue int) ([]models.PaymentReminder, error) {
	var createdReminders []models.PaymentReminder

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get overdue invoices that don't have reminders of this type
		var invoices []models.Invoice
		cutoffDate := time.Now().AddDate(0, 0, -daysAfterDue)

		if err := tx.Preload("Customer").
			Where("branch_id = ? AND status IN ? AND due_date <= ? AND id NOT IN (?)",
				branchID,
				[]models.InvoiceStatus{models.InvoiceStatusSent, models.InvoiceStatusPartiallyPaid, models.InvoiceStatusOverdue},
				cutoffDate,
				tx.Model(&models.PaymentReminder{}).
					Select("invoice_id").
					Where("type = ? AND status != ?", reminderType, models.PaymentReminderStatusCancelled),
			).Find(&invoices).Error; err != nil {
			return fmt.Errorf("failed to fetch overdue invoices: %w", err)
		}

		// Create reminders for each overdue invoice
		for _, invoice := range invoices {
			reminder := &models.PaymentReminder{
				BranchID:      branchID, // TODO: Update parameter to use branchID
				CustomerID:    invoice.CustomerID,
				InvoiceID:     invoice.ID,
				Type:          reminderType,
				Status:        models.PaymentReminderStatusScheduled,
				ScheduledDate: time.Now(),
				Subject:       fmt.Sprintf("Payment Reminder: Invoice %s", invoice.InvoiceNumber),
				Message:       fmt.Sprintf("This is a reminder that your payment for Invoice %s is overdue. Please make payment at your earliest convenience.", invoice.InvoiceNumber),
			}

			if err := tx.Create(reminder).Error; err != nil {
				return fmt.Errorf("failed to create automatic reminder for invoice %s: %w", invoice.ID, err)
			}

			createdReminders = append(createdReminders, *reminder)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return createdReminders, nil
}

// GetPaymentReminderSummary gets summary statistics for payment reminders
func (s *PaymentReminderService) GetPaymentReminderSummary(branchID string) (*PaymentReminderSummary, error) {
	var summary PaymentReminderSummary

	// Get total count
	if err := s.db.Model(&models.PaymentReminder{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalReminders).Error; err != nil {
		return nil, fmt.Errorf("failed to count payment reminders: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := s.db.Model(&models.PaymentReminder{}).
		Where("branch_id = ?", branchID).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.PaymentReminderStatusScheduled):
			summary.ScheduledReminders = sc.Count
		case string(models.PaymentReminderStatusSent):
			summary.SentReminders = sc.Count
		case string(models.PaymentReminderStatusCancelled):
			summary.CancelledReminders = sc.Count
		}
	}

	// Get type counts
	typeCounts := []struct {
		Type  string
		Count int64
	}{}

	if err := s.db.Model(&models.PaymentReminder{}).
		Where("branch_id = ?", branchID).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get type counts: %w", err)
	}

	// Map type counts
	summary.TypeCounts = make(map[string]int64)
	for _, tc := range typeCounts {
		summary.TypeCounts[tc.Type] = tc.Count
	}

	// Get due reminders count
	now := time.Now()
	if err := s.db.Model(&models.PaymentReminder{}).
		Where("branch_id = ? AND status = ? AND scheduled_date <= ?",
			branchID, models.PaymentReminderStatusScheduled, now).
		Count(&summary.DueReminders).Error; err != nil {
		return nil, fmt.Errorf("failed to count due reminders: %w", err)
	}

	return &summary, nil
}

// ValidatePaymentReminder validates payment reminder data
func (s *PaymentReminderService) ValidatePaymentReminder(paymentReminder *models.PaymentReminder) error {
	if paymentReminder.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if paymentReminder.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if paymentReminder.InvoiceID == "" {
		return fmt.Errorf("invoice ID is required")
	}
	if paymentReminder.Type == "" {
		return fmt.Errorf("reminder type is required")
	}
	if paymentReminder.ScheduledDate.IsZero() {
		return fmt.Errorf("scheduled date is required")
	}
	if paymentReminder.ScheduledDate.Before(time.Now().AddDate(0, 0, -1)) {
		return fmt.Errorf("scheduled date cannot be in the past")
	}

	// Validate status
	validStatuses := []models.PaymentReminderStatus{
		models.PaymentReminderStatusScheduled,
		models.PaymentReminderStatusSent,
		models.PaymentReminderStatusCancelled,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if paymentReminder.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", paymentReminder.Status)
	}

	// Validate type
	validTypes := []string{
		"payment_due", "overdue_notice", "final_notice",
		"courtesy_reminder", "follow_up", "custom",
	}
	isValidType := false
	for _, validType := range validTypes {
		if paymentReminder.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid reminder type: %s", paymentReminder.Type)
	}

	return nil
}

// PaymentReminderSummary represents summary statistics for payment reminders
type PaymentReminderSummary struct {
	TotalReminders     int64            `json:"totalReminders"`
	ScheduledReminders int64            `json:"scheduledReminders"`
	SentReminders      int64            `json:"sentReminders"`
	CancelledReminders int64            `json:"cancelledReminders"`
	DueReminders       int64            `json:"dueReminders"`
	TypeCounts         map[string]int64 `json:"typeCounts"`
}
