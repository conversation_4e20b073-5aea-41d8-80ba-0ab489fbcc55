package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BankTransactionService handles bank transaction-related business logic
type BankTransactionService struct {
	db *gorm.DB
}

// NewBankTransactionService creates a new BankTransactionService
func NewBankTransactionService(db *gorm.DB) *BankTransactionService {
	return &BankTransactionService{db: db}
}

// GetAllBankTransactions retrieves all bank transactions with pagination
func (s *BankTransactionService) GetAllBankTransactions(page, limit int, search string) ([]models.BankTransaction, int64, error) {
	var transactions []models.BankTransaction
	var total int64

	query := s.db.Model(&models.BankTransaction{})

	// Apply search filter if provided
	if search != "" {
		query = query.Where("description ILIKE ? OR reference ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank transactions: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("BankAccount").Preload("BankAccount.Merchant").
		Offset(offset).Limit(limit).
		Order("date DESC, created_at DESC").
		Find(&transactions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank transactions: %w", err)
	}

	return transactions, total, nil
}

// GetBankTransactionByID retrieves a bank transaction by ID
func (s *BankTransactionService) GetBankTransactionByID(id string) (*models.BankTransaction, error) {
	var transaction models.BankTransaction
	
	if err := s.db.Preload("BankAccount").Preload("BankAccount.Merchant").Preload("Matches").
		First(&transaction, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank transaction not found")
		}
		return nil, fmt.Errorf("failed to fetch bank transaction: %w", err)
	}

	return &transaction, nil
}

// GetBankTransactionsByBankAccount retrieves bank transactions for a specific bank account
func (s *BankTransactionService) GetBankTransactionsByBankAccount(bankAccountID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.BankTransaction, int64, error) {
	var transactions []models.BankTransaction
	var total int64

	query := s.db.Model(&models.BankTransaction{}).Where("bank_account_id = ?", bankAccountID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("description ILIKE ? OR reference ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank transactions: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("BankAccount").Preload("Matches").
		Offset(offset).Limit(limit).
		Order("date DESC, created_at DESC").
		Find(&transactions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank transactions: %w", err)
	}

	return transactions, total, nil
}

// GetBankTransactionsByMerchant retrieves bank transactions for a specific merchant
func (s *BankTransactionService) GetBankTransactionsByMerchant(branchID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.BankTransaction, int64, error) {
	var transactions []models.BankTransaction
	var total int64

	// Join with bank_accounts to filter by merchant
	query := s.db.Model(&models.BankTransaction{}).
		Joins("JOIN bank_accounts ON bank_transactions.bank_account_id = bank_accounts.id").
		Where("bank_accounts.branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("bank_transactions.description ILIKE ? OR bank_transactions.reference ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("bank_transactions.status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("bank_transactions.date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("bank_transactions.date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank transactions: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("BankAccount").Preload("BankAccount.Merchant").Preload("Matches").
		Offset(offset).Limit(limit).
		Order("bank_transactions.date DESC, bank_transactions.created_at DESC").
		Find(&transactions).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank transactions: %w", err)
	}

	return transactions, total, nil
}

// CreateBankTransaction creates a new bank transaction
func (s *BankTransactionService) CreateBankTransaction(transaction *models.BankTransaction) error {
	// Validate bank account exists
	var bankAccount models.BankAccount
	if err := s.db.First(&bankAccount, "id = ?", transaction.BankAccountID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("bank account not found")
		}
		return fmt.Errorf("failed to validate bank account: %w", err)
	}

	// Set default status if not provided
	if transaction.Status == "" {
		transaction.Status = models.BankTransactionStatusUnreconciled
	}

	// Create the bank transaction
	if err := s.db.Create(transaction).Error; err != nil {
		return fmt.Errorf("failed to create bank transaction: %w", err)
	}

	return nil
}

// CreateBankTransactions creates multiple bank transactions in a batch
func (s *BankTransactionService) CreateBankTransactions(transactions []*models.BankTransaction) error {
	if len(transactions) == 0 {
		return fmt.Errorf("no transactions provided")
	}

	// Validate all bank accounts exist
	bankAccountIDs := make([]string, 0, len(transactions))
	for _, transaction := range transactions {
		bankAccountIDs = append(bankAccountIDs, transaction.BankAccountID)
	}

	var count int64
	if err := s.db.Model(&models.BankAccount{}).Where("id IN ?", bankAccountIDs).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to validate bank accounts: %w", err)
	}

	// Check if all bank accounts exist
	uniqueAccountIDs := make(map[string]bool)
	for _, id := range bankAccountIDs {
		uniqueAccountIDs[id] = true
	}
	if int64(len(uniqueAccountIDs)) != count {
		return fmt.Errorf("one or more bank accounts not found")
	}

	// Set default status for transactions without status
	for _, transaction := range transactions {
		if transaction.Status == "" {
			transaction.Status = models.BankTransactionStatusUnreconciled
		}
	}

	// Create transactions in batch
	if err := s.db.CreateInBatches(transactions, 100).Error; err != nil {
		return fmt.Errorf("failed to create bank transactions: %w", err)
	}

	return nil
}

// UpdateBankTransaction updates an existing bank transaction
func (s *BankTransactionService) UpdateBankTransaction(id string, updates *models.BankTransaction) (*models.BankTransaction, error) {
	var transaction models.BankTransaction
	
	// Check if bank transaction exists
	if err := s.db.First(&transaction, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank transaction not found")
		}
		return nil, fmt.Errorf("failed to fetch bank transaction: %w", err)
	}

	// If updating bank account ID, validate the new bank account
	if updates.BankAccountID != "" && updates.BankAccountID != transaction.BankAccountID {
		var bankAccount models.BankAccount
		if err := s.db.First(&bankAccount, "id = ?", updates.BankAccountID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("bank account not found")
			}
			return nil, fmt.Errorf("failed to validate bank account: %w", err)
		}
	}

	// Update the bank transaction
	if err := s.db.Model(&transaction).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update bank transaction: %w", err)
	}

	// Fetch updated bank transaction with relationships
	if err := s.db.Preload("BankAccount").Preload("BankAccount.Merchant").Preload("Matches").
		First(&transaction, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated bank transaction: %w", err)
	}

	return &transaction, nil
}

// DeleteBankTransaction deletes a bank transaction
func (s *BankTransactionService) DeleteBankTransaction(id string) error {
	var transaction models.BankTransaction
	
	// Check if bank transaction exists
	if err := s.db.First(&transaction, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("bank transaction not found")
		}
		return fmt.Errorf("failed to fetch bank transaction: %w", err)
	}

	// Check if transaction is reconciled
	if transaction.Status == models.BankTransactionStatusReconciled {
		return fmt.Errorf("cannot delete reconciled bank transaction")
	}

	// Check if transaction has matches
	var matchCount int64
	if err := s.db.Model(&models.BankTransactionMatch{}).
		Where("bank_transaction_id = ?", id).Count(&matchCount).Error; err != nil {
		return fmt.Errorf("failed to check transaction matches: %w", err)
	}

	if matchCount > 0 {
		return fmt.Errorf("cannot delete bank transaction with existing matches")
	}

	// Delete the bank transaction
	if err := s.db.Delete(&transaction).Error; err != nil {
		return fmt.Errorf("failed to delete bank transaction: %w", err)
	}

	return nil
}

// ReconcileBankTransaction marks a bank transaction as reconciled
func (s *BankTransactionService) ReconcileBankTransaction(id string) (*models.BankTransaction, error) {
	var transaction models.BankTransaction
	
	// Check if bank transaction exists
	if err := s.db.First(&transaction, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank transaction not found")
		}
		return nil, fmt.Errorf("failed to fetch bank transaction: %w", err)
	}

	// Update status to reconciled
	transaction.Status = models.BankTransactionStatusReconciled
	if err := s.db.Save(&transaction).Error; err != nil {
		return nil, fmt.Errorf("failed to reconcile bank transaction: %w", err)
	}

	return &transaction, nil
}

// GetBankTransactionBalance calculates the running balance for a bank account up to a specific date
func (s *BankTransactionService) GetBankTransactionBalance(bankAccountID string, upToDate time.Time) (decimal.Decimal, error) {
	var result struct {
		Balance decimal.Decimal
	}

	if err := s.db.Model(&models.BankTransaction{}).
		Select("COALESCE(SUM(amount), 0) as balance").
		Where("bank_account_id = ? AND date <= ?", bankAccountID, upToDate).
		Scan(&result).Error; err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate balance: %w", err)
	}

	return result.Balance, nil
}

// ValidateBankTransaction validates bank transaction data
func (s *BankTransactionService) ValidateBankTransaction(transaction *models.BankTransaction) error {
	if transaction.BankAccountID == "" {
		return fmt.Errorf("bank account ID is required")
	}
	if transaction.Description == "" {
		return fmt.Errorf("description is required")
	}
	if transaction.Amount.IsZero() {
		return fmt.Errorf("amount cannot be zero")
	}
	if transaction.Date.IsZero() {
		return fmt.Errorf("transaction date is required")
	}

	return nil
}
