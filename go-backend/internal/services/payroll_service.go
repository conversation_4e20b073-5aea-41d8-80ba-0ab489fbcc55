package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type PayrollService struct {
	db *gorm.DB
}

func NewPayrollService(db *gorm.DB) *PayrollService {
	return &PayrollService{db: db}
}

// CreateEmployeeRequest represents a request to create an employee
type CreateEmployeeRequest struct {
	MerchantID       string                  `json:"merchantId" binding:"required"`
	EmployeeNumber   *string                 `json:"employeeNumber"`
	FirstName        string                  `json:"firstName" binding:"required"`
	LastName         string                  `json:"lastName" binding:"required"`
	Email            *string                 `json:"email"`
	Phone            *string                 `json:"phone"`
	Address          *string                 `json:"address"`
	JobTitle         *string                 `json:"jobTitle"`
	Department       *string                 `json:"department"`
	HireDate         time.Time               `json:"hireDate" binding:"required"`
	Salary           decimal.Decimal         `json:"salary" binding:"required"`
	PayFrequency     *string                 `json:"payFrequency"`
	Status           models.EmploymentStatus `json:"status"`
	TaxID            *string                 `json:"taxId"`
	BankAccount      *string                 `json:"bankAccount"`
	EmergencyContact *string                 `json:"emergencyContact"`
	Notes            *string                 `json:"notes"`
}

// UpdateEmployeeRequest represents a request to update an employee
type UpdateEmployeeRequest struct {
	FirstName        *string                  `json:"firstName"`
	LastName         *string                  `json:"lastName"`
	Email            *string                  `json:"email"`
	Phone            *string                  `json:"phone"`
	Address          *string                  `json:"address"`
	JobTitle         *string                  `json:"jobTitle"`
	Department       *string                  `json:"department"`
	Salary           *decimal.Decimal         `json:"salary"`
	PayFrequency     *string                  `json:"payFrequency"`
	Status           *models.EmploymentStatus `json:"status"`
	TaxID            *string                  `json:"taxId"`
	BankAccount      *string                  `json:"bankAccount"`
	EmergencyContact *string                  `json:"emergencyContact"`
	Notes            *string                  `json:"notes"`
}

// EmployeeResponse represents an employee response
type EmployeeResponse struct {
	ID               string                  `json:"id"`
	MerchantID       string                  `json:"merchantId"`
	EmployeeNumber   *string                 `json:"employeeNumber"`
	FirstName        string                  `json:"firstName"`
	LastName         string                  `json:"lastName"`
	FullName         string                  `json:"fullName"`
	Email            *string                 `json:"email"`
	Phone            *string                 `json:"phone"`
	Address          *string                 `json:"address"`
	JobTitle         *string                 `json:"jobTitle"`
	Department       *string                 `json:"department"`
	HireDate         string                  `json:"hireDate"`
	Salary           decimal.Decimal         `json:"salary"`
	PayFrequency     *string                 `json:"payFrequency"`
	Status           models.EmploymentStatus `json:"status"`
	TaxID            *string                 `json:"taxId"`
	BankAccount      *string                 `json:"bankAccount"`
	EmergencyContact *string                 `json:"emergencyContact"`
	Notes            *string                 `json:"notes"`
	CreatedAt        string                  `json:"createdAt"`
	UpdatedAt        string                  `json:"updatedAt"`
	Merchant         *models.Merchant        `json:"merchant,omitempty"`
}

// CreatePayrollRunRequest represents a request to create a payroll run
type CreatePayrollRunRequest struct {
	MerchantID string                  `json:"merchantId" binding:"required"`
	PayPeriod  string                  `json:"payPeriod" binding:"required"`
	StartDate  time.Time               `json:"startDate" binding:"required"`
	EndDate    time.Time               `json:"endDate" binding:"required"`
	PayDate    time.Time               `json:"payDate" binding:"required"`
	Status     models.PayrollRunStatus `json:"status"`
	Notes      *string                 `json:"notes"`
}

// PayrollRunResponse represents a payroll run response
type PayrollRunResponse struct {
	ID              string                  `json:"id"`
	MerchantID      string                  `json:"merchantId"`
	PayrollNumber   string                  `json:"payrollNumber"`
	PayPeriod       string                  `json:"payPeriod"`
	StartDate       string                  `json:"startDate"`
	EndDate         string                  `json:"endDate"`
	PayDate         string                  `json:"payDate"`
	Status          models.PayrollRunStatus `json:"status"`
	TotalGrossPay   decimal.Decimal         `json:"totalGrossPay"`
	TotalDeductions decimal.Decimal         `json:"totalDeductions"`
	TotalNetPay     decimal.Decimal         `json:"totalNetPay"`
	EmployeeCount   int                     `json:"employeeCount"`
	Notes           *string                 `json:"notes"`
	CreatedAt       string                  `json:"createdAt"`
	UpdatedAt       string                  `json:"updatedAt"`
	Merchant        *models.Merchant        `json:"merchant,omitempty"`
	PayrollDetails  []PayrollDetailResponse `json:"payrollDetails,omitempty"`
}

// PayrollDetailResponse represents a payroll detail response
type PayrollDetailResponse struct {
	ID              string            `json:"id"`
	PayrollRunID    string            `json:"payrollRunId"`
	EmployeeID      string            `json:"employeeId"`
	GrossPay        decimal.Decimal   `json:"grossPay"`
	TaxDeductions   decimal.Decimal   `json:"taxDeductions"`
	OtherDeductions decimal.Decimal   `json:"otherDeductions"`
	NetPay          decimal.Decimal   `json:"netPay"`
	HoursWorked     *decimal.Decimal  `json:"hoursWorked"`
	OvertimeHours   *decimal.Decimal  `json:"overtimeHours"`
	Notes           *string           `json:"notes"`
	CreatedAt       string            `json:"createdAt"`
	Employee        *EmployeeResponse `json:"employee,omitempty"`
}

// GetAllEmployees returns all employees with pagination
func (s *PayrollService) GetAllEmployees(page, limit int, userID string) ([]EmployeeResponse, int64, error) {
	var employees []models.Employee
	var total int64

	// Build query to get employees from merchants user has access to
	query := s.db.Model(&models.Employee{}).
		Joins("JOIN user_merchant_permissions ON employees.branch_id = user_merchant_permissions.branch_id").
		Where("user_merchant_permissions.user_id = ?", userID)

	// Count total employees
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get employees with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").
		Order("last_name ASC, first_name ASC").
		Offset(offset).Limit(limit).Find(&employees).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]EmployeeResponse, 0, len(employees))
	for _, employee := range employees {
		responses = append(responses, s.toEmployeeResponse(employee))
	}

	return responses, total, nil
}

// GetEmployeesByMerchant returns employees for a specific merchant
func (s *PayrollService) GetEmployeesByMerchant(branchID string, page, limit int, userID string) ([]EmployeeResponse, int64, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this merchant")
	}

	var employees []models.Employee
	var total int64

	// Count total employees for this merchant
	if err := s.db.Model(&models.Employee{}).Where("branch_id = ? AND status = ?", branchID, models.EmploymentStatusActive).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get employees with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ? AND status = ?", branchID, models.EmploymentStatusActive).
		Preload("Merchant").
		Order("last_name ASC, first_name ASC").
		Offset(offset).Limit(limit).Find(&employees).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]EmployeeResponse, 0, len(employees))
	for _, employee := range employees {
		responses = append(responses, s.toEmployeeResponse(employee))
	}

	return responses, total, nil
}

// GetEmployeeByID returns an employee by ID
func (s *PayrollService) GetEmployeeByID(id, userID string) (*EmployeeResponse, error) {
	var employee models.Employee

	// Get employee and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON employees.branch_id = user_merchant_permissions.branch_id").
		Where("employees.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		Preload("Merchant").
		First(&employee).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("employee not found or access denied")
		}
		return nil, err
	}

	response := s.toEmployeeResponse(employee)
	return &response, nil
}

// CreateEmployee creates a new employee
func (s *PayrollService) CreateEmployee(req CreateEmployeeRequest, userID string) (*EmployeeResponse, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, req.MerchantID) {
		return nil, errors.New("access denied to this merchant")
	}

	// Check if employee number already exists for this merchant
	var existingEmployee models.Employee
	if err := s.db.Where("branch_id = ? AND employee_number = ?", req.MerchantID, req.EmployeeNumber).First(&existingEmployee).Error; err == nil {
		return nil, errors.New("employee number already exists for this merchant")
	}

	// Check if email already exists for this merchant
	if err := s.db.Where("branch_id = ? AND email = ?", req.MerchantID, req.Email).First(&existingEmployee).Error; err == nil {
		return nil, errors.New("email already exists for this merchant")
	}

	// Validate salary
	if req.Salary.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("salary must be greater than zero")
	}

	// Set default status if not provided
	status := req.Status
	if status == "" {
		status = models.EmploymentStatusActive
	}

	// Create employee
	employee := models.Employee{
		ID:               uuid.New().String(),
		BranchID:         req.MerchantID, // TODO: Update request struct to use BranchID
		EmployeeNumber:   req.EmployeeNumber,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		Email:            req.Email,
		Phone:            req.Phone,
		Address:          req.Address,
		JobTitle:         req.JobTitle,
		Department:       req.Department,
		HireDate:         req.HireDate,
		Salary:           req.Salary,
		PayFrequency:     req.PayFrequency,
		Status:           status,
		TaxID:            req.TaxID,
		BankAccount:      req.BankAccount,
		EmergencyContact: req.EmergencyContact,
		Notes:            req.Notes,
	}

	if err := s.db.Create(&employee).Error; err != nil {
		return nil, err
	}

	// Reload employee with relationships
	if err := s.db.Preload("Merchant").
		Where("id = ?", employee.ID).First(&employee).Error; err != nil {
		return nil, err
	}

	response := s.toEmployeeResponse(employee)
	return &response, nil
}

// CreatePayrollRun creates a new payroll run
func (s *PayrollService) CreatePayrollRun(req CreatePayrollRunRequest, userID string) (*PayrollRunResponse, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, req.MerchantID) {
		return nil, errors.New("access denied to this merchant")
	}

	// Validate dates
	if req.EndDate.Before(req.StartDate) {
		return nil, errors.New("end date must be after start date")
	}
	if req.PayDate.Before(req.EndDate) {
		return nil, errors.New("pay date must be after end date")
	}

	// Generate payroll number
	payrollNumber := s.generatePayrollNumber(req.MerchantID)

	// Set default status if not provided
	status := req.Status
	if status == "" {
		status = models.PayrollRunStatusDraft
	}

	// Create payroll run
	payrollRun := models.PayrollRun{
		ID:             uuid.New().String(),
		BranchID:       req.MerchantID, // TODO: Update request struct to use BranchID
		RunNumber:      payrollNumber,
		PayPeriodStart: req.StartDate,
		PayPeriodEnd:   req.EndDate,
		PayDate:        req.PayDate,
		Status:         status,
		Notes:          req.Notes,
	}

	if err := s.db.Create(&payrollRun).Error; err != nil {
		return nil, err
	}

	// If status is not draft, generate payroll details for all active employees
	if status != models.PayrollRunStatusDraft {
		if err := s.generatePayrollDetails(payrollRun.ID, req.MerchantID); err != nil {
			return nil, err
		}
	}

	// Reload payroll run with relationships
	if err := s.db.Preload("Merchant").Preload("PayrollDetails").Preload("PayrollDetails.Employee").
		Where("id = ?", payrollRun.ID).First(&payrollRun).Error; err != nil {
		return nil, err
	}

	response := s.toPayrollRunResponse(payrollRun)
	return &response, nil
}

// Helper methods
func (s *PayrollService) hasMerchantAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *PayrollService) generatePayrollNumber(branchID string) string {
	var count int64
	s.db.Model(&models.PayrollRun{}).Where("branch_id = ?", branchID).Count(&count)
	return "PR-" + branchID[:8] + "-" + fmt.Sprintf("%06d", count+1)
}

func (s *PayrollService) generatePayrollDetails(payrollRunID, branchID string) error {
	// Get all active employees for this merchant
	var employees []models.Employee
	if err := s.db.Where("branch_id = ? AND status = ?", branchID, models.EmploymentStatusActive).Find(&employees).Error; err != nil {
		return err
	}

	// Create payroll details for each employee
	for _, employee := range employees {
		// Calculate gross pay based on pay frequency
		grossPay := s.calculateGrossPay(employee.Salary, employee.PayFrequency)

		// Calculate basic tax deductions (simplified - in real world, this would be more complex)
		taxDeductions := grossPay.Mul(decimal.NewFromFloat(0.2)) // 20% tax rate

		// Net pay = gross pay - deductions
		netPay := grossPay.Sub(taxDeductions)

		payrollDetail := models.PayrollDetail{
			ID:           uuid.New().String(),
			BranchID:     employee.BranchID,
			PayrollRunID: payrollRunID,
			EmployeeID:   employee.ID,
			GrossPay:     grossPay,
			NetPay:       netPay,
			FederalTax:   taxDeductions,
			Deductions:   decimal.Zero,
		}

		if err := s.db.Create(&payrollDetail).Error; err != nil {
			return err
		}
	}

	return nil
}

func (s *PayrollService) calculateGrossPay(annualSalary decimal.Decimal, payFrequency *string) decimal.Decimal {
	if payFrequency == nil {
		return annualSalary.Div(decimal.NewFromInt(12)) // Default to monthly
	}

	switch *payFrequency {
	case "Weekly":
		return annualSalary.Div(decimal.NewFromInt(52))
	case "Bi-weekly":
		return annualSalary.Div(decimal.NewFromInt(26))
	case "Monthly":
		return annualSalary.Div(decimal.NewFromInt(12))
	case "Semi-monthly":
		return annualSalary.Div(decimal.NewFromInt(24))
	default:
		return annualSalary.Div(decimal.NewFromInt(12)) // Default to monthly
	}
}

// toEmployeeResponse converts an Employee model to EmployeeResponse
func (s *PayrollService) toEmployeeResponse(employee models.Employee) EmployeeResponse {
	return EmployeeResponse{
		ID:               employee.ID,
		MerchantID:       employee.BranchID, // TODO: Update response struct to use BranchID
		EmployeeNumber:   employee.EmployeeNumber,
		FirstName:        employee.FirstName,
		LastName:         employee.LastName,
		FullName:         employee.FirstName + " " + employee.LastName,
		Email:            employee.Email,
		Phone:            employee.Phone,
		Address:          employee.Address,
		JobTitle:         employee.JobTitle,
		Department:       employee.Department,
		HireDate:         employee.HireDate.Format("2006-01-02T15:04:05Z"),
		Salary:           employee.Salary,
		PayFrequency:     employee.PayFrequency,
		Status:           employee.Status,
		TaxID:            employee.TaxID,
		BankAccount:      employee.BankAccount,
		EmergencyContact: employee.EmergencyContact,
		Notes:            employee.Notes,
		CreatedAt:        employee.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:        employee.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Merchant:         nil, // TODO: Update response struct to use Branch instead of Merchant
	}
}

// toPayrollRunResponse converts a PayrollRun model to PayrollRunResponse
func (s *PayrollService) toPayrollRunResponse(payrollRun models.PayrollRun) PayrollRunResponse {
	response := PayrollRunResponse{
		ID:            payrollRun.ID,
		MerchantID:    payrollRun.BranchID, // TODO: Update response struct to use BranchID
		PayrollNumber: payrollRun.RunNumber,
		PayPeriod:     "Custom", // Since the model doesn't have PayPeriod field
		StartDate:     payrollRun.PayPeriodStart.Format("2006-01-02T15:04:05Z"),
		EndDate:       payrollRun.PayPeriodEnd.Format("2006-01-02T15:04:05Z"),
		PayDate:       payrollRun.PayDate.Format("2006-01-02T15:04:05Z"),
		Status:        payrollRun.Status,
		Notes:         payrollRun.Notes,
		CreatedAt:     payrollRun.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:     payrollRun.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Merchant:      nil, // TODO: Update response struct to use Branch instead of Merchant
	}

	// Calculate totals from payroll details
	var totalGrossPay, totalDeductions, totalNetPay decimal.Decimal
	var employeeCount int

	for _, detail := range payrollRun.Details {
		totalGrossPay = totalGrossPay.Add(detail.GrossPay)
		totalDeductions = totalDeductions.Add(detail.FederalTax).Add(detail.StateTax).Add(detail.Deductions)
		totalNetPay = totalNetPay.Add(detail.NetPay)
		employeeCount++

		// Convert to response format
		detailResponse := PayrollDetailResponse{
			ID:              detail.ID,
			PayrollRunID:    detail.PayrollRunID,
			EmployeeID:      detail.EmployeeID,
			GrossPay:        detail.GrossPay,
			TaxDeductions:   detail.FederalTax.Add(detail.StateTax),
			OtherDeductions: detail.Deductions,
			NetPay:          detail.NetPay,
			HoursWorked:     detail.HoursWorked,
			OvertimeHours:   detail.OvertimeHours,
			Notes:           detail.Notes,
			CreatedAt:       detail.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}

		if detail.Employee.ID != "" {
			employeeResponse := s.toEmployeeResponse(detail.Employee)
			detailResponse.Employee = &employeeResponse
		}

		response.PayrollDetails = append(response.PayrollDetails, detailResponse)
	}

	response.TotalGrossPay = totalGrossPay
	response.TotalDeductions = totalDeductions
	response.TotalNetPay = totalNetPay
	response.EmployeeCount = employeeCount

	return response
}
