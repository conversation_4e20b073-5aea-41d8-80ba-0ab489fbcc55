package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type JournalEntryService struct {
	db *gorm.DB
}

func NewJournalEntryService(db *gorm.DB) *JournalEntryService {
	return &JournalEntryService{db: db}
}

// CreateJournalEntryRequest represents a request to create a journal entry
type CreateJournalEntryRequest struct {
	MerchantID  string                          `json:"merchantId" binding:"required"`
	Date        time.Time                       `json:"date" binding:"required"`
	Description string                          `json:"description" binding:"required"`
	Reference   *string                         `json:"reference"`
	Lines       []CreateJournalEntryLineRequest `json:"lines" binding:"required,min=2"`
}

// CreateJournalEntryLineRequest represents a journal entry line
type CreateJournalEntryLineRequest struct {
	AccountID   string                      `json:"accountId" binding:"required"`
	Type        models.JournalEntryLineType `json:"type" binding:"required"`
	Amount      decimal.Decimal             `json:"amount" binding:"required"`
	Description *string                     `json:"description"`
}

// UpdateJournalEntryRequest represents a request to update a journal entry
type UpdateJournalEntryRequest struct {
	Date        *time.Time                      `json:"date"`
	Description *string                         `json:"description"`
	Reference   *string                         `json:"reference"`
	Lines       []CreateJournalEntryLineRequest `json:"lines"`
}

// JournalEntryResponse represents a journal entry response
type JournalEntryResponse struct {
	ID          string                        `json:"id"`
	MerchantID  string                        `json:"merchantId"`
	EntryNumber string                        `json:"entryNumber"`
	Date        string                        `json:"date"`
	Description string                        `json:"description"`
	Reference   *string                       `json:"reference"`
	SourceType  models.JournalEntrySourceType `json:"sourceType"`
	TotalAmount decimal.Decimal               `json:"totalAmount"`
	CreatedAt   string                        `json:"createdAt"`
	UpdatedAt   string                        `json:"updatedAt"`
	Lines       []models.JournalEntryLine     `json:"lines,omitempty"`
}

// GetAllJournalEntries returns all journal entries with pagination
func (s *JournalEntryService) GetAllJournalEntries(page, limit int, userID string) ([]JournalEntryResponse, int64, error) {
	var entries []models.JournalEntry
	var total int64

	// Build query to get journal entries from merchants user has access to
	query := s.db.Model(&models.JournalEntry{}).
		Joins("JOIN user_merchant_permissions ON journal_entries.branch_id = user_merchant_permissions.branch_id").
		Where("user_merchant_permissions.user_id = ?", userID)

	// Count total entries
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get entries with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Lines").Preload("Lines.Account").
		Order("date DESC, entry_number DESC").
		Offset(offset).Limit(limit).Find(&entries).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]JournalEntryResponse, 0, len(entries))
	for _, entry := range entries {
		responses = append(responses, s.toJournalEntryResponse(entry))
	}

	return responses, total, nil
}

// GetJournalEntriesByMerchant returns journal entries for a specific merchant
func (s *JournalEntryService) GetJournalEntriesByMerchant(branchID string, page, limit int, userID string) ([]JournalEntryResponse, int64, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this merchant")
	}

	var entries []models.JournalEntry
	var total int64

	// Count total entries for this merchant
	if err := s.db.Model(&models.JournalEntry{}).Where("branch_id = ?", branchID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get entries with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ?", branchID).
		Preload("Lines").Preload("Lines.Account").
		Order("date DESC, entry_number DESC").
		Offset(offset).Limit(limit).Find(&entries).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]JournalEntryResponse, 0, len(entries))
	for _, entry := range entries {
		responses = append(responses, s.toJournalEntryResponse(entry))
	}

	return responses, total, nil
}

// GetJournalEntryByID returns a journal entry by ID
func (s *JournalEntryService) GetJournalEntryByID(id, userID string) (*JournalEntryResponse, error) {
	var entry models.JournalEntry

	// Get entry and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON journal_entries.branch_id = user_merchant_permissions.branch_id").
		Where("journal_entries.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		Preload("Lines").Preload("Lines.Account").
		First(&entry).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("journal entry not found or access denied")
		}
		return nil, err
	}

	response := s.toJournalEntryResponse(entry)
	return &response, nil
}

// CreateJournalEntry creates a new journal entry
func (s *JournalEntryService) CreateJournalEntry(req CreateJournalEntryRequest, userID string) (*JournalEntryResponse, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, req.MerchantID) {
		return nil, errors.New("access denied to this merchant")
	}

	// Validate that we have at least 2 lines
	if len(req.Lines) < 2 {
		return nil, errors.New("journal entry must have at least 2 lines")
	}

	// Validate that we have both debits and credits
	var totalDebits, totalCredits decimal.Decimal
	for _, line := range req.Lines {
		// Validate amount is positive
		if line.Amount.LessThanOrEqual(decimal.Zero) {
			return nil, errors.New("journal entry line amount must be positive")
		}

		if line.Type == models.JournalEntryLineTypeDebit {
			totalDebits = totalDebits.Add(line.Amount)
		} else if line.Type == models.JournalEntryLineTypeCredit {
			totalCredits = totalCredits.Add(line.Amount)
		} else {
			return nil, errors.New("invalid journal entry line type")
		}

		// Verify account exists and belongs to merchant
		var account models.ChartOfAccount
		if err := s.db.Where("id = ? AND branch_id = ? AND is_active = ?", line.AccountID, req.MerchantID, true).First(&account).Error; err != nil {
			return nil, fmt.Errorf("account %s not found or does not belong to this merchant", line.AccountID)
		}
	}

	// Ensure debits equal credits
	if !totalDebits.Equal(totalCredits) {
		return nil, errors.New("total debits must equal total credits")
	}

	// Generate entry number
	entryNumber := s.generateEntryNumber(req.MerchantID)

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create journal entry
	entry := models.JournalEntry{
		ID:          uuid.New().String(),
		BranchID:    req.MerchantID, // TODO: Update request struct to use BranchID
		EntryNumber: entryNumber,
		Date:        req.Date,
		Description: req.Description,
		Reference:   req.Reference,
		SourceType:  models.JournalEntrySourceManual,
		TotalAmount: totalDebits, // Use total debits as total amount
		CreatedByID: userID,
	}

	if err := tx.Create(&entry).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create journal entry lines
	for i, lineReq := range req.Lines {
		line := models.JournalEntryLine{
			ID:             uuid.New().String(),
			JournalEntryID: entry.ID,
			AccountID:      lineReq.AccountID,
			Type:           lineReq.Type,
			Amount:         lineReq.Amount,
			Description:    lineReq.Description,
			SortOrder:      i + 1,
		}

		if err := tx.Create(&line).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload entry with relationships
	if err := s.db.Preload("Lines").Preload("Lines.Account").
		Where("id = ?", entry.ID).First(&entry).Error; err != nil {
		return nil, err
	}

	response := s.toJournalEntryResponse(entry)
	return &response, nil
}

// UpdateJournalEntry updates an existing journal entry
func (s *JournalEntryService) UpdateJournalEntry(id string, req UpdateJournalEntryRequest, userID string) (*JournalEntryResponse, error) {
	var entry models.JournalEntry

	// Get entry and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON journal_entries.branch_id = user_merchant_permissions.branch_id").
		Where("journal_entries.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&entry).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("journal entry not found or access denied")
		}
		return nil, err
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update basic fields
	updates := make(map[string]interface{})
	if req.Date != nil {
		updates["date"] = *req.Date
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Reference != nil {
		updates["reference"] = *req.Reference
	}

	// Update lines if provided
	if len(req.Lines) > 0 {
		// Validate lines
		if len(req.Lines) < 2 {
			tx.Rollback()
			return nil, errors.New("journal entry must have at least 2 lines")
		}

		// Validate that we have both debits and credits
		var totalDebits, totalCredits decimal.Decimal
		for _, line := range req.Lines {
			// Validate amount is positive
			if line.Amount.LessThanOrEqual(decimal.Zero) {
				tx.Rollback()
				return nil, errors.New("journal entry line amount must be positive")
			}

			if line.Type == models.JournalEntryLineTypeDebit {
				totalDebits = totalDebits.Add(line.Amount)
			} else if line.Type == models.JournalEntryLineTypeCredit {
				totalCredits = totalCredits.Add(line.Amount)
			} else {
				tx.Rollback()
				return nil, errors.New("invalid journal entry line type")
			}

			// Verify account exists and belongs to merchant
			var account models.ChartOfAccount
			if err := tx.Where("id = ? AND branch_id = ? AND is_active = ?", line.AccountID, entry.BranchID, true).First(&account).Error; err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("account %s not found or does not belong to this branch", line.AccountID)
			}
		}

		if !totalDebits.Equal(totalCredits) {
			tx.Rollback()
			return nil, errors.New("total debits must equal total credits")
		}

		// Delete existing lines
		if err := tx.Where("journal_entry_id = ?", id).Delete(&models.JournalEntryLine{}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// Create new lines
		for i, lineReq := range req.Lines {
			line := models.JournalEntryLine{
				ID:             uuid.New().String(),
				JournalEntryID: id,
				AccountID:      lineReq.AccountID,
				Type:           lineReq.Type,
				Amount:         lineReq.Amount,
				Description:    lineReq.Description,
				SortOrder:      i + 1,
			}

			if err := tx.Create(&line).Error; err != nil {
				tx.Rollback()
				return nil, err
			}
		}

		// Update total amount
		updates["total_amount"] = totalDebits
	}

	// Apply updates
	if len(updates) > 0 {
		if err := tx.Model(&entry).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload entry with relationships
	if err := s.db.Preload("Lines").Preload("Lines.Account").
		Where("id = ?", id).First(&entry).Error; err != nil {
		return nil, err
	}

	response := s.toJournalEntryResponse(entry)
	return &response, nil
}

// DeleteJournalEntry deletes a journal entry
func (s *JournalEntryService) DeleteJournalEntry(id, userID string) error {
	var entry models.JournalEntry

	// Get entry and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON journal_entries.branch_id = user_merchant_permissions.branch_id").
		Where("journal_entries.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&entry).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("journal entry not found or access denied")
		}
		return err
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete journal entry lines first
	if err := tx.Where("journal_entry_id = ?", id).Delete(&models.JournalEntryLine{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete journal entry
	if err := tx.Delete(&entry).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Helper methods
func (s *JournalEntryService) hasMerchantAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *JournalEntryService) generateEntryNumber(branchID string) string {
	var count int64
	s.db.Model(&models.JournalEntry{}).Where("branch_id = ?", branchID).Count(&count)
	return "JE-" + branchID[:8] + "-" + fmt.Sprintf("%06d", count+1)
}

// toJournalEntryResponse converts a JournalEntry model to JournalEntryResponse
func (s *JournalEntryService) toJournalEntryResponse(entry models.JournalEntry) JournalEntryResponse {
	return JournalEntryResponse{
		ID:          entry.ID,
		MerchantID:  entry.BranchID, // TODO: Update response struct to use BranchID
		EntryNumber: entry.EntryNumber,
		Date:        entry.Date.Format("2006-01-02T15:04:05Z"),
		Description: entry.Description,
		Reference:   entry.Reference,
		SourceType:  entry.SourceType,
		TotalAmount: entry.TotalAmount,
		CreatedAt:   entry.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   entry.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Lines:       entry.Lines,
	}
}
