package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CustomerStatementService handles customer statement-related business logic
type CustomerStatementService struct {
	db *gorm.DB
}

// NewCustomerStatementService creates a new CustomerStatementService
func NewCustomerStatementService(db *gorm.DB) *CustomerStatementService {
	return &CustomerStatementService{db: db}
}

// GetAllCustomerStatements retrieves all customer statements with pagination
func (s *CustomerStatementService) GetAllCustomerStatements(page, limit int, search string) ([]models.CustomerStatement, int64, error) {
	var customerStatements []models.CustomerStatement
	var total int64

	query := s.db.Model(&models.CustomerStatement{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON customer_statements.customer_id = customers.id").
			Where("customers.name ILIKE ? OR customers.email ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer statements: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Items").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&customerStatements).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer statements: %w", err)
	}

	return customerStatements, total, nil
}

// GetCustomerStatementByID retrieves a customer statement by ID
func (s *CustomerStatementService) GetCustomerStatementByID(id string) (*models.CustomerStatement, error) {
	var customerStatement models.CustomerStatement

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Items").
		First(&customerStatement, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("customer statement not found")
		}
		return nil, fmt.Errorf("failed to fetch customer statement: %w", err)
	}

	return &customerStatement, nil
}

// GetCustomerStatementsByMerchant retrieves customer statements for a specific merchant
func (s *CustomerStatementService) GetCustomerStatementsByMerchant(branchID string, page, limit int, search, customerID string, startDate, endDate *time.Time) ([]models.CustomerStatement, int64, error) {
	var customerStatements []models.CustomerStatement
	var total int64

	query := s.db.Model(&models.CustomerStatement{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON customer_statements.customer_id = customers.id").
			Where("customers.name ILIKE ? OR customers.email ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply customer filter if provided
	if customerID != "" {
		query = query.Where("customer_id = ?", customerID)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("start_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("end_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer statements: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Items").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&customerStatements).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer statements: %w", err)
	}

	return customerStatements, total, nil
}

// GetCustomerStatementsByCustomer retrieves customer statements for a specific customer
func (s *CustomerStatementService) GetCustomerStatementsByCustomer(customerID string, page, limit int) ([]models.CustomerStatement, int64, error) {
	var customerStatements []models.CustomerStatement
	var total int64

	query := s.db.Model(&models.CustomerStatement{}).Where("customer_id = ?", customerID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer statements: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Items").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&customerStatements).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer statements: %w", err)
	}

	return customerStatements, total, nil
}

// CreateCustomerStatement creates a new customer statement
func (s *CustomerStatementService) CreateCustomerStatement(customerStatement *models.CustomerStatement) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", customerStatement.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate customer exists and belongs to branch
		var customer models.Customer
		if err := tx.First(&customer, "id = ? AND branch_id = ?", customerStatement.CustomerID, customerStatement.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate customer: %w", err)
		}

		// Validate customer statement
		if err := s.ValidateCustomerStatement(customerStatement); err != nil {
			return err
		}

		// Create the customer statement
		if err := tx.Create(customerStatement).Error; err != nil {
			return fmt.Errorf("failed to create customer statement: %w", err)
		}

		return nil
	})
}

// UpdateCustomerStatement updates an existing customer statement
func (s *CustomerStatementService) UpdateCustomerStatement(id string, updates *models.CustomerStatement) (*models.CustomerStatement, error) {
	var customerStatement models.CustomerStatement

	// Check if customer statement exists
	if err := s.db.First(&customerStatement, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("customer statement not found")
		}
		return nil, fmt.Errorf("failed to fetch customer statement: %w", err)
	}

	var updatedStatement *models.CustomerStatement
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Update the customer statement
		if err := tx.Model(&customerStatement).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update customer statement: %w", err)
		}

		// Fetch updated customer statement with relationships
		if err := tx.Preload("Branch").Preload("Customer").Preload("Items").
			First(&customerStatement, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated customer statement: %w", err)
		}

		updatedStatement = &customerStatement
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedStatement, nil
}

// DeleteCustomerStatement deletes a customer statement
func (s *CustomerStatementService) DeleteCustomerStatement(id string) error {
	var customerStatement models.CustomerStatement

	// Check if customer statement exists
	if err := s.db.First(&customerStatement, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("customer statement not found")
		}
		return fmt.Errorf("failed to fetch customer statement: %w", err)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete statement items first
		if err := tx.Where("statement_id = ?", id).Delete(&models.StatementItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete statement items: %w", err)
		}

		// Delete the customer statement
		if err := tx.Delete(&customerStatement).Error; err != nil {
			return fmt.Errorf("failed to delete customer statement: %w", err)
		}

		return nil
	})
}

// GenerateCustomerStatement generates a customer statement for a specific period
func (s *CustomerStatementService) GenerateCustomerStatement(branchID, customerID string, startDate, endDate time.Time) (*models.CustomerStatement, error) {
	var generatedStatement *models.CustomerStatement

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", branchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate customer exists and belongs to branch
		var customer models.Customer
		if err := tx.First(&customer, "id = ? AND branch_id = ?", customerID, branchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate customer: %w", err)
		}

		// Validate date range
		if startDate.After(endDate) || startDate.Equal(endDate) {
			return fmt.Errorf("start date must be before end date")
		}

		// Check if statement already exists for this period
		var existingStatement models.CustomerStatement
		if err := tx.First(&existingStatement, "branch_id = ? AND customer_id = ? AND start_date = ? AND end_date = ?",
			branchID, customerID, startDate, endDate).Error; err == nil {
			return fmt.Errorf("statement already exists for this period")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check existing statement: %w", err)
		}

		// Get opening balance from previous statement or calculate from transactions
		openingBalance := decimal.Zero
		var previousStatement models.CustomerStatement
		if err := tx.Where("branch_id = ? AND customer_id = ? AND end_date < ?",
			branchID, customerID, startDate).
			Order("end_date DESC").
			First(&previousStatement).Error; err == nil {
			// Use closing balance from previous statement
			// For now, we'll start with zero balance - this would need proper calculation
			// based on the last statement item's balance
			openingBalance = decimal.Zero
		}

		// Generate statement items and calculate totals
		statementItems, _, err := s.generateStatementItems(tx, branchID, customerID, startDate, endDate, openingBalance)
		if err != nil {
			return fmt.Errorf("failed to generate statement items: %w", err)
		}

		// Create the customer statement
		customerStatement := &models.CustomerStatement{
			BranchID:   branchID,
			CustomerID: customerID,
			StartDate:  startDate,
			EndDate:    endDate,
		}

		if err := tx.Create(customerStatement).Error; err != nil {
			return fmt.Errorf("failed to create customer statement: %w", err)
		}

		// Create statement items
		for _, item := range statementItems {
			item.StatementID = customerStatement.ID
			if err := tx.Create(&item).Error; err != nil {
				return fmt.Errorf("failed to create statement item: %w", err)
			}
		}

		// Fetch the created statement with relationships
		if err := tx.Preload("Branch").Preload("Customer").Preload("Items").
			First(customerStatement, "id = ?", customerStatement.ID).Error; err != nil {
			return fmt.Errorf("failed to fetch created statement: %w", err)
		}

		generatedStatement = customerStatement
		return nil
	})

	if err != nil {
		return nil, err
	}

	return generatedStatement, nil
}

// generateStatementItems generates statement items for a customer statement
func (s *CustomerStatementService) generateStatementItems(tx *gorm.DB, branchID, customerID string, startDate, endDate time.Time, openingBalance decimal.Decimal) ([]models.StatementItem, decimal.Decimal, error) {
	var statementItems []models.StatementItem
	runningBalance := openingBalance

	// Add opening balance item if not zero
	if !openingBalance.IsZero() {
		statementItems = append(statementItems, models.StatementItem{
			Date:        startDate,
			Description: "Opening Balance",
			Amount:      openingBalance,
			Balance:     runningBalance,
			Type:        "opening_balance",
			Reference:   nil,
		})
	}

	// Get invoices in the period
	var invoices []models.Invoice
	if err := tx.Where("branch_id = ? AND customer_id = ? AND issue_date BETWEEN ? AND ?",
		branchID, customerID, startDate, endDate).
		Order("issue_date ASC").
		Find(&invoices).Error; err != nil {
		return nil, decimal.Zero, fmt.Errorf("failed to fetch invoices: %w", err)
	}

	// Add invoice items
	for _, invoice := range invoices {
		runningBalance = runningBalance.Add(invoice.TotalAmount)
		statementItems = append(statementItems, models.StatementItem{
			Date:        invoice.IssueDate,
			Description: fmt.Sprintf("Invoice %s", invoice.InvoiceNumber),
			Amount:      invoice.TotalAmount,
			Balance:     runningBalance,
			Type:        "invoice",
			Reference:   &invoice.InvoiceNumber,
		})
	}

	// Get payments in the period (this would need a Payment model)
	// For now, we'll skip payments as the Payment model isn't defined in the current schema

	// Get credit notes in the period
	var creditNotes []models.CreditNote
	if err := tx.Where("branch_id = ? AND customer_id = ? AND issue_date BETWEEN ? AND ?",
		branchID, customerID, startDate, endDate).
		Order("issue_date ASC").
		Find(&creditNotes).Error; err != nil {
		return nil, decimal.Zero, fmt.Errorf("failed to fetch credit notes: %w", err)
	}

	// Add credit note items
	for _, creditNote := range creditNotes {
		runningBalance = runningBalance.Sub(creditNote.TotalAmount)
		statementItems = append(statementItems, models.StatementItem{
			Date:        creditNote.IssueDate,
			Description: fmt.Sprintf("Credit Note %s", creditNote.CreditNoteNumber),
			Amount:      creditNote.TotalAmount.Neg(),
			Balance:     runningBalance,
			Type:        "credit_note",
			Reference:   &creditNote.CreditNoteNumber,
		})
	}

	return statementItems, runningBalance, nil
}

// GetCustomerStatementSummary gets summary statistics for customer statements
func (s *CustomerStatementService) GetCustomerStatementSummary(branchID string) (*CustomerStatementSummary, error) {
	var summary CustomerStatementSummary

	// Get total count
	if err := s.db.Model(&models.CustomerStatement{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalStatements).Error; err != nil {
		return nil, fmt.Errorf("failed to count customer statements: %w", err)
	}

	// Get statements by period (last 12 months)
	now := time.Now()
	oneYearAgo := now.AddDate(-1, 0, 0)

	if err := s.db.Model(&models.CustomerStatement{}).
		Where("branch_id = ? AND created_at >= ?", branchID, oneYearAgo).
		Count(&summary.StatementsLastYear).Error; err != nil {
		return nil, fmt.Errorf("failed to count statements last year: %w", err)
	}

	// Get statements by customer count
	customerCounts := []struct {
		CustomerID string
		Count      int64
	}{}

	if err := s.db.Model(&models.CustomerStatement{}).
		Where("branch_id = ?", branchID).
		Select("customer_id, COUNT(*) as count").
		Group("customer_id").
		Scan(&customerCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get customer counts: %w", err)
	}

	summary.CustomerCounts = make(map[string]int64)
	for _, cc := range customerCounts {
		summary.CustomerCounts[cc.CustomerID] = cc.Count
	}

	// Get recent statements
	var recentStatements []models.CustomerStatement
	if err := s.db.Preload("Customer").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(5).
		Find(&recentStatements).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent statements: %w", err)
	}

	summary.RecentStatements = recentStatements

	return &summary, nil
}

// ValidateCustomerStatement validates customer statement data
func (s *CustomerStatementService) ValidateCustomerStatement(customerStatement *models.CustomerStatement) error {
	if customerStatement.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if customerStatement.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if customerStatement.StartDate.IsZero() {
		return fmt.Errorf("start date is required")
	}
	if customerStatement.EndDate.IsZero() {
		return fmt.Errorf("end date is required")
	}
	if customerStatement.StartDate.After(customerStatement.EndDate) || customerStatement.StartDate.Equal(customerStatement.EndDate) {
		return fmt.Errorf("start date must be before end date")
	}

	// Check if date range is reasonable (not more than 1 year)
	if customerStatement.EndDate.Sub(customerStatement.StartDate) > 365*24*time.Hour {
		return fmt.Errorf("statement period cannot exceed 1 year")
	}

	return nil
}

// GetCustomerBalance gets the current balance for a customer
func (s *CustomerStatementService) GetCustomerBalance(customerID string) (decimal.Decimal, error) {
	var balance decimal.Decimal

	// Get the latest statement for the customer
	var latestStatement models.CustomerStatement
	if err := s.db.Where("customer_id = ?", customerID).
		Order("end_date DESC").
		First(&latestStatement).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// No statements found, calculate from transactions
			return s.calculateCustomerBalanceFromTransactions(customerID, nil)
		}
		return decimal.Zero, fmt.Errorf("failed to fetch latest statement: %w", err)
	}

	// Get transactions after the latest statement
	transactionsAfter, err := s.calculateCustomerBalanceFromTransactions(customerID, &latestStatement.EndDate)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate balance from transactions: %w", err)
	}

	// Add the balance from the latest statement item
	var latestItem models.StatementItem
	if err := s.db.Where("statement_id = ?", latestStatement.ID).
		Order("date DESC").
		First(&latestItem).Error; err == nil {
		balance = latestItem.Balance.Add(transactionsAfter)
	} else {
		balance = transactionsAfter
	}

	return balance, nil
}

// calculateCustomerBalanceFromTransactions calculates customer balance from transactions
func (s *CustomerStatementService) calculateCustomerBalanceFromTransactions(customerID string, afterDate *time.Time) (decimal.Decimal, error) {
	var balance decimal.Decimal

	// Build query for invoices
	invoiceQuery := s.db.Model(&models.Invoice{}).Where("customer_id = ?", customerID)
	if afterDate != nil {
		invoiceQuery = invoiceQuery.Where("issue_date > ?", *afterDate)
	}

	// Sum invoice amounts
	var invoiceTotal decimal.Decimal
	if err := invoiceQuery.Select("COALESCE(SUM(total_amount), 0)").Scan(&invoiceTotal).Error; err != nil {
		return decimal.Zero, fmt.Errorf("failed to sum invoice amounts: %w", err)
	}

	// Build query for credit notes
	creditQuery := s.db.Model(&models.CreditNote{}).Where("customer_id = ?", customerID)
	if afterDate != nil {
		creditQuery = creditQuery.Where("issue_date > ?", *afterDate)
	}

	// Sum credit note amounts
	var creditTotal decimal.Decimal
	if err := creditQuery.Select("COALESCE(SUM(total_amount), 0)").Scan(&creditTotal).Error; err != nil {
		return decimal.Zero, fmt.Errorf("failed to sum credit note amounts: %w", err)
	}

	// Calculate balance (invoices - credits)
	balance = invoiceTotal.Sub(creditTotal)

	return balance, nil
}

// CustomerStatementSummary represents summary statistics for customer statements
type CustomerStatementSummary struct {
	TotalStatements    int64                      `json:"totalStatements"`
	StatementsLastYear int64                      `json:"statementsLastYear"`
	CustomerCounts     map[string]int64           `json:"customerCounts"`
	RecentStatements   []models.CustomerStatement `json:"recentStatements"`
}
