package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// SalesOrderService handles sales order-related business logic
type SalesOrderService struct {
	db *gorm.DB
}

// NewSalesOrderService creates a new SalesOrderService
func NewSalesOrderService(db *gorm.DB) *SalesOrderService {
	return &SalesOrderService{db: db}
}

// GetAllSalesOrders retrieves all sales orders with pagination
func (s *SalesOrderService) GetAllSalesOrders(page, limit int, search string) ([]models.SalesOrder, int64, error) {
	var orders []models.SalesOrder
	var total int64

	query := s.db.Model(&models.SalesOrder{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON sales_orders.customer_id = customers.id").
			Where("sales_orders.order_number ILIKE ? OR customers.name ILIKE ? OR sales_orders.notes ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count sales orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch sales orders: %w", err)
	}

	return orders, total, nil
}

// GetSalesOrderByID retrieves a sales order by ID
func (s *SalesOrderService) GetSalesOrderByID(id string) (*models.SalesOrder, error) {
	var order models.SalesOrder

	if err := s.db.Preload("Merchant").Preload("Customer").
		First(&order, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("sales order not found")
		}
		return nil, fmt.Errorf("failed to fetch sales order: %w", err)
	}

	return &order, nil
}

// GetSalesOrderByOrderNumber retrieves a sales order by order number and merchant
func (s *SalesOrderService) GetSalesOrderByOrderNumber(branchID, orderNumber string) (*models.SalesOrder, error) {
	var order models.SalesOrder

	if err := s.db.Preload("Merchant").Preload("Customer").
		First(&order, "branch_id = ? AND order_number = ?", branchID, orderNumber).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("sales order not found")
		}
		return nil, fmt.Errorf("failed to fetch sales order: %w", err)
	}

	return &order, nil
}

// GetSalesOrdersByMerchant retrieves sales orders for a specific merchant
func (s *SalesOrderService) GetSalesOrdersByMerchant(branchID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.SalesOrder, int64, error) {
	var orders []models.SalesOrder
	var total int64

	query := s.db.Model(&models.SalesOrder{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON sales_orders.customer_id = customers.id").
			Where("sales_orders.order_number ILIKE ? OR customers.name ILIKE ? OR sales_orders.notes ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("order_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("order_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count sales orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch sales orders: %w", err)
	}

	return orders, total, nil
}

// GetSalesOrdersByCustomer retrieves sales orders for a specific customer
func (s *SalesOrderService) GetSalesOrdersByCustomer(customerID string, page, limit int, status string) ([]models.SalesOrder, int64, error) {
	var orders []models.SalesOrder
	var total int64

	query := s.db.Model(&models.SalesOrder{}).Where("customer_id = ?", customerID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count sales orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&orders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch sales orders: %w", err)
	}

	return orders, total, nil
}

// CreateSalesOrder creates a new sales order
func (s *SalesOrderService) CreateSalesOrder(order *models.SalesOrder) error {
	// Validate branch exists
	var branch models.Branch
	if err := s.db.First(&branch, "id = ?", order.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("branch not found")
		}
		return fmt.Errorf("failed to validate branch: %w", err)
	}

	// Validate customer exists and belongs to branch
	var customer models.Customer
	if err := s.db.First(&customer, "id = ? AND branch_id = ?", order.CustomerID, order.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("customer not found or does not belong to branch")
		}
		return fmt.Errorf("failed to validate customer: %w", err)
	}

	// Check if order number already exists for this branch
	var existingOrder models.SalesOrder
	if err := s.db.First(&existingOrder, "branch_id = ? AND order_number = ?", order.BranchID, order.OrderNumber).Error; err == nil {
		return fmt.Errorf("order number already exists for this merchant")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check order number uniqueness: %w", err)
	}

	// Set default status if not provided
	if order.Status == "" {
		order.Status = "Draft"
	}

	// Create the sales order
	if err := s.db.Create(order).Error; err != nil {
		return fmt.Errorf("failed to create sales order: %w", err)
	}

	return nil
}

// UpdateSalesOrder updates an existing sales order
func (s *SalesOrderService) UpdateSalesOrder(id string, updates *models.SalesOrder) (*models.SalesOrder, error) {
	var order models.SalesOrder

	// Check if sales order exists
	if err := s.db.First(&order, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("sales order not found")
		}
		return nil, fmt.Errorf("failed to fetch sales order: %w", err)
	}

	// Prevent updates to completed orders
	if order.Status == "Completed" || order.Status == "Cancelled" {
		return nil, fmt.Errorf("cannot update completed or cancelled sales order")
	}

	// If updating customer ID, validate the new customer
	if updates.CustomerID != "" && updates.CustomerID != order.CustomerID {
		var customer models.Customer
		if err := s.db.First(&customer, "id = ? AND branch_id = ?", updates.CustomerID, order.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("customer not found or does not belong to branch")
			}
			return nil, fmt.Errorf("failed to validate customer: %w", err)
		}
	}

	// If updating order number, check uniqueness
	if updates.OrderNumber != "" && updates.OrderNumber != order.OrderNumber {
		var existingOrder models.SalesOrder
		if err := s.db.First(&existingOrder, "branch_id = ? AND order_number = ? AND id != ?",
			order.BranchID, updates.OrderNumber, id).Error; err == nil {
			return nil, fmt.Errorf("order number already exists for this merchant")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to check order number uniqueness: %w", err)
		}
	}

	// Update the sales order
	if err := s.db.Model(&order).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update sales order: %w", err)
	}

	// Fetch updated sales order with relationships
	if err := s.db.Preload("Merchant").Preload("Customer").
		First(&order, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated sales order: %w", err)
	}

	return &order, nil
}

// UpdateSalesOrderStatus updates the status of a sales order
func (s *SalesOrderService) UpdateSalesOrderStatus(id, status string) (*models.SalesOrder, error) {
	var order models.SalesOrder

	// Check if sales order exists
	if err := s.db.First(&order, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("sales order not found")
		}
		return nil, fmt.Errorf("failed to fetch sales order: %w", err)
	}

	// Validate status transition
	if !s.isValidStatusTransition(order.Status, status) {
		return nil, fmt.Errorf("invalid status transition from %s to %s", order.Status, status)
	}

	// Update status
	order.Status = status
	if err := s.db.Save(&order).Error; err != nil {
		return nil, fmt.Errorf("failed to update sales order status: %w", err)
	}

	return &order, nil
}

// DeleteSalesOrder deletes a sales order
func (s *SalesOrderService) DeleteSalesOrder(id string) error {
	var order models.SalesOrder

	// Check if sales order exists
	if err := s.db.First(&order, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("sales order not found")
		}
		return fmt.Errorf("failed to fetch sales order: %w", err)
	}

	// Prevent deletion of completed orders
	if order.Status == "Completed" {
		return fmt.Errorf("cannot delete completed sales order")
	}

	// Delete the sales order
	if err := s.db.Delete(&order).Error; err != nil {
		return fmt.Errorf("failed to delete sales order: %w", err)
	}

	return nil
}

// GenerateOrderNumber generates a unique order number for a merchant
func (s *SalesOrderService) GenerateOrderNumber(branchID string) (string, error) {
	// Get the current year
	year := time.Now().Year()

	// Get the count of orders for this merchant this year
	var count int64
	startOfYear := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endOfYear := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.UTC)

	if err := s.db.Model(&models.SalesOrder{}).
		Where("branch_id = ? AND order_date >= ? AND order_date < ?", branchID, startOfYear, endOfYear).
		Count(&count).Error; err != nil {
		return "", fmt.Errorf("failed to count existing orders: %w", err)
	}

	// Generate order number: SO-YYYY-NNNN
	orderNumber := fmt.Sprintf("SO-%d-%04d", year, count+1)

	// Ensure uniqueness (in case of concurrent requests)
	for {
		var existingOrder models.SalesOrder
		if err := s.db.First(&existingOrder, "branch_id = ? AND order_number = ?", branchID, orderNumber).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break // Order number is unique
			}
			return "", fmt.Errorf("failed to check order number uniqueness: %w", err)
		}
		// Order number exists, increment and try again
		count++
		orderNumber = fmt.Sprintf("SO-%d-%04d", year, count+1)
	}

	return orderNumber, nil
}

// GetSalesOrderSummary gets summary statistics for sales orders
func (s *SalesOrderService) GetSalesOrderSummary(branchID string, startDate, endDate *time.Time) (*SalesOrderSummary, error) {
	query := s.db.Model(&models.SalesOrder{}).Where("branch_id = ?", branchID)

	if startDate != nil {
		query = query.Where("order_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("order_date <= ?", *endDate)
	}

	var summary SalesOrderSummary

	// Get total count
	if err := query.Count(&summary.TotalOrders).Error; err != nil {
		return nil, fmt.Errorf("failed to count orders: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case "Draft":
			summary.DraftOrders = sc.Count
		case "Confirmed":
			summary.ConfirmedOrders = sc.Count
		case "Processing":
			summary.ProcessingOrders = sc.Count
		case "Completed":
			summary.CompletedOrders = sc.Count
		case "Cancelled":
			summary.CancelledOrders = sc.Count
		}
	}

	return &summary, nil
}

// SalesOrderSummary represents summary statistics for sales orders
type SalesOrderSummary struct {
	TotalOrders      int64 `json:"totalOrders"`
	DraftOrders      int64 `json:"draftOrders"`
	ConfirmedOrders  int64 `json:"confirmedOrders"`
	ProcessingOrders int64 `json:"processingOrders"`
	CompletedOrders  int64 `json:"completedOrders"`
	CancelledOrders  int64 `json:"cancelledOrders"`
}

// isValidStatusTransition checks if a status transition is valid
func (s *SalesOrderService) isValidStatusTransition(currentStatus, newStatus string) bool {
	validTransitions := map[string][]string{
		"Draft":      {"Confirmed", "Cancelled"},
		"Confirmed":  {"Processing", "Cancelled"},
		"Processing": {"Completed", "Cancelled"},
		"Completed":  {}, // No transitions from completed
		"Cancelled":  {}, // No transitions from cancelled
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// ValidateSalesOrder validates sales order data
func (s *SalesOrderService) ValidateSalesOrder(order *models.SalesOrder) error {
	if order.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if order.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if order.OrderNumber == "" {
		return fmt.Errorf("order number is required")
	}
	if order.OrderDate.IsZero() {
		return fmt.Errorf("order date is required")
	}

	// Validate status
	validStatuses := []string{"Draft", "Confirmed", "Processing", "Completed", "Cancelled"}
	isValidStatus := false
	for _, status := range validStatuses {
		if order.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", order.Status)
	}

	return nil
}
