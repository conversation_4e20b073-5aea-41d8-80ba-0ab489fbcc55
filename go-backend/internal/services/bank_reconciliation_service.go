package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BankReconciliationService handles bank reconciliation-related business logic
type BankReconciliationService struct {
	db *gorm.DB
}

// NewBankReconciliationService creates a new BankReconciliationService
func NewBankReconciliationService(db *gorm.DB) *BankReconciliationService {
	return &BankReconciliationService{db: db}
}

// GetAllBankReconciliations retrieves all bank reconciliations with pagination
func (s *BankReconciliationService) GetAllBankReconciliations(page, limit int, search string) ([]models.BankReconciliation, int64, error) {
	var reconciliations []models.BankReconciliation
	var total int64

	query := s.db.Model(&models.BankReconciliation{})

	// Apply search filter if provided (search by notes or bank account name)
	if search != "" {
		query = query.Joins("LEFT JOIN bank_accounts ON bank_reconciliations.bank_account_id = bank_accounts.id").
			Where("bank_reconciliations.notes ILIKE ? OR bank_accounts.account_name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank reconciliations: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("BankAccount").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&reconciliations).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank reconciliations: %w", err)
	}

	return reconciliations, total, nil
}

// GetBankReconciliationByID retrieves a bank reconciliation by ID
func (s *BankReconciliationService) GetBankReconciliationByID(id string) (*models.BankReconciliation, error) {
	var reconciliation models.BankReconciliation

	if err := s.db.Preload("Merchant").Preload("BankAccount").
		First(&reconciliation, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank reconciliation not found")
		}
		return nil, fmt.Errorf("failed to fetch bank reconciliation: %w", err)
	}

	return &reconciliation, nil
}

// GetBankReconciliationsByMerchant retrieves bank reconciliations for a specific merchant
func (s *BankReconciliationService) GetBankReconciliationsByMerchant(branchID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.BankReconciliation, int64, error) {
	var reconciliations []models.BankReconciliation
	var total int64

	query := s.db.Model(&models.BankReconciliation{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN bank_accounts ON bank_reconciliations.bank_account_id = bank_accounts.id").
			Where("bank_reconciliations.notes ILIKE ? OR bank_accounts.account_name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("start_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("end_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank reconciliations: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("BankAccount").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&reconciliations).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank reconciliations: %w", err)
	}

	return reconciliations, total, nil
}

// GetBankReconciliationsByBankAccount retrieves bank reconciliations for a specific bank account
func (s *BankReconciliationService) GetBankReconciliationsByBankAccount(bankAccountID string, page, limit int, status string) ([]models.BankReconciliation, int64, error) {
	var reconciliations []models.BankReconciliation
	var total int64

	query := s.db.Model(&models.BankReconciliation{}).Where("bank_account_id = ?", bankAccountID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank reconciliations: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("BankAccount").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&reconciliations).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank reconciliations: %w", err)
	}

	return reconciliations, total, nil
}

// CreateBankReconciliation creates a new bank reconciliation
func (s *BankReconciliationService) CreateBankReconciliation(reconciliation *models.BankReconciliation) error {
	// Validate branch exists
	var branch models.Branch
	if err := s.db.First(&branch, "id = ?", reconciliation.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("branch not found")
		}
		return fmt.Errorf("failed to validate branch: %w", err)
	}

	// Validate bank account exists and belongs to branch
	var bankAccount models.BankAccount
	if err := s.db.First(&bankAccount, "id = ? AND branch_id = ?", reconciliation.BankAccountID, reconciliation.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("bank account not found or does not belong to branch")
		}
		return fmt.Errorf("failed to validate bank account: %w", err)
	}

	// Check for overlapping reconciliation periods
	var existingCount int64
	if err := s.db.Model(&models.BankReconciliation{}).
		Where("bank_account_id = ? AND status != ? AND ((start_date <= ? AND end_date >= ?) OR (start_date <= ? AND end_date >= ?))",
			reconciliation.BankAccountID, models.BankReconciliationStatusCompleted,
			reconciliation.StartDate, reconciliation.StartDate,
			reconciliation.EndDate, reconciliation.EndDate).
		Count(&existingCount).Error; err != nil {
		return fmt.Errorf("failed to check for overlapping reconciliations: %w", err)
	}

	if existingCount > 0 {
		return fmt.Errorf("overlapping reconciliation period exists for this bank account")
	}

	// Set default status if not provided
	if reconciliation.Status == "" {
		reconciliation.Status = models.BankReconciliationStatusInProgress
	}

	// Calculate book balance if not provided
	if reconciliation.BookBalance.IsZero() {
		bookBalance, err := s.calculateBookBalance(reconciliation.BankAccountID, reconciliation.EndDate)
		if err != nil {
			return fmt.Errorf("failed to calculate book balance: %w", err)
		}
		reconciliation.BookBalance = bookBalance
	}

	// Create the bank reconciliation
	if err := s.db.Create(reconciliation).Error; err != nil {
		return fmt.Errorf("failed to create bank reconciliation: %w", err)
	}

	return nil
}

// UpdateBankReconciliation updates an existing bank reconciliation
func (s *BankReconciliationService) UpdateBankReconciliation(id string, updates *models.BankReconciliation) (*models.BankReconciliation, error) {
	var reconciliation models.BankReconciliation

	// Check if bank reconciliation exists
	if err := s.db.First(&reconciliation, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank reconciliation not found")
		}
		return nil, fmt.Errorf("failed to fetch bank reconciliation: %w", err)
	}

	// Prevent updates to completed reconciliations
	if reconciliation.Status == models.BankReconciliationStatusCompleted {
		return nil, fmt.Errorf("cannot update completed bank reconciliation")
	}

	// If updating bank account ID, validate the new bank account
	if updates.BankAccountID != "" && updates.BankAccountID != reconciliation.BankAccountID {
		var bankAccount models.BankAccount
		if err := s.db.First(&bankAccount, "id = ? AND branch_id = ?", updates.BankAccountID, reconciliation.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("bank account not found or does not belong to branch")
			}
			return nil, fmt.Errorf("failed to validate bank account: %w", err)
		}
	}

	// Update the bank reconciliation
	if err := s.db.Model(&reconciliation).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update bank reconciliation: %w", err)
	}

	// Fetch updated bank reconciliation with relationships
	if err := s.db.Preload("Branch").Preload("BankAccount").
		First(&reconciliation, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated bank reconciliation: %w", err)
	}

	return &reconciliation, nil
}

// CompleteBankReconciliation marks a bank reconciliation as completed
func (s *BankReconciliationService) CompleteBankReconciliation(id string) (*models.BankReconciliation, error) {
	var reconciliation models.BankReconciliation

	// Check if bank reconciliation exists
	if err := s.db.First(&reconciliation, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank reconciliation not found")
		}
		return nil, fmt.Errorf("failed to fetch bank reconciliation: %w", err)
	}

	// Check if already completed
	if reconciliation.Status == models.BankReconciliationStatusCompleted {
		return nil, fmt.Errorf("bank reconciliation is already completed")
	}

	// Verify that statement balance matches book balance (within tolerance)
	tolerance := decimal.NewFromFloat(0.01) // 1 cent tolerance
	difference := reconciliation.StatementBalance.Sub(reconciliation.BookBalance).Abs()
	if difference.GreaterThan(tolerance) {
		return nil, fmt.Errorf("statement balance does not match book balance (difference: %s)", difference.String())
	}

	// Update status to completed
	reconciliation.Status = models.BankReconciliationStatusCompleted
	if err := s.db.Save(&reconciliation).Error; err != nil {
		return nil, fmt.Errorf("failed to complete bank reconciliation: %w", err)
	}

	// Mark all transactions in the period as reconciled
	if err := s.markTransactionsAsReconciled(reconciliation.BankAccountID, reconciliation.StartDate, reconciliation.EndDate); err != nil {
		return nil, fmt.Errorf("failed to mark transactions as reconciled: %w", err)
	}

	return &reconciliation, nil
}

// DeleteBankReconciliation deletes a bank reconciliation
func (s *BankReconciliationService) DeleteBankReconciliation(id string) error {
	var reconciliation models.BankReconciliation

	// Check if bank reconciliation exists
	if err := s.db.First(&reconciliation, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("bank reconciliation not found")
		}
		return fmt.Errorf("failed to fetch bank reconciliation: %w", err)
	}

	// Prevent deletion of completed reconciliations
	if reconciliation.Status == models.BankReconciliationStatusCompleted {
		return fmt.Errorf("cannot delete completed bank reconciliation")
	}

	// Delete the bank reconciliation
	if err := s.db.Delete(&reconciliation).Error; err != nil {
		return fmt.Errorf("failed to delete bank reconciliation: %w", err)
	}

	return nil
}

// calculateBookBalance calculates the book balance for a bank account up to a specific date
func (s *BankReconciliationService) calculateBookBalance(bankAccountID string, endDate time.Time) (decimal.Decimal, error) {
	var result struct {
		Balance decimal.Decimal
	}

	if err := s.db.Model(&models.BankTransaction{}).
		Select("COALESCE(SUM(amount), 0) as balance").
		Where("bank_account_id = ? AND date <= ?", bankAccountID, endDate).
		Scan(&result).Error; err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate book balance: %w", err)
	}

	return result.Balance, nil
}

// markTransactionsAsReconciled marks all transactions in a period as reconciled
func (s *BankReconciliationService) markTransactionsAsReconciled(bankAccountID string, startDate, endDate time.Time) error {
	if err := s.db.Model(&models.BankTransaction{}).
		Where("bank_account_id = ? AND date >= ? AND date <= ? AND status = ?",
			bankAccountID, startDate, endDate, models.BankTransactionStatusUnreconciled).
		Update("status", models.BankTransactionStatusReconciled).Error; err != nil {
		return fmt.Errorf("failed to mark transactions as reconciled: %w", err)
	}

	return nil
}

// GetReconciliationSummary gets summary statistics for reconciliations
func (s *BankReconciliationService) GetReconciliationSummary(branchID string, bankAccountID *string) (*ReconciliationSummary, error) {
	query := s.db.Model(&models.BankReconciliation{}).Where("branch_id = ?", branchID)

	if bankAccountID != nil {
		query = query.Where("bank_account_id = ?", *bankAccountID)
	}

	var summary ReconciliationSummary

	// Get total count
	if err := query.Count(&summary.TotalReconciliations).Error; err != nil {
		return nil, fmt.Errorf("failed to count reconciliations: %w", err)
	}

	// Get completed count
	if err := query.Where("status = ?", models.BankReconciliationStatusCompleted).
		Count(&summary.CompletedReconciliations).Error; err != nil {
		return nil, fmt.Errorf("failed to count completed reconciliations: %w", err)
	}

	// Get in-progress count
	summary.InProgressReconciliations = summary.TotalReconciliations - summary.CompletedReconciliations

	return &summary, nil
}

// ReconciliationSummary represents summary statistics for reconciliations
type ReconciliationSummary struct {
	TotalReconciliations      int64 `json:"totalReconciliations"`
	CompletedReconciliations  int64 `json:"completedReconciliations"`
	InProgressReconciliations int64 `json:"inProgressReconciliations"`
}

// ValidateBankReconciliation validates bank reconciliation data
func (s *BankReconciliationService) ValidateBankReconciliation(reconciliation *models.BankReconciliation) error {
	if reconciliation.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if reconciliation.BankAccountID == "" {
		return fmt.Errorf("bank account ID is required")
	}
	if reconciliation.StartDate.IsZero() {
		return fmt.Errorf("start date is required")
	}
	if reconciliation.EndDate.IsZero() {
		return fmt.Errorf("end date is required")
	}
	if reconciliation.EndDate.Before(reconciliation.StartDate) {
		return fmt.Errorf("end date must be after start date")
	}
	if reconciliation.StatementBalance.IsZero() {
		return fmt.Errorf("statement balance is required")
	}

	return nil
}
