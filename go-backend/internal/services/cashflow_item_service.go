package services

import (
	"errors"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CashFlowItemService struct {
	db *gorm.DB
}

func NewCashFlowItemService(db *gorm.DB) *CashFlowItemService {
	return &CashFlowItemService{db: db}
}

// Request/Response types
type CreateCashFlowItemRequest struct {
	Description   string                    `json:"description" binding:"required"`
	Amount        decimal.Decimal           `json:"amount" binding:"required"`
	Type          models.CashFlowItemType   `json:"type" binding:"required"`
	Date          time.Time                 `json:"date" binding:"required"`
	Status        models.CashFlowItemStatus `json:"status"`
	Category      *string                   `json:"category"`
	ReferenceType *string                   `json:"referenceType"`
	ReferenceID   *string                   `json:"referenceId"`
	Notes         *string                   `json:"notes"`
}

type UpdateCashFlowItemRequest struct {
	Description   *string                    `json:"description"`
	Amount        *decimal.Decimal           `json:"amount"`
	Type          *models.CashFlowItemType   `json:"type"`
	Date          *time.Time                 `json:"date"`
	Status        *models.CashFlowItemStatus `json:"status"`
	Category      *string                    `json:"category"`
	ReferenceType *string                    `json:"referenceType"`
	ReferenceID   *string                    `json:"referenceId"`
	Notes         *string                    `json:"notes"`
}

type CashFlowItemFilters struct {
	Type      string `json:"type"`
	Status    string `json:"status"`
	Category  string `json:"category"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}

// GetAllCashFlowItems returns all cash flow items with pagination and filtering
func (s *CashFlowItemService) GetAllCashFlowItems(page, limit int, userID string, filters CashFlowItemFilters) ([]models.CashFlowItem, int64, error) {
	var items []models.CashFlowItem
	var total int64

	// Build query to get items from merchants user has access to
	query := s.db.Model(&models.CashFlowItem{}).
		Joins("JOIN user_merchant_permissions ON cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("user_merchant_permissions.user_id = ?", userID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("cash_flow_items.type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("cash_flow_items.status = ?", filters.Status)
	}
	if filters.Category != "" {
		query = query.Where("cash_flow_items.category = ?", filters.Category)
	}
	if filters.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", filters.StartDate); err == nil {
			query = query.Where("cash_flow_items.date >= ?", startDate)
		}
	}
	if filters.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", filters.EndDate); err == nil {
			query = query.Where("cash_flow_items.date <= ?", endDate)
		}
	}

	// Count total items
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get items with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Category").
		Order("cash_flow_items.date DESC").
		Offset(offset).Limit(limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// GetCashFlowItemByID returns a cash flow item by ID
func (s *CashFlowItemService) GetCashFlowItemByID(id, userID string) (*models.CashFlowItem, error) {
	var item models.CashFlowItem

	// Get item and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("cash_flow_items.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		Preload("Merchant").Preload("Category").
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("cash flow item not found or access denied")
		}
		return nil, err
	}

	return &item, nil
}

// CreateCashFlowItem creates a new cash flow item
func (s *CashFlowItemService) CreateCashFlowItem(req CreateCashFlowItemRequest, userID string) (*models.CashFlowItem, error) {
	// Get user's default branch
	branchID, err := s.getUserDefaultBranch(userID)
	if err != nil {
		return nil, err
	}

	// Set default status if not provided
	status := req.Status
	if status == "" {
		status = models.CashFlowItemStatus("Projected")
	}

	// Create item
	item := models.CashFlowItem{
		ID:          uuid.New().String(),
		BranchID:    branchID,
		Description: req.Description,
		Amount:      req.Amount,
		Type:        req.Type,
		Date:        req.Date,
		Status:      status,
		CategoryID:  req.Category,
		Reference:   req.ReferenceType, // Using Reference field for reference type
		Notes:       req.Notes,
	}

	if err := s.db.Create(&item).Error; err != nil {
		return nil, err
	}

	// Reload item with relationships
	if err := s.db.Preload("Branch").Preload("Category").
		Where("id = ?", item.ID).First(&item).Error; err != nil {
		return nil, err
	}

	return &item, nil
}

// UpdateCashFlowItem updates an existing cash flow item
func (s *CashFlowItemService) UpdateCashFlowItem(id string, req UpdateCashFlowItemRequest, userID string) (*models.CashFlowItem, error) {
	var item models.CashFlowItem

	// Get item and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON cash_flow_items.branch_id = user_branch_permissions.branch_id").
		Where("cash_flow_items.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("cash flow item not found or access denied")
		}
		return nil, err
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Amount != nil {
		updates["amount"] = *req.Amount
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Date != nil {
		updates["date"] = *req.Date
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Category != nil {
		updates["category_id"] = *req.Category
	}
	if req.ReferenceType != nil {
		updates["reference"] = *req.ReferenceType
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}

	if len(updates) > 0 {
		if err := s.db.Model(&item).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload item with relationships
	if err := s.db.Preload("Branch").Preload("Category").
		Where("id = ?", id).First(&item).Error; err != nil {
		return nil, err
	}

	return &item, nil
}

// DeleteCashFlowItem deletes a cash flow item
func (s *CashFlowItemService) DeleteCashFlowItem(id, userID string) error {
	var item models.CashFlowItem

	// Get item and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON cash_flow_items.branch_id = user_branch_permissions.branch_id").
		Where("cash_flow_items.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("cash flow item not found or access denied")
		}
		return err
	}

	// Delete the item
	return s.db.Delete(&item).Error
}

// Helper methods
func (s *CashFlowItemService) getUserDefaultBranch(userID string) (string, error) {
	var permission models.UserBranchPermission
	if err := s.db.Where("user_id = ?", userID).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("no branch found for user")
		}
		return "", err
	}
	return permission.BranchID, nil
}

func (s *CashFlowItemService) hasBranchAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserBranchPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}
