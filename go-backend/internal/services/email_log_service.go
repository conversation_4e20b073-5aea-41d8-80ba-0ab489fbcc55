package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// EmailLogService handles email log-related business logic
type EmailLogService struct {
	db *gorm.DB
}

// NewEmailLogService creates a new EmailLogService
func NewEmailLogService(db *gorm.DB) *EmailLogService {
	return &EmailLogService{db: db}
}

// GetAllEmailLogs retrieves all email logs with pagination
func (s *EmailLogService) GetAllEmailLogs(page, limit int, search string) ([]models.EmailLog, int64, error) {
	var logs []models.EmailLog
	var total int64

	query := s.db.Model(&models.EmailLog{})

	// Apply search filter if provided (search by email addresses or subject)
	if search != "" {
		query = query.Where("to_email ILIKE ? OR from_email ILIKE ? OR subject ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email logs: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Template").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email logs: %w", err)
	}

	return logs, total, nil
}

// GetEmailLogByID retrieves an email log by ID
func (s *EmailLogService) GetEmailLogByID(id string) (*models.EmailLog, error) {
	var log models.EmailLog

	if err := s.db.Preload("Merchant").Preload("Template").
		First(&log, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email log not found")
		}
		return nil, fmt.Errorf("failed to fetch email log: %w", err)
	}

	return &log, nil
}

// GetEmailLogsByMerchant retrieves email logs for a specific merchant
func (s *EmailLogService) GetEmailLogsByMerchant(branchID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.EmailLog, int64, error) {
	var logs []models.EmailLog
	var total int64

	query := s.db.Model(&models.EmailLog{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("to_email ILIKE ? OR from_email ILIKE ? OR subject ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("created_at >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("created_at <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email logs: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Template").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email logs: %w", err)
	}

	return logs, total, nil
}

// GetEmailLogsByTemplate retrieves email logs for a specific template
func (s *EmailLogService) GetEmailLogsByTemplate(templateID string, page, limit int, status string) ([]models.EmailLog, int64, error) {
	var logs []models.EmailLog
	var total int64

	query := s.db.Model(&models.EmailLog{}).Where("template_id = ?", templateID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email logs: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Template").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email logs: %w", err)
	}

	return logs, total, nil
}

// CreateEmailLog creates a new email log
func (s *EmailLogService) CreateEmailLog(log *models.EmailLog) error {
	// Validate branch exists
	var branch models.Branch
	if err := s.db.First(&branch, "id = ?", log.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("branch not found")
		}
		return fmt.Errorf("failed to validate branch: %w", err)
	}

	// Validate template exists if provided
	if log.TemplateID != nil {
		var template models.EmailTemplate
		if err := s.db.First(&template, "id = ? AND branch_id = ?", *log.TemplateID, log.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("email template not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate email template: %w", err)
		}
	}

	// Set default status if not provided
	if log.Status == "" {
		log.Status = models.EmailStatusScheduled
	}

	// Set sent time if status is sent
	if log.Status == models.EmailStatusSent && log.SentAt == nil {
		now := time.Now()
		log.SentAt = &now
	}

	// Create the email log
	if err := s.db.Create(log).Error; err != nil {
		return fmt.Errorf("failed to create email log: %w", err)
	}

	return nil
}

// UpdateEmailLog updates an existing email log
func (s *EmailLogService) UpdateEmailLog(id string, updates *models.EmailLog) (*models.EmailLog, error) {
	var log models.EmailLog

	// Check if email log exists
	if err := s.db.First(&log, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email log not found")
		}
		return nil, fmt.Errorf("failed to fetch email log: %w", err)
	}

	// If updating template ID, validate the new template
	if updates.TemplateID != nil && (log.TemplateID == nil || *updates.TemplateID != *log.TemplateID) {
		var template models.EmailTemplate
		if err := s.db.First(&template, "id = ? AND branch_id = ?", *updates.TemplateID, log.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("email template not found or does not belong to branch")
			}
			return nil, fmt.Errorf("failed to validate email template: %w", err)
		}
	}

	// Set sent time if status is being updated to sent
	if updates.Status == models.EmailStatusSent && log.Status != models.EmailStatusSent {
		now := time.Now()
		updates.SentAt = &now
	}

	// Update the email log
	if err := s.db.Model(&log).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update email log: %w", err)
	}

	// Fetch updated email log with relationships
	if err := s.db.Preload("Merchant").Preload("Template").
		First(&log, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated email log: %w", err)
	}

	return &log, nil
}

// UpdateEmailLogStatus updates the status of an email log
func (s *EmailLogService) UpdateEmailLogStatus(id string, status models.EmailStatus, errorMessage *string) (*models.EmailLog, error) {
	var log models.EmailLog

	// Check if email log exists
	if err := s.db.First(&log, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email log not found")
		}
		return nil, fmt.Errorf("failed to fetch email log: %w", err)
	}

	// Update status and related fields
	updates := map[string]interface{}{
		"status": status,
	}

	// Set sent time if status is sent
	if status == models.EmailStatusSent {
		now := time.Now()
		updates["sent_at"] = &now
	}

	// Set error message if status is failed
	if status == models.EmailStatusFailed && errorMessage != nil {
		updates["error_message"] = *errorMessage
	}

	// Clear error message if status is not failed
	if status != models.EmailStatusFailed {
		updates["error_message"] = nil
	}

	if err := s.db.Model(&log).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update email log status: %w", err)
	}

	// Fetch updated email log
	if err := s.db.Preload("Merchant").Preload("Template").
		First(&log, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated email log: %w", err)
	}

	return &log, nil
}

// DeleteEmailLog deletes an email log
func (s *EmailLogService) DeleteEmailLog(id string) error {
	var log models.EmailLog

	// Check if email log exists
	if err := s.db.First(&log, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("email log not found")
		}
		return fmt.Errorf("failed to fetch email log: %w", err)
	}

	// Delete the email log
	if err := s.db.Delete(&log).Error; err != nil {
		return fmt.Errorf("failed to delete email log: %w", err)
	}

	return nil
}

// GetEmailLogSummary gets summary statistics for email logs
func (s *EmailLogService) GetEmailLogSummary(branchID string, startDate, endDate *time.Time) (*EmailLogSummary, error) {
	query := s.db.Model(&models.EmailLog{}).Where("branch_id = ?", branchID)

	if startDate != nil {
		query = query.Where("created_at >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("created_at <= ?", *endDate)
	}

	var summary EmailLogSummary

	// Get total count
	if err := query.Count(&summary.TotalEmails).Error; err != nil {
		return nil, fmt.Errorf("failed to count email logs: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.EmailStatusSent):
			summary.SentEmails = sc.Count
		case string(models.EmailStatusFailed):
			summary.FailedEmails = sc.Count
		case string(models.EmailStatusScheduled):
			summary.ScheduledEmails = sc.Count
		}
	}

	// Calculate success rate
	if summary.TotalEmails > 0 {
		summary.SuccessRate = float64(summary.SentEmails) / float64(summary.TotalEmails) * 100
	}

	return &summary, nil
}

// EmailLogSummary represents summary statistics for email logs
type EmailLogSummary struct {
	TotalEmails     int64   `json:"totalEmails"`
	SentEmails      int64   `json:"sentEmails"`
	FailedEmails    int64   `json:"failedEmails"`
	ScheduledEmails int64   `json:"scheduledEmails"`
	SuccessRate     float64 `json:"successRate"`
}

// ValidateEmailLog validates email log data
func (s *EmailLogService) ValidateEmailLog(log *models.EmailLog) error {
	if log.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if log.ToEmail == "" {
		return fmt.Errorf("to email is required")
	}
	if log.FromEmail == "" {
		return fmt.Errorf("from email is required")
	}
	if log.Subject == "" {
		return fmt.Errorf("subject is required")
	}
	if log.Body == "" {
		return fmt.Errorf("body is required")
	}

	// Validate email status
	validStatuses := []models.EmailStatus{
		models.EmailStatusSent,
		models.EmailStatusFailed,
		models.EmailStatusScheduled,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if log.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", log.Status)
	}

	// Basic email validation
	if !s.isValidEmail(log.ToEmail) {
		return fmt.Errorf("invalid to email format")
	}
	if !s.isValidEmail(log.FromEmail) {
		return fmt.Errorf("invalid from email format")
	}

	return nil
}

// isValidEmail performs basic email validation
func (s *EmailLogService) isValidEmail(email string) bool {
	// Basic email validation - in production, use a proper email validation library
	if len(email) < 5 {
		return false
	}

	atCount := 0
	for _, char := range email {
		if char == '@' {
			atCount++
		}
	}

	return atCount == 1 && email[0] != '@' && email[len(email)-1] != '@'
}

// GetValidStatuses returns all valid email statuses
func (s *EmailLogService) GetValidStatuses() []string {
	return []string{
		string(models.EmailStatusSent),
		string(models.EmailStatusFailed),
		string(models.EmailStatusScheduled),
	}
}

// MarkEmailAsSent marks an email log as sent
func (s *EmailLogService) MarkEmailAsSent(id string) (*models.EmailLog, error) {
	return s.UpdateEmailLogStatus(id, models.EmailStatusSent, nil)
}

// MarkEmailAsFailed marks an email log as failed with an error message
func (s *EmailLogService) MarkEmailAsFailed(id string, errorMessage string) (*models.EmailLog, error) {
	return s.UpdateEmailLogStatus(id, models.EmailStatusFailed, &errorMessage)
}

// GetRecentEmailLogs gets the most recent email logs for a merchant
func (s *EmailLogService) GetRecentEmailLogs(branchID string, limit int) ([]models.EmailLog, error) {
	var logs []models.EmailLog

	if err := s.db.Preload("Merchant").Preload("Template").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch recent email logs: %w", err)
	}

	return logs, nil
}
