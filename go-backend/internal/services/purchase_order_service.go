package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// PurchaseOrderService handles purchase order-related business logic
type PurchaseOrderService struct {
	db *gorm.DB
}

// NewPurchaseOrderService creates a new PurchaseOrderService
func NewPurchaseOrderService(db *gorm.DB) *PurchaseOrderService {
	return &PurchaseOrderService{db: db}
}

// GetAllPurchaseOrders retrieves all purchase orders with pagination
func (s *PurchaseOrderService) GetAllPurchaseOrders(page, limit int, search string) ([]models.PurchaseOrder, int64, error) {
	var purchaseOrders []models.PurchaseOrder
	var total int64

	query := s.db.Model(&models.PurchaseOrder{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN vendors ON purchase_orders.vendor_id = vendors.id").
			Where("purchase_orders.order_number ILIKE ? OR vendors.name ILIKE ? OR purchase_orders.reference ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&purchaseOrders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch purchase orders: %w", err)
	}

	return purchaseOrders, total, nil
}

// GetPurchaseOrderByID retrieves a purchase order by ID
func (s *PurchaseOrderService) GetPurchaseOrderByID(id string) (*models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	if err := s.db.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
		First(&purchaseOrder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("purchase order not found")
		}
		return nil, fmt.Errorf("failed to fetch purchase order: %w", err)
	}

	return &purchaseOrder, nil
}

// GetPurchaseOrderByNumber retrieves a purchase order by number and merchant
func (s *PurchaseOrderService) GetPurchaseOrderByNumber(branchID, orderNumber string) (*models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	if err := s.db.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
		First(&purchaseOrder, "branch_id = ? AND order_number = ?", branchID, orderNumber).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("purchase order not found")
		}
		return nil, fmt.Errorf("failed to fetch purchase order: %w", err)
	}

	return &purchaseOrder, nil
}

// GetPurchaseOrdersByMerchant retrieves purchase orders for a specific merchant
func (s *PurchaseOrderService) GetPurchaseOrdersByMerchant(branchID string, page, limit int, search, status string, startDate, endDate *time.Time) ([]models.PurchaseOrder, int64, error) {
	var purchaseOrders []models.PurchaseOrder
	var total int64

	query := s.db.Model(&models.PurchaseOrder{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN vendors ON purchase_orders.vendor_id = vendors.id").
			Where("purchase_orders.order_number ILIKE ? OR vendors.name ILIKE ? OR purchase_orders.reference ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("order_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("order_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&purchaseOrders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch purchase orders: %w", err)
	}

	return purchaseOrders, total, nil
}

// GetPurchaseOrdersByVendor retrieves purchase orders for a specific vendor
func (s *PurchaseOrderService) GetPurchaseOrdersByVendor(vendorID string, page, limit int, status string) ([]models.PurchaseOrder, int64, error) {
	var purchaseOrders []models.PurchaseOrder
	var total int64

	query := s.db.Model(&models.PurchaseOrder{}).Where("vendor_id = ?", vendorID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
		Offset(offset).Limit(limit).
		Order("order_date DESC, created_at DESC").
		Find(&purchaseOrders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch purchase orders: %w", err)
	}

	return purchaseOrders, total, nil
}

// CreatePurchaseOrder creates a new purchase order with items
func (s *PurchaseOrderService) CreatePurchaseOrder(purchaseOrder *models.PurchaseOrder, items []models.PurchaseOrderItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", purchaseOrder.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate vendor exists and belongs to branch
		var vendor models.Vendor
		if err := tx.First(&vendor, "id = ? AND branch_id = ?", purchaseOrder.VendorID, purchaseOrder.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("vendor not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate vendor: %w", err)
		}

		// Check if order number already exists for this branch
		var existingPO models.PurchaseOrder
		if err := tx.First(&existingPO, "branch_id = ? AND order_number = ?", purchaseOrder.BranchID, purchaseOrder.OrderNumber).Error; err == nil {
			return fmt.Errorf("purchase order number already exists for this merchant")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check order number uniqueness: %w", err)
		}

		// Calculate totals from items
		var subtotal, taxTotal decimal.Decimal
		for _, item := range items {
			itemTotal := item.Quantity.Mul(item.UnitPrice)
			subtotal = subtotal.Add(itemTotal)
			taxTotal = taxTotal.Add(item.TaxAmount)
		}

		// Set calculated amounts
		purchaseOrder.SubTotal = subtotal
		purchaseOrder.TaxAmount = taxTotal
		purchaseOrder.TotalAmount = subtotal.Add(taxTotal)

		// Set default status if not provided
		if purchaseOrder.Status == "" {
			purchaseOrder.Status = models.PurchaseOrderStatusDraft
		}

		// Create the purchase order
		if err := tx.Create(purchaseOrder).Error; err != nil {
			return fmt.Errorf("failed to create purchase order: %w", err)
		}

		// Create purchase order items
		for i := range items {
			items[i].PurchaseOrderID = purchaseOrder.ID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
			// Calculate total price for the item
			items[i].TotalPrice = items[i].Quantity.Mul(items[i].UnitPrice)
		}

		if err := tx.Create(&items).Error; err != nil {
			return fmt.Errorf("failed to create purchase order items: %w", err)
		}

		return nil
	})
}

// UpdatePurchaseOrder updates an existing purchase order
func (s *PurchaseOrderService) UpdatePurchaseOrder(id string, updates *models.PurchaseOrder) (*models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	// Check if purchase order exists
	if err := s.db.First(&purchaseOrder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("purchase order not found")
		}
		return nil, fmt.Errorf("failed to fetch purchase order: %w", err)
	}

	// Prevent updates to received or closed purchase orders
	if purchaseOrder.Status == models.PurchaseOrderStatusReceived || purchaseOrder.Status == models.PurchaseOrderStatusClosed {
		return nil, fmt.Errorf("cannot update received or closed purchase order")
	}

	var updatedPurchaseOrder *models.PurchaseOrder
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating vendor ID, validate the new vendor
		if updates.VendorID != "" && updates.VendorID != purchaseOrder.VendorID {
			var vendor models.Vendor
			if err := tx.First(&vendor, "id = ? AND branch_id = ?", updates.VendorID, purchaseOrder.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("vendor not found or does not belong to branch")
				}
				return fmt.Errorf("failed to validate vendor: %w", err)
			}
		}

		// If updating order number, check uniqueness
		if updates.OrderNumber != "" && updates.OrderNumber != purchaseOrder.OrderNumber {
			var existingPO models.PurchaseOrder
			if err := tx.First(&existingPO, "branch_id = ? AND order_number = ? AND id != ?",
				purchaseOrder.BranchID, updates.OrderNumber, id).Error; err == nil {
				return fmt.Errorf("purchase order number already exists for this merchant")
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to check order number uniqueness: %w", err)
			}
		}

		// Update the purchase order
		if err := tx.Model(&purchaseOrder).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update purchase order: %w", err)
		}

		// Fetch updated purchase order with relationships
		if err := tx.Preload("Merchant").Preload("Vendor").Preload("Items").Preload("Items.TaxRate").Preload("Items.InventoryItem").
			First(&purchaseOrder, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated purchase order: %w", err)
		}

		updatedPurchaseOrder = &purchaseOrder
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedPurchaseOrder, nil
}

// UpdatePurchaseOrderStatus updates the status of a purchase order
func (s *PurchaseOrderService) UpdatePurchaseOrderStatus(id string, status models.PurchaseOrderStatus) (*models.PurchaseOrder, error) {
	var purchaseOrder models.PurchaseOrder

	// Check if purchase order exists
	if err := s.db.First(&purchaseOrder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("purchase order not found")
		}
		return nil, fmt.Errorf("failed to fetch purchase order: %w", err)
	}

	// Validate status transition
	if !s.isValidStatusTransition(purchaseOrder.Status, status) {
		return nil, fmt.Errorf("invalid status transition from %s to %s", purchaseOrder.Status, status)
	}

	// Update status and related fields
	updates := map[string]interface{}{
		"status": status,
	}

	// Set received date when status changes to received
	if status == models.PurchaseOrderStatusReceived {
		now := time.Now()
		updates["received_date"] = &now
	}

	if err := s.db.Model(&purchaseOrder).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update purchase order status: %w", err)
	}

	return &purchaseOrder, nil
}

// DeletePurchaseOrder deletes a purchase order
func (s *PurchaseOrderService) DeletePurchaseOrder(id string) error {
	var purchaseOrder models.PurchaseOrder

	// Check if purchase order exists
	if err := s.db.First(&purchaseOrder, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("purchase order not found")
		}
		return fmt.Errorf("failed to fetch purchase order: %w", err)
	}

	// Prevent deletion of sent, confirmed, or received purchase orders
	if purchaseOrder.Status == models.PurchaseOrderStatusSent ||
		purchaseOrder.Status == models.PurchaseOrderStatusConfirmed ||
		purchaseOrder.Status == models.PurchaseOrderStatusReceived ||
		purchaseOrder.Status == models.PurchaseOrderStatusClosed {
		return fmt.Errorf("cannot delete purchase order with status: %s", purchaseOrder.Status)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete purchase order items first
		if err := tx.Where("purchase_order_id = ?", id).Delete(&models.PurchaseOrderItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete purchase order items: %w", err)
		}

		// Delete the purchase order
		if err := tx.Delete(&purchaseOrder).Error; err != nil {
			return fmt.Errorf("failed to delete purchase order: %w", err)
		}

		return nil
	})
}

// UpdatePurchaseOrderItems updates the items for a purchase order
func (s *PurchaseOrderService) UpdatePurchaseOrderItems(purchaseOrderID string, items []models.PurchaseOrderItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Verify purchase order exists and is editable
		var purchaseOrder models.PurchaseOrder
		if err := tx.First(&purchaseOrder, "id = ?", purchaseOrderID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("purchase order not found")
			}
			return fmt.Errorf("failed to validate purchase order: %w", err)
		}

		// Prevent updates to received or closed purchase orders
		if purchaseOrder.Status == models.PurchaseOrderStatusReceived || purchaseOrder.Status == models.PurchaseOrderStatusClosed {
			return fmt.Errorf("cannot update items for received or closed purchase order")
		}

		// Delete existing items
		if err := tx.Where("purchase_order_id = ?", purchaseOrderID).Delete(&models.PurchaseOrderItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing purchase order items: %w", err)
		}

		// Create new items
		var subtotal, taxTotal decimal.Decimal
		for i := range items {
			items[i].PurchaseOrderID = purchaseOrderID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
			// Calculate total price for the item
			items[i].TotalPrice = items[i].Quantity.Mul(items[i].UnitPrice)

			// Add to totals
			subtotal = subtotal.Add(items[i].TotalPrice)
			taxTotal = taxTotal.Add(items[i].TaxAmount)
		}

		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create purchase order items: %w", err)
			}
		}

		// Update purchase order totals
		totalAmount := subtotal.Add(taxTotal)
		if err := tx.Model(&purchaseOrder).Updates(map[string]interface{}{
			"sub_total":    subtotal,
			"tax_amount":   taxTotal,
			"total_amount": totalAmount,
		}).Error; err != nil {
			return fmt.Errorf("failed to update purchase order totals: %w", err)
		}

		return nil
	})
}

// ReceivePurchaseOrderItems marks items as received and updates inventory
func (s *PurchaseOrderService) ReceivePurchaseOrderItems(purchaseOrderID string, receivedItems map[string]decimal.Decimal) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get purchase order with items
		var purchaseOrder models.PurchaseOrder
		if err := tx.Preload("Items").First(&purchaseOrder, "id = ?", purchaseOrderID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("purchase order not found")
			}
			return fmt.Errorf("failed to fetch purchase order: %w", err)
		}

		// Validate purchase order status
		if purchaseOrder.Status != models.PurchaseOrderStatusConfirmed && purchaseOrder.Status != models.PurchaseOrderStatusPartial {
			return fmt.Errorf("purchase order must be confirmed or partially received to receive items")
		}

		allItemsReceived := true
		for _, item := range purchaseOrder.Items {
			receivedQty, exists := receivedItems[item.ID]
			if !exists {
				receivedQty = decimal.Zero
			}

			// Update received quantity
			newReceivedQty := item.ReceivedQuantity.Add(receivedQty)
			if newReceivedQty.GreaterThan(item.Quantity) {
				return fmt.Errorf("received quantity cannot exceed ordered quantity for item: %s", item.Description)
			}

			if err := tx.Model(&item).Update("received_quantity", newReceivedQty).Error; err != nil {
				return fmt.Errorf("failed to update received quantity for item %s: %w", item.ID, err)
			}

			// Update inventory if item is linked to inventory
			if item.InventoryItemID != nil && receivedQty.GreaterThan(decimal.Zero) {
				var inventoryItem models.InventoryItem
				if err := tx.First(&inventoryItem, "id = ?", *item.InventoryItemID).Error; err == nil {
					newQuantity := inventoryItem.QuantityOnHand.Add(receivedQty)
					if err := tx.Model(&inventoryItem).Update("quantity_on_hand", newQuantity).Error; err != nil {
						return fmt.Errorf("failed to update inventory quantity: %w", err)
					}

					// Create inventory transaction
					notes := fmt.Sprintf("Received from PO %s", purchaseOrder.OrderNumber)
					sourceType := "PurchaseOrder"
					inventoryTransaction := models.InventoryTransaction{
						BranchID:        purchaseOrder.BranchID,
						InventoryItemID: *item.InventoryItemID,
						Date:            time.Now(),
						Type:            models.InventoryTransactionTypePurchase,
						Quantity:        receivedQty,
						UnitCost:        item.UnitPrice,
						TotalCost:       receivedQty.Mul(item.UnitPrice),
						SourceType:      &sourceType,
						SourceID:        &purchaseOrder.ID,
						Notes:           &notes,
					}

					if err := tx.Create(&inventoryTransaction).Error; err != nil {
						return fmt.Errorf("failed to create inventory transaction: %w", err)
					}
				}
			}

			// Check if item is fully received
			if newReceivedQty.LessThan(item.Quantity) {
				allItemsReceived = false
			}
		}

		// Update purchase order status
		var newStatus models.PurchaseOrderStatus
		if allItemsReceived {
			newStatus = models.PurchaseOrderStatusReceived
		} else {
			newStatus = models.PurchaseOrderStatusPartial
		}

		updates := map[string]interface{}{
			"status": newStatus,
		}

		if newStatus == models.PurchaseOrderStatusReceived {
			now := time.Now()
			updates["received_date"] = &now
		}

		if err := tx.Model(&purchaseOrder).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update purchase order status: %w", err)
		}

		return nil
	})
}

// GeneratePurchaseOrderNumber generates a unique purchase order number for a merchant
func (s *PurchaseOrderService) GeneratePurchaseOrderNumber(branchID string) (string, error) {
	// Get the current year
	year := time.Now().Year()

	// Get the count of purchase orders for this merchant this year
	var count int64
	startOfYear := time.Date(year, 1, 1, 0, 0, 0, 0, time.UTC)
	endOfYear := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.UTC)

	if err := s.db.Model(&models.PurchaseOrder{}).
		Where("branch_id = ? AND order_date >= ? AND order_date < ?", branchID, startOfYear, endOfYear).
		Count(&count).Error; err != nil {
		return "", fmt.Errorf("failed to count existing purchase orders: %w", err)
	}

	// Generate purchase order number: PO-YYYY-NNNN
	orderNumber := fmt.Sprintf("PO-%d-%04d", year, count+1)

	// Ensure uniqueness (in case of concurrent requests)
	for {
		var existingPO models.PurchaseOrder
		if err := s.db.First(&existingPO, "branch_id = ? AND order_number = ?", branchID, orderNumber).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break // Order number is unique
			}
			return "", fmt.Errorf("failed to check order number uniqueness: %w", err)
		}
		// Order number exists, increment and try again
		count++
		orderNumber = fmt.Sprintf("PO-%d-%04d", year, count+1)
	}

	return orderNumber, nil
}

// GetPurchaseOrderSummary gets summary statistics for purchase orders
func (s *PurchaseOrderService) GetPurchaseOrderSummary(branchID string, startDate, endDate *time.Time) (*PurchaseOrderSummary, error) {
	query := s.db.Model(&models.PurchaseOrder{}).Where("branch_id = ?", branchID)

	if startDate != nil {
		query = query.Where("order_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("order_date <= ?", *endDate)
	}

	var summary PurchaseOrderSummary

	// Get total count
	if err := query.Count(&summary.TotalPurchaseOrders).Error; err != nil {
		return nil, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.PurchaseOrderStatusDraft):
			summary.DraftPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusSent):
			summary.SentPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusConfirmed):
			summary.ConfirmedPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusPartial):
			summary.PartialPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusReceived):
			summary.ReceivedPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusCancelled):
			summary.CancelledPurchaseOrders = sc.Count
		case string(models.PurchaseOrderStatusClosed):
			summary.ClosedPurchaseOrders = sc.Count
		}
	}

	// Get total amounts
	var totalAmount decimal.Decimal
	if err := query.Select("COALESCE(SUM(total_amount), 0) as total").
		Scan(&totalAmount).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total amount: %w", err)
	}

	summary.TotalAmount = totalAmount.String()

	return &summary, nil
}

// PurchaseOrderSummary represents summary statistics for purchase orders
type PurchaseOrderSummary struct {
	TotalPurchaseOrders     int64  `json:"totalPurchaseOrders"`
	DraftPurchaseOrders     int64  `json:"draftPurchaseOrders"`
	SentPurchaseOrders      int64  `json:"sentPurchaseOrders"`
	ConfirmedPurchaseOrders int64  `json:"confirmedPurchaseOrders"`
	PartialPurchaseOrders   int64  `json:"partialPurchaseOrders"`
	ReceivedPurchaseOrders  int64  `json:"receivedPurchaseOrders"`
	CancelledPurchaseOrders int64  `json:"cancelledPurchaseOrders"`
	ClosedPurchaseOrders    int64  `json:"closedPurchaseOrders"`
	TotalAmount             string `json:"totalAmount"`
}

// isValidStatusTransition checks if a status transition is valid
func (s *PurchaseOrderService) isValidStatusTransition(currentStatus, newStatus models.PurchaseOrderStatus) bool {
	validTransitions := map[models.PurchaseOrderStatus][]models.PurchaseOrderStatus{
		models.PurchaseOrderStatusDraft:     {models.PurchaseOrderStatusSent, models.PurchaseOrderStatusCancelled},
		models.PurchaseOrderStatusSent:      {models.PurchaseOrderStatusConfirmed, models.PurchaseOrderStatusCancelled},
		models.PurchaseOrderStatusConfirmed: {models.PurchaseOrderStatusPartial, models.PurchaseOrderStatusReceived, models.PurchaseOrderStatusCancelled},
		models.PurchaseOrderStatusPartial:   {models.PurchaseOrderStatusReceived, models.PurchaseOrderStatusCancelled},
		models.PurchaseOrderStatusReceived:  {models.PurchaseOrderStatusClosed},
		models.PurchaseOrderStatusCancelled: {}, // No transitions from cancelled
		models.PurchaseOrderStatusClosed:    {}, // No transitions from closed
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// ValidatePurchaseOrder validates purchase order data
func (s *PurchaseOrderService) ValidatePurchaseOrder(purchaseOrder *models.PurchaseOrder) error {
	if purchaseOrder.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if purchaseOrder.VendorID == "" {
		return fmt.Errorf("vendor ID is required")
	}
	if purchaseOrder.OrderNumber == "" {
		return fmt.Errorf("order number is required")
	}
	if purchaseOrder.OrderDate.IsZero() {
		return fmt.Errorf("order date is required")
	}
	if purchaseOrder.TotalAmount.LessThan(decimal.Zero) {
		return fmt.Errorf("total amount cannot be negative")
	}

	// Validate status
	validStatuses := []models.PurchaseOrderStatus{
		models.PurchaseOrderStatusDraft,
		models.PurchaseOrderStatusSent,
		models.PurchaseOrderStatusConfirmed,
		models.PurchaseOrderStatusPartial,
		models.PurchaseOrderStatusReceived,
		models.PurchaseOrderStatusCancelled,
		models.PurchaseOrderStatusClosed,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if purchaseOrder.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", purchaseOrder.Status)
	}

	return nil
}

// ValidatePurchaseOrderItem validates purchase order item data
func (s *PurchaseOrderService) ValidatePurchaseOrderItem(item *models.PurchaseOrderItem) error {
	if item.PurchaseOrderID == "" {
		return fmt.Errorf("purchase order ID is required")
	}
	if item.Description == "" {
		return fmt.Errorf("item description is required")
	}
	if item.Quantity.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("item quantity must be greater than zero")
	}
	if item.UnitPrice.LessThan(decimal.Zero) {
		return fmt.Errorf("item unit price cannot be negative")
	}
	if item.ReceivedQuantity.LessThan(decimal.Zero) {
		return fmt.Errorf("received quantity cannot be negative")
	}
	if item.ReceivedQuantity.GreaterThan(item.Quantity) {
		return fmt.Errorf("received quantity cannot exceed ordered quantity")
	}

	return nil
}
