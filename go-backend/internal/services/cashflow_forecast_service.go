package services

import (
	"errors"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CashFlowForecastService struct {
	db *gorm.DB
}

func NewCashFlowForecastService(db *gorm.DB) *CashFlowForecastService {
	return &CashFlowForecastService{db: db}
}

// Request/Response types
type CashFlowForecastRequest struct {
	PeriodType string `json:"periodType"` // "week" or "month"
	Periods    int    `json:"periods"`    // number of periods
	StartDate  string `json:"startDate"`  // optional start date
}

type CashFlowForecastResponse struct {
	StartDate      string           `json:"startDate"`
	EndDate        string           `json:"endDate"`
	OpeningBalance decimal.Decimal  `json:"openingBalance"`
	Periods        []CashFlowPeriod `json:"periods"`
	TotalInflows   decimal.Decimal  `json:"totalInflows"`
	TotalOutflows  decimal.Decimal  `json:"totalOutflows"`
	NetCashFlow    decimal.Decimal  `json:"netCashFlow"`
	ClosingBalance decimal.Decimal  `json:"closingBalance"`
	GeneratedAt    string           `json:"generatedAt"`
}

type CashFlowPeriod struct {
	Period        string          `json:"period"`
	StartDate     string          `json:"startDate"`
	EndDate       string          `json:"endDate"`
	Inflows       decimal.Decimal `json:"inflows"`
	Outflows      decimal.Decimal `json:"outflows"`
	NetCashFlow   decimal.Decimal `json:"netCashFlow"`
	EndingBalance decimal.Decimal `json:"endingBalance"`
}

type CashFlowHistoryRequest struct {
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
	Category  string `json:"category"`
	Type      string `json:"type"`
}

type CashFlowHistoryResponse struct {
	Items       []models.CashFlowItem `json:"items"`
	Summary     CashFlowSummary       `json:"summary"`
	GeneratedAt string                `json:"generatedAt"`
}

type CashFlowSummary struct {
	TotalInflows  decimal.Decimal `json:"totalInflows"`
	TotalOutflows decimal.Decimal `json:"totalOutflows"`
	NetCashFlow   decimal.Decimal `json:"netCashFlow"`
	ItemCount     int             `json:"itemCount"`
}

type BudgetComparisonRequest struct {
	Year  int `json:"year"`
	Month int `json:"month"` // 0 for yearly, 1-12 for monthly
}

type BudgetComparisonResponse struct {
	Period      string                   `json:"period"`
	Revenue     BudgetComparisonCategory `json:"revenue"`
	Expenses    BudgetComparisonCategory `json:"expenses"`
	Net         BudgetComparisonCategory `json:"net"`
	GeneratedAt string                   `json:"generatedAt"`
}

type BudgetComparisonCategory struct {
	Budgeted        decimal.Decimal `json:"budgeted"`
	Actual          decimal.Decimal `json:"actual"`
	Variance        decimal.Decimal `json:"variance"`
	VariancePercent decimal.Decimal `json:"variancePercent"`
}

// GenerateForecast generates a cash flow forecast
func (s *CashFlowForecastService) GenerateForecast(req CashFlowForecastRequest, userID string) (*CashFlowForecastResponse, error) {
	// Get user's default merchant
	branchID, err := s.getUserDefaultMerchant(userID)
	if err != nil {
		return nil, err
	}

	// Parse start date or use current date
	var startDate time.Time
	if req.StartDate != "" {
		if parsed, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			startDate = parsed
		} else {
			startDate = time.Now()
		}
	} else {
		startDate = time.Now()
	}

	// Calculate end date based on period type and number of periods
	var endDate time.Time
	if req.PeriodType == "month" {
		endDate = startDate.AddDate(0, req.Periods, 0)
	} else {
		// Default to weeks
		endDate = startDate.AddDate(0, 0, req.Periods*7)
	}

	// Get cash flow items in the date range
	var items []models.CashFlowItem
	if err := s.db.Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC").Find(&items).Error; err != nil {
		return nil, err
	}

	// Generate periods
	periods := s.generatePeriods(startDate, endDate, req.PeriodType, req.Periods, items)

	// Calculate totals
	totalInflows := decimal.Zero
	totalOutflows := decimal.Zero
	for _, period := range periods {
		totalInflows = totalInflows.Add(period.Inflows)
		totalOutflows = totalOutflows.Add(period.Outflows)
	}

	netCashFlow := totalInflows.Sub(totalOutflows)
	openingBalance := decimal.Zero // This could be calculated from actual bank balances
	closingBalance := openingBalance.Add(netCashFlow)

	return &CashFlowForecastResponse{
		StartDate:      startDate.Format("2006-01-02"),
		EndDate:        endDate.Format("2006-01-02"),
		OpeningBalance: openingBalance,
		Periods:        periods,
		TotalInflows:   totalInflows,
		TotalOutflows:  totalOutflows,
		NetCashFlow:    netCashFlow,
		ClosingBalance: closingBalance,
		GeneratedAt:    time.Now().Format("2006-01-02T15:04:05Z"),
	}, nil
}

// GetHistory returns historical cash flow data
func (s *CashFlowForecastService) GetHistory(req CashFlowHistoryRequest, userID string) (*CashFlowHistoryResponse, error) {
	// Get user's default merchant
	branchID, err := s.getUserDefaultMerchant(userID)
	if err != nil {
		return nil, err
	}

	// Build query
	query := s.db.Where("branch_id = ?", branchID)

	// Apply filters
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			query = query.Where("date >= ?", startDate)
		}
	}
	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			query = query.Where("date <= ?", endDate)
		}
	}
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// Get items
	var items []models.CashFlowItem
	if err := query.Order("date DESC").Find(&items).Error; err != nil {
		return nil, err
	}

	// Calculate summary
	summary := s.calculateSummary(items)

	return &CashFlowHistoryResponse{
		Items:       items,
		Summary:     summary,
		GeneratedAt: time.Now().Format("2006-01-02T15:04:05Z"),
	}, nil
}

// GetBudgetComparison returns budget comparison data
func (s *CashFlowForecastService) GetBudgetComparison(req BudgetComparisonRequest, userID string) (*BudgetComparisonResponse, error) {
	// Get user's default merchant
	branchID, err := s.getUserDefaultMerchant(userID)
	if err != nil {
		return nil, err
	}

	// Calculate date range
	var startDate, endDate time.Time
	var period string

	if req.Month > 0 {
		// Monthly comparison
		startDate = time.Date(req.Year, time.Month(req.Month), 1, 0, 0, 0, 0, time.UTC)
		endDate = startDate.AddDate(0, 1, -1)
		period = startDate.Format("January 2006")
	} else {
		// Yearly comparison
		startDate = time.Date(req.Year, 1, 1, 0, 0, 0, 0, time.UTC)
		endDate = time.Date(req.Year, 12, 31, 23, 59, 59, 0, time.UTC)
		period = startDate.Format("2006")
	}

	// Get actual cash flow data
	var items []models.CashFlowItem
	if err := s.db.Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Find(&items).Error; err != nil {
		return nil, err
	}

	// Calculate actual values
	actualInflows := decimal.Zero
	actualOutflows := decimal.Zero
	for _, item := range items {
		if item.Type == models.CashFlowItemType("Inflow") {
			actualInflows = actualInflows.Add(item.Amount)
		} else {
			actualOutflows = actualOutflows.Add(item.Amount)
		}
	}

	// For now, use zero budgeted amounts (would need budget data from database)
	budgetedInflows := decimal.Zero
	budgetedOutflows := decimal.Zero

	// Calculate variances
	revenueVariance := actualInflows.Sub(budgetedInflows)
	expenseVariance := actualOutflows.Sub(budgetedOutflows)
	netVariance := revenueVariance.Sub(expenseVariance)

	// Calculate variance percentages
	revenueVariancePercent := decimal.Zero
	if !budgetedInflows.IsZero() {
		revenueVariancePercent = revenueVariance.Div(budgetedInflows).Mul(decimal.NewFromInt(100))
	}

	expenseVariancePercent := decimal.Zero
	if !budgetedOutflows.IsZero() {
		expenseVariancePercent = expenseVariance.Div(budgetedOutflows).Mul(decimal.NewFromInt(100))
	}

	netBudgeted := budgetedInflows.Sub(budgetedOutflows)
	netActual := actualInflows.Sub(actualOutflows)
	netVariancePercent := decimal.Zero
	if !netBudgeted.IsZero() {
		netVariancePercent = netVariance.Div(netBudgeted).Mul(decimal.NewFromInt(100))
	}

	return &BudgetComparisonResponse{
		Period: period,
		Revenue: BudgetComparisonCategory{
			Budgeted:        budgetedInflows,
			Actual:          actualInflows,
			Variance:        revenueVariance,
			VariancePercent: revenueVariancePercent,
		},
		Expenses: BudgetComparisonCategory{
			Budgeted:        budgetedOutflows,
			Actual:          actualOutflows,
			Variance:        expenseVariance,
			VariancePercent: expenseVariancePercent,
		},
		Net: BudgetComparisonCategory{
			Budgeted:        netBudgeted,
			Actual:          netActual,
			Variance:        netVariance,
			VariancePercent: netVariancePercent,
		},
		GeneratedAt: time.Now().Format("2006-01-02T15:04:05Z"),
	}, nil
}

// Helper methods
func (s *CashFlowForecastService) getUserDefaultMerchant(userID string) (string, error) {
	var permission models.UserMerchantPermission
	if err := s.db.Where("user_id = ?", userID).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("no merchant found for user")
		}
		return "", err
	}
	return permission.MerchantID, nil
}

func (s *CashFlowForecastService) generatePeriods(startDate, endDate time.Time, periodType string, periods int, items []models.CashFlowItem) []CashFlowPeriod {
	var result []CashFlowPeriod
	currentBalance := decimal.Zero

	for i := 0; i < periods; i++ {
		var periodStart, periodEnd time.Time
		var periodName string

		if periodType == "month" {
			periodStart = startDate.AddDate(0, i, 0)
			periodEnd = periodStart.AddDate(0, 1, -1)
			periodName = periodStart.Format("January 2006")
		} else {
			// Default to weeks
			periodStart = startDate.AddDate(0, 0, i*7)
			periodEnd = periodStart.AddDate(0, 0, 6)
			periodName = "Week " + string(rune(i+1))
		}

		// Calculate inflows and outflows for this period
		inflows := decimal.Zero
		outflows := decimal.Zero

		for _, item := range items {
			if item.Date.After(periodStart.Add(-time.Second)) && item.Date.Before(periodEnd.Add(time.Second)) {
				if item.Type == models.CashFlowItemType("Inflow") {
					inflows = inflows.Add(item.Amount)
				} else {
					outflows = outflows.Add(item.Amount)
				}
			}
		}

		netCashFlow := inflows.Sub(outflows)
		currentBalance = currentBalance.Add(netCashFlow)

		result = append(result, CashFlowPeriod{
			Period:        periodName,
			StartDate:     periodStart.Format("2006-01-02"),
			EndDate:       periodEnd.Format("2006-01-02"),
			Inflows:       inflows,
			Outflows:      outflows,
			NetCashFlow:   netCashFlow,
			EndingBalance: currentBalance,
		})
	}

	return result
}

func (s *CashFlowForecastService) calculateSummary(items []models.CashFlowItem) CashFlowSummary {
	totalInflows := decimal.Zero
	totalOutflows := decimal.Zero

	for _, item := range items {
		if item.Type == models.CashFlowItemType("Inflow") {
			totalInflows = totalInflows.Add(item.Amount)
		} else {
			totalOutflows = totalOutflows.Add(item.Amount)
		}
	}

	return CashFlowSummary{
		TotalInflows:  totalInflows,
		TotalOutflows: totalOutflows,
		NetCashFlow:   totalInflows.Sub(totalOutflows),
		ItemCount:     len(items),
	}
}
