package services

import (
	"errors"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// RecurringCashFlowItemService handles recurring cash flow items
type RecurringCashFlowItemService struct {
	db *gorm.DB
}

func NewRecurringCashFlowItemService(db *gorm.DB) *RecurringCashFlowItemService {
	return &RecurringCashFlowItemService{db: db}
}

// Request/Response types
type CreateRecurringCashFlowItemRequest struct {
	Description string                    `json:"description" binding:"required"`
	Amount      decimal.Decimal           `json:"amount" binding:"required"`
	Type        models.CashFlowItemType   `json:"type" binding:"required"`
	Frequency   models.RecurringFrequency `json:"frequency" binding:"required"`
	StartDate   time.Time                 `json:"startDate" binding:"required"`
	EndDate     *time.Time                `json:"endDate"`
	NextDate    time.Time                 `json:"nextDate"`
	Status      models.RecurringStatus    `json:"status"`
	Category    *string                   `json:"category"`
	Notes       *string                   `json:"notes"`

	CustomInterval *int `json:"customInterval"`
}

type UpdateRecurringCashFlowItemRequest struct {
	Description *string                    `json:"description"`
	Amount      *decimal.Decimal           `json:"amount"`
	Type        *models.CashFlowItemType   `json:"type"`
	Frequency   *models.RecurringFrequency `json:"frequency"`
	StartDate   *time.Time                 `json:"startDate"`
	EndDate     *time.Time                 `json:"endDate"`
	NextDate    *time.Time                 `json:"nextDate"`
	Status      *models.RecurringStatus    `json:"status"`
	Category    *string                    `json:"category"`
	Notes       *string                    `json:"notes"`

	CustomInterval *int `json:"customInterval"`
}

type RecurringCashFlowFilters struct {
	Type     string `json:"type"`
	Status   string `json:"status"`
	Category string `json:"category"`
	IsActive *bool  `json:"isActive"`
}

// GetAllRecurringCashFlowItems returns all recurring cash flow items with pagination and filtering
func (s *RecurringCashFlowItemService) GetAllRecurringCashFlowItems(page, limit int, userID string, filters RecurringCashFlowFilters) ([]models.RecurringCashFlowItem, int64, error) {
	var items []models.RecurringCashFlowItem
	var total int64

	// Build query to get items from merchants user has access to
	query := s.db.Model(&models.RecurringCashFlowItem{}).
		Joins("JOIN user_merchant_permissions ON recurring_cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("user_merchant_permissions.user_id = ?", userID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("recurring_cash_flow_items.type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("recurring_cash_flow_items.status = ?", filters.Status)
	}
	if filters.Category != "" {
		query = query.Where("recurring_cash_flow_items.category = ?", filters.Category)
	}
	if filters.IsActive != nil {
		query = query.Where("recurring_cash_flow_items.is_active = ?", *filters.IsActive)
	}

	// Count total items
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get items with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Category").
		Order("recurring_cash_flow_items.next_date ASC").
		Offset(offset).Limit(limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

// GetRecurringCashFlowItemByID returns a recurring cash flow item by ID
func (s *RecurringCashFlowItemService) GetRecurringCashFlowItemByID(id, userID string) (*models.RecurringCashFlowItem, error) {
	var item models.RecurringCashFlowItem

	// Get item and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON recurring_cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("recurring_cash_flow_items.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		Preload("Merchant").Preload("Category").
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("recurring cash flow item not found or access denied")
		}
		return nil, err
	}

	return &item, nil
}

// CreateRecurringCashFlowItem creates a new recurring cash flow item
func (s *RecurringCashFlowItemService) CreateRecurringCashFlowItem(req CreateRecurringCashFlowItemRequest, userID string) (*models.RecurringCashFlowItem, error) {
	// Get user's default merchant
	branchID, err := s.getUserDefaultMerchant(userID)
	if err != nil {
		return nil, err
	}

	// Set default status if not provided
	status := req.Status
	if status == "" {
		status = models.RecurringStatus("Active")
	}

	// Set next date to start date if not provided
	nextDate := req.NextDate
	if nextDate.IsZero() {
		nextDate = req.StartDate
	}

	// Create item
	item := models.RecurringCashFlowItem{
		ID:             uuid.New().String(),
		BranchID:       branchID, // TODO: Update parameter to use branchID
		Description:    req.Description,
		Amount:         req.Amount,
		Type:           req.Type,
		Frequency:      req.Frequency,
		StartDate:      req.StartDate,
		EndDate:        req.EndDate,
		NextDate:       nextDate,
		Status:         status,
		CategoryID:     req.Category,
		Notes:          req.Notes,
		CustomInterval: req.CustomInterval,
	}

	if err := s.db.Create(&item).Error; err != nil {
		return nil, err
	}

	// Reload item with relationships
	if err := s.db.Preload("Merchant").Preload("Category").
		Where("id = ?", item.ID).First(&item).Error; err != nil {
		return nil, err
	}

	return &item, nil
}

// UpdateRecurringCashFlowItem updates an existing recurring cash flow item
func (s *RecurringCashFlowItemService) UpdateRecurringCashFlowItem(id string, req UpdateRecurringCashFlowItemRequest, userID string) (*models.RecurringCashFlowItem, error) {
	var item models.RecurringCashFlowItem

	// Get item and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON recurring_cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("recurring_cash_flow_items.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("recurring cash flow item not found or access denied")
		}
		return nil, err
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Amount != nil {
		updates["amount"] = *req.Amount
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Frequency != nil {
		updates["frequency"] = *req.Frequency
	}
	if req.StartDate != nil {
		updates["start_date"] = *req.StartDate
	}
	if req.EndDate != nil {
		updates["end_date"] = *req.EndDate
	}
	if req.NextDate != nil {
		updates["next_date"] = *req.NextDate
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Category != nil {
		updates["category_id"] = *req.Category
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.CustomInterval != nil {
		updates["custom_interval"] = *req.CustomInterval
	}

	if len(updates) > 0 {
		if err := s.db.Model(&item).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload item with relationships
	if err := s.db.Preload("Merchant").Preload("Category").
		Where("id = ?", id).First(&item).Error; err != nil {
		return nil, err
	}

	return &item, nil
}

// DeleteRecurringCashFlowItem deletes a recurring cash flow item
func (s *RecurringCashFlowItemService) DeleteRecurringCashFlowItem(id, userID string) error {
	var item models.RecurringCashFlowItem

	// Get item and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON recurring_cash_flow_items.branch_id = user_merchant_permissions.branch_id").
		Where("recurring_cash_flow_items.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("recurring cash flow item not found or access denied")
		}
		return err
	}

	// Delete the item
	return s.db.Delete(&item).Error
}

// Helper methods
func (s *RecurringCashFlowItemService) getUserDefaultMerchant(userID string) (string, error) {
	var permission models.UserMerchantPermission
	if err := s.db.Where("user_id = ?", userID).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("no merchant found for user")
		}
		return "", err
	}
	return permission.MerchantID, nil
}
