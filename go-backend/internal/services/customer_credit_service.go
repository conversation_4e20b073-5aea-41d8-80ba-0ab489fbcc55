package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CustomerCreditService handles customer credit-related business logic
type CustomerCreditService struct {
	db *gorm.DB
}

// NewCustomerCreditService creates a new CustomerCreditService
func NewCustomerCreditService(db *gorm.DB) *CustomerCreditService {
	return &CustomerCreditService{db: db}
}

// GetAllCustomerCredits retrieves all customer credits with pagination
func (s *CustomerCreditService) GetAllCustomerCredits(page, limit int, search string) ([]models.CustomerCredit, int64, error) {
	var customerCredits []models.CustomerCredit
	var total int64

	query := s.db.Model(&models.CustomerCredit{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
			Where("customers.name ILIKE ? OR customers.email ILIKE ? OR customer_credits.source ILIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer credits: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Customer").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&customerCredits).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer credits: %w", err)
	}

	return customerCredits, total, nil
}

// GetCustomerCreditByID retrieves a customer credit by ID
func (s *CustomerCreditService) GetCustomerCreditByID(id string) (*models.CustomerCredit, error) {
	var customerCredit models.CustomerCredit

	if err := s.db.Preload("Customer").First(&customerCredit, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("customer credit not found")
		}
		return nil, fmt.Errorf("failed to fetch customer credit: %w", err)
	}

	return &customerCredit, nil
}

// GetCustomerCreditsByCustomer retrieves customer credits for a specific customer
func (s *CustomerCreditService) GetCustomerCreditsByCustomer(customerID string, page, limit int, activeOnly bool) ([]models.CustomerCredit, int64, error) {
	var customerCredits []models.CustomerCredit
	var total int64

	query := s.db.Model(&models.CustomerCredit{}).Where("customer_id = ?", customerID)

	// Apply active filter if requested
	if activeOnly {
		query = query.Where("balance > 0")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer credits: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Customer").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&customerCredits).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer credits: %w", err)
	}

	return customerCredits, total, nil
}

// GetCustomerCreditsByMerchant retrieves customer credits for a specific merchant
func (s *CustomerCreditService) GetCustomerCreditsByMerchant(branchID string, page, limit int, search, source string, activeOnly bool) ([]models.CustomerCredit, int64, error) {
	var customerCredits []models.CustomerCredit
	var total int64

	query := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("customers.name ILIKE ? OR customers.email ILIKE ? OR customer_credits.source ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply source filter if provided
	if source != "" {
		query = query.Where("customer_credits.source = ?", source)
	}

	// Apply active filter if requested
	if activeOnly {
		query = query.Where("customer_credits.balance > 0")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count customer credits: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Customer").
		Offset(offset).Limit(limit).
		Order("customer_credits.created_at DESC").
		Find(&customerCredits).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch customer credits: %w", err)
	}

	return customerCredits, total, nil
}

// CreateCustomerCredit creates a new customer credit
func (s *CustomerCreditService) CreateCustomerCredit(customerCredit *models.CustomerCredit) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate customer exists
		var customer models.Customer
		if err := tx.First(&customer, "id = ?", customerCredit.CustomerID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer not found")
			}
			return fmt.Errorf("failed to validate customer: %w", err)
		}

		// Set balance equal to amount initially
		customerCredit.Balance = customerCredit.Amount

		// Validate customer credit
		if err := s.ValidateCustomerCredit(customerCredit); err != nil {
			return err
		}

		// Create the customer credit
		if err := tx.Create(customerCredit).Error; err != nil {
			return fmt.Errorf("failed to create customer credit: %w", err)
		}

		return nil
	})
}

// UpdateCustomerCredit updates an existing customer credit
func (s *CustomerCreditService) UpdateCustomerCredit(id string, updates *models.CustomerCredit) (*models.CustomerCredit, error) {
	var customerCredit models.CustomerCredit

	// Check if customer credit exists
	if err := s.db.First(&customerCredit, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("customer credit not found")
		}
		return nil, fmt.Errorf("failed to fetch customer credit: %w", err)
	}

	// Prevent updates to amount and balance directly
	updates.Amount = decimal.Zero
	updates.Balance = decimal.Zero

	var updatedCredit *models.CustomerCredit
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Update the customer credit
		if err := tx.Model(&customerCredit).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update customer credit: %w", err)
		}

		// Fetch updated customer credit with relationships
		if err := tx.Preload("Customer").First(&customerCredit, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated customer credit: %w", err)
		}

		updatedCredit = &customerCredit
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedCredit, nil
}

// DeleteCustomerCredit deletes a customer credit
func (s *CustomerCreditService) DeleteCustomerCredit(id string) error {
	var customerCredit models.CustomerCredit

	// Check if customer credit exists
	if err := s.db.First(&customerCredit, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("customer credit not found")
		}
		return fmt.Errorf("failed to fetch customer credit: %w", err)
	}

	// Prevent deletion if credit has been used
	if customerCredit.Balance.LessThan(customerCredit.Amount) {
		return fmt.Errorf("cannot delete customer credit that has been partially used")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete the customer credit
		if err := tx.Delete(&customerCredit).Error; err != nil {
			return fmt.Errorf("failed to delete customer credit: %w", err)
		}

		return nil
	})
}

// ApplyCustomerCredit applies customer credit to an invoice
func (s *CustomerCreditService) ApplyCustomerCredit(creditID, invoiceID string, amount decimal.Decimal) (*CreditApplicationResult, error) {
	var result *CreditApplicationResult

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get customer credit
		var customerCredit models.CustomerCredit
		if err := tx.Preload("Customer").First(&customerCredit, "id = ?", creditID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer credit not found")
			}
			return fmt.Errorf("failed to fetch customer credit: %w", err)
		}

		// Get invoice
		var invoice models.Invoice
		if err := tx.First(&invoice, "id = ?", invoiceID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("invoice not found")
			}
			return fmt.Errorf("failed to fetch invoice: %w", err)
		}

		// Validate that customer credit belongs to the same customer as the invoice
		if customerCredit.CustomerID != invoice.CustomerID {
			return fmt.Errorf("customer credit does not belong to the invoice customer")
		}

		// Validate amount
		if amount.LessThanOrEqual(decimal.Zero) {
			return fmt.Errorf("application amount must be greater than zero")
		}

		if amount.GreaterThan(customerCredit.Balance) {
			return fmt.Errorf("application amount cannot exceed available credit balance")
		}

		if amount.GreaterThan(invoice.BalanceAmount) {
			return fmt.Errorf("application amount cannot exceed invoice balance")
		}

		// Update customer credit balance
		newCreditBalance := customerCredit.Balance.Sub(amount)
		if err := tx.Model(&customerCredit).Update("balance", newCreditBalance).Error; err != nil {
			return fmt.Errorf("failed to update customer credit balance: %w", err)
		}

		// Update invoice balance
		newInvoiceBalance := invoice.BalanceAmount.Sub(amount)
		updates := map[string]interface{}{
			"balance_amount": newInvoiceBalance,
		}

		// Update invoice status if fully paid
		if newInvoiceBalance.IsZero() {
			updates["status"] = models.InvoiceStatusPaid
		}

		if err := tx.Model(&invoice).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update invoice balance: %w", err)
		}

		// Create credit application record
		creditApplication := &models.CreditApplication{
			CreditNoteID: creditID, // Using the same field for customer credit
			InvoiceID:    invoiceID,
			Amount:       amount,
			AppliedDate:  time.Now(),
		}

		if err := tx.Create(creditApplication).Error; err != nil {
			return fmt.Errorf("failed to create credit application record: %w", err)
		}

		result = &CreditApplicationResult{
			CreditID:            creditID,
			InvoiceID:           invoiceID,
			AppliedAmount:       amount,
			RemainingCredit:     newCreditBalance,
			InvoiceBalanceAfter: newInvoiceBalance,
			ApplicationID:       creditApplication.ID,
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetCustomerTotalCredits gets the total available credits for a customer
func (s *CustomerCreditService) GetCustomerTotalCredits(customerID string) (decimal.Decimal, error) {
	var totalCredits decimal.Decimal

	if err := s.db.Model(&models.CustomerCredit{}).
		Where("customer_id = ? AND balance > 0", customerID).
		Select("COALESCE(SUM(balance), 0)").
		Scan(&totalCredits).Error; err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate total customer credits: %w", err)
	}

	return totalCredits, nil
}

// GetCustomerCreditSummary gets summary statistics for customer credits
func (s *CustomerCreditService) GetCustomerCreditSummary(branchID string) (*CustomerCreditSummary, error) {
	var summary CustomerCreditSummary

	// Get total count
	if err := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ?", branchID).
		Count(&summary.TotalCredits).Error; err != nil {
		return nil, fmt.Errorf("failed to count customer credits: %w", err)
	}

	// Get active count (credits with balance > 0)
	if err := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ? AND customer_credits.balance > 0", branchID).
		Count(&summary.ActiveCredits).Error; err != nil {
		return nil, fmt.Errorf("failed to count active customer credits: %w", err)
	}

	// Get total amount
	var totalAmount decimal.Decimal
	if err := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ?", branchID).
		Select("COALESCE(SUM(customer_credits.amount), 0)").
		Scan(&totalAmount).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total amount: %w", err)
	}

	// Get total balance
	var totalBalance decimal.Decimal
	if err := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ?", branchID).
		Select("COALESCE(SUM(customer_credits.balance), 0)").
		Scan(&totalBalance).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total balance: %w", err)
	}

	// Get source counts
	sourceCounts := []struct {
		Source string
		Count  int64
	}{}

	if err := s.db.Model(&models.CustomerCredit{}).
		Joins("LEFT JOIN customers ON customer_credits.customer_id = customers.id").
		Where("customers.branch_id = ?", branchID).
		Select("customer_credits.source, COUNT(*) as count").
		Group("customer_credits.source").
		Scan(&sourceCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get source counts: %w", err)
	}

	// Map source counts
	summary.SourceCounts = make(map[string]int64)
	for _, sc := range sourceCounts {
		summary.SourceCounts[sc.Source] = sc.Count
	}

	summary.TotalAmount = totalAmount.String()
	summary.TotalBalance = totalBalance.String()

	return &summary, nil
}

// CreateCreditFromCreditNote creates customer credit from a credit note
func (s *CustomerCreditService) CreateCreditFromCreditNote(creditNoteID string) (*models.CustomerCredit, error) {
	var customerCredit *models.CustomerCredit

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get credit note
		var creditNote models.CreditNote
		if err := tx.Preload("Customer").First(&creditNote, "id = ?", creditNoteID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("credit note not found")
			}
			return fmt.Errorf("failed to fetch credit note: %w", err)
		}

		// Check if credit note is approved
		if creditNote.Status != models.CreditNoteStatusOpen {
			return fmt.Errorf("credit note must be in open status to create customer credit")
		}

		// Check if credit note has a customer
		if creditNote.CustomerID == nil {
			return fmt.Errorf("credit note must have a customer to create customer credit")
		}

		// Check if customer credit already exists for this credit note
		var existingCredit models.CustomerCredit
		if err := tx.First(&existingCredit, "source = ? AND source_id = ?", "credit_note", creditNoteID).Error; err == nil {
			return fmt.Errorf("customer credit already exists for this credit note")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check existing customer credit: %w", err)
		}

		// Create customer credit
		customerCredit = &models.CustomerCredit{
			CustomerID: *creditNote.CustomerID,
			Amount:     creditNote.BalanceAmount,
			Balance:    creditNote.BalanceAmount,
			Source:     "credit_note",
			SourceID:   &creditNoteID,
			Notes:      &creditNote.Reason,
		}

		if err := tx.Create(customerCredit).Error; err != nil {
			return fmt.Errorf("failed to create customer credit: %w", err)
		}

		// Update credit note status to closed
		if err := tx.Model(&creditNote).Update("status", models.CreditNoteStatusClosed).Error; err != nil {
			return fmt.Errorf("failed to update credit note status: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch the created customer credit with relationships
	return s.GetCustomerCreditByID(customerCredit.ID)
}

// ValidateCustomerCredit validates customer credit data
func (s *CustomerCreditService) ValidateCustomerCredit(customerCredit *models.CustomerCredit) error {
	if customerCredit.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if customerCredit.Amount.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("credit amount must be greater than zero")
	}
	if customerCredit.Balance.LessThan(decimal.Zero) {
		return fmt.Errorf("credit balance cannot be negative")
	}
	if customerCredit.Balance.GreaterThan(customerCredit.Amount) {
		return fmt.Errorf("credit balance cannot exceed credit amount")
	}
	if customerCredit.Source == "" {
		return fmt.Errorf("credit source is required")
	}

	// Validate source
	validSources := []string{
		"credit_note", "refund", "overpayment", "adjustment",
		"promotion", "loyalty", "manual", "return",
	}
	isValidSource := false
	for _, validSource := range validSources {
		if customerCredit.Source == validSource {
			isValidSource = true
			break
		}
	}
	if !isValidSource {
		return fmt.Errorf("invalid credit source: %s", customerCredit.Source)
	}

	return nil
}

// GetCustomerCreditHistory gets the credit application history for a customer
func (s *CustomerCreditService) GetCustomerCreditHistory(customerID string, page, limit int) ([]CreditHistoryItem, int64, error) {
	var historyItems []CreditHistoryItem
	var total int64

	// Get credit applications
	var applications []models.CreditApplication
	query := s.db.Model(&models.CreditApplication{}).
		Joins("LEFT JOIN customer_credits ON credit_applications.credit_note_id = customer_credits.id").
		Where("customer_credits.customer_id = ?", customerID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count credit applications: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Invoice").
		Offset(offset).Limit(limit).
		Order("applied_date DESC").
		Find(&applications).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch credit applications: %w", err)
	}

	// Convert to history items
	for _, app := range applications {
		historyItem := CreditHistoryItem{
			ID:          app.ID,
			Type:        "application",
			Amount:      app.Amount,
			Date:        app.AppliedDate,
			Description: fmt.Sprintf("Applied to Invoice %s", app.Invoice.InvoiceNumber),
		}
		historyItems = append(historyItems, historyItem)
	}

	return historyItems, total, nil
}

// CustomerCreditSummary represents summary statistics for customer credits
type CustomerCreditSummary struct {
	TotalCredits  int64            `json:"totalCredits"`
	ActiveCredits int64            `json:"activeCredits"`
	TotalAmount   string           `json:"totalAmount"`
	TotalBalance  string           `json:"totalBalance"`
	SourceCounts  map[string]int64 `json:"sourceCounts"`
}

// CreditApplicationResult represents the result of applying customer credit
type CreditApplicationResult struct {
	CreditID            string          `json:"creditId"`
	InvoiceID           string          `json:"invoiceId"`
	AppliedAmount       decimal.Decimal `json:"appliedAmount"`
	RemainingCredit     decimal.Decimal `json:"remainingCredit"`
	InvoiceBalanceAfter decimal.Decimal `json:"invoiceBalanceAfter"`
	ApplicationID       string          `json:"applicationId"`
}

// CreditHistoryItem represents a credit history item
type CreditHistoryItem struct {
	ID          string          `json:"id"`
	Type        string          `json:"type"` // "application", "creation", "adjustment"
	Amount      decimal.Decimal `json:"amount"`
	Date        time.Time       `json:"date"`
	Description string          `json:"description"`
}
