package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type InvoiceService struct {
	db *gorm.DB
}

func NewInvoiceService(db *gorm.DB) *InvoiceService {
	return &InvoiceService{db: db}
}

// CreateInvoiceRequest represents a request to create an invoice
type CreateInvoiceRequest struct {
	MerchantID      string                     `json:"merchantId"`
	CustomerID      string                     `json:"customerId" binding:"required"`
	IssueDate       time.Time                  `json:"issueDate" binding:"required"`
	DueDate         *time.Time                 `json:"dueDate"`
	Notes           *string                    `json:"notes"`
	Terms           *string                    `json:"terms"`
	Currency        string                     `json:"currency"`
	ExchangeRate    decimal.Decimal            `json:"exchangeRate"`
	Reference       *string                    `json:"reference"`
	PoNumber        *string                    `json:"poNumber"`
	ShippingAddress *string                    `json:"shippingAddress"`
	BillingAddress  *string                    `json:"billingAddress"`
	Items           []CreateInvoiceItemRequest `json:"items" binding:"required,min=1"`
}

// CreateInvoiceItemRequest represents an invoice item
type CreateInvoiceItemRequest struct {
	Description string          `json:"description" binding:"required"`
	Quantity    decimal.Decimal `json:"quantity" binding:"required"`
	UnitPrice   decimal.Decimal `json:"unitPrice" binding:"required"`
	TaxRateID   *string         `json:"taxRateId"`
}

// UpdateInvoiceRequest represents a request to update an invoice
type UpdateInvoiceRequest struct {
	CustomerID      *string                    `json:"customerId"`
	IssueDate       *time.Time                 `json:"issueDate"`
	DueDate         *time.Time                 `json:"dueDate"`
	Notes           *string                    `json:"notes"`
	Terms           *string                    `json:"terms"`
	Currency        *string                    `json:"currency"`
	ExchangeRate    *decimal.Decimal           `json:"exchangeRate"`
	Reference       *string                    `json:"reference"`
	PoNumber        *string                    `json:"poNumber"`
	ShippingAddress *string                    `json:"shippingAddress"`
	BillingAddress  *string                    `json:"billingAddress"`
	Status          *models.InvoiceStatus      `json:"status"`
	Items           []CreateInvoiceItemRequest `json:"items"`
}

// InvoicePaymentRequest represents a payment for an invoice
type InvoicePaymentRequest struct {
	Amount        decimal.Decimal `json:"amount" binding:"required"`
	PaymentDate   time.Time       `json:"paymentDate" binding:"required"`
	PaymentMethod *string         `json:"paymentMethod"`
	Reference     *string         `json:"reference"`
	Notes         *string         `json:"notes"`
}

// InvoiceResponse represents an invoice response
type InvoiceResponse struct {
	ID              string                  `json:"id"`
	MerchantID      string                  `json:"merchantId"`
	CustomerID      string                  `json:"customerId"`
	InvoiceNumber   string                  `json:"invoiceNumber"`
	Status          models.InvoiceStatus    `json:"status"`
	IssueDate       string                  `json:"issueDate"`
	DueDate         *string                 `json:"dueDate"`
	SubTotal        decimal.Decimal         `json:"subTotal"`
	TaxAmount       decimal.Decimal         `json:"taxAmount"`
	DiscountAmount  decimal.Decimal         `json:"discountAmount"`
	TotalAmount     decimal.Decimal         `json:"totalAmount"`
	PaidAmount      decimal.Decimal         `json:"paidAmount"`
	BalanceAmount   decimal.Decimal         `json:"balanceAmount"`
	Notes           *string                 `json:"notes"`
	Terms           *string                 `json:"terms"`
	CreatedAt       string                  `json:"createdAt"`
	UpdatedAt       string                  `json:"updatedAt"`
	Currency        string                  `json:"currency"`
	ExchangeRate    decimal.Decimal         `json:"exchangeRate"`
	Reference       *string                 `json:"reference"`
	PoNumber        *string                 `json:"poNumber"`
	ShippingAddress *string                 `json:"shippingAddress"`
	BillingAddress  *string                 `json:"billingAddress"`
	Customer        *models.Customer        `json:"customer,omitempty"`
	Items           []models.InvoiceItem    `json:"items,omitempty"`
	Payments        []models.InvoicePayment `json:"payments,omitempty"`
}

// GetAllInvoices returns all invoices with pagination, search, and sorting
func (s *InvoiceService) GetAllInvoices(page, limit int, search, sortBy, sortOrder, userID string) ([]InvoiceResponse, int64, error) {
	var invoices []models.Invoice
	var total int64

	// Build query to get invoices from branches user has access to
	query := s.db.Model(&models.Invoice{}).
		Joins("JOIN user_branch_permissions ON invoices.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ?", userID)

	// Apply search filter if provided
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("invoices.invoice_number ILIKE ? OR invoices.notes ILIKE ? OR invoices.reference ILIKE ? OR invoices.po_number ILIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern)
	}

	// Count total invoices after filtering
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	validSortFields := map[string]string{
		"invoice_number": "invoices.invoice_number",
		"created_at":     "invoices.created_at",
		"updated_at":     "invoices.updated_at",
		"issue_date":     "invoices.issue_date",
		"due_date":       "invoices.due_date",
		"total_amount":   "invoices.total_amount",
		"status":         "invoices.status",
	}

	if sortField, exists := validSortFields[sortBy]; exists {
		orderClause := sortField + " " + sortOrder
		query = query.Order(orderClause)
	} else {
		// Default sorting
		query = query.Order("invoices.created_at desc")
	}

	// Get invoices with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Customer").Preload("Items").Preload("Payments").
		Offset(offset).Limit(limit).Find(&invoices).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]InvoiceResponse, 0, len(invoices))
	for _, invoice := range invoices {
		responses = append(responses, s.toInvoiceResponse(invoice))
	}

	return responses, total, nil
}

// GetInvoicesByMerchant returns invoices for a specific merchant
func (s *InvoiceService) GetInvoicesByMerchant(branchID string, page, limit int, userID string) ([]InvoiceResponse, int64, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this merchant")
	}

	var invoices []models.Invoice
	var total int64

	// Count total invoices for this merchant
	if err := s.db.Model(&models.Invoice{}).Where("branch_id = ?", branchID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get invoices with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ?", branchID).
		Preload("Customer").Preload("Items").Preload("Payments").
		Offset(offset).Limit(limit).Find(&invoices).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]InvoiceResponse, 0, len(invoices))
	for _, invoice := range invoices {
		responses = append(responses, s.toInvoiceResponse(invoice))
	}

	return responses, total, nil
}

// GetInvoicesByCustomer returns invoices for a specific customer
func (s *InvoiceService) GetInvoicesByCustomer(branchID, customerID string, page, limit int) ([]InvoiceResponse, int64, error) {
	var invoices []models.Invoice
	var total int64

	// Count total invoices for this customer
	if err := s.db.Model(&models.Invoice{}).Where("branch_id = ? AND customer_id = ?", branchID, customerID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get invoices with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ? AND customer_id = ?", branchID, customerID).
		Preload("Customer").Preload("Items").Preload("Payments").
		Offset(offset).Limit(limit).Order("issue_date DESC").Find(&invoices).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]InvoiceResponse, 0, len(invoices))
	for _, invoice := range invoices {
		responses = append(responses, s.toInvoiceResponse(invoice))
	}

	return responses, total, nil
}

// GetInvoiceByID returns an invoice by ID
func (s *InvoiceService) GetInvoiceByID(id, userID string) (*InvoiceResponse, error) {
	var invoice models.Invoice

	// Get invoice and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON invoices.branch_id = user_branch_permissions.branch_id").
		Where("invoices.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		Preload("Customer").Preload("Items").Preload("Payments").
		First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invoice not found or access denied")
		}
		return nil, err
	}

	response := s.toInvoiceResponse(invoice)
	return &response, nil
}

// CreateInvoice creates a new invoice
func (s *InvoiceService) CreateInvoice(req CreateInvoiceRequest, userID string) (*InvoiceResponse, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, req.MerchantID) {
		return nil, errors.New("access denied to this merchant")
	}

	// Verify customer belongs to the merchant
	var customer models.Customer
	if err := s.db.Where("id = ? AND branch_id = ?", req.CustomerID, req.MerchantID).First(&customer).Error; err != nil {
		return nil, errors.New("customer not found or does not belong to this merchant")
	}

	// Set defaults
	currency := req.Currency
	if currency == "" {
		currency = "USD"
	}
	exchangeRate := req.ExchangeRate
	if exchangeRate.IsZero() {
		exchangeRate = decimal.NewFromInt(1)
	}

	// Generate invoice number
	invoiceNumber := s.generateInvoiceNumber(req.MerchantID)

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create invoice
	invoice := models.Invoice{
		ID:              uuid.New().String(),
		BranchID:        req.MerchantID, // TODO: Update request struct to use BranchID
		CustomerID:      req.CustomerID,
		InvoiceNumber:   invoiceNumber,
		Status:          models.InvoiceStatusDraft,
		IssueDate:       req.IssueDate,
		DueDate:         req.DueDate,
		Notes:           req.Notes,
		Terms:           req.Terms,
		Currency:        currency,
		ExchangeRate:    exchangeRate,
		Reference:       req.Reference,
		PoNumber:        req.PoNumber,
		ShippingAddress: req.ShippingAddress,
		BillingAddress:  req.BillingAddress,
	}

	if err := tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create invoice items and calculate totals
	var subTotal, taxAmount decimal.Decimal
	for i, itemReq := range req.Items {
		totalPrice := itemReq.Quantity.Mul(itemReq.UnitPrice)

		// Calculate tax if tax rate is provided
		var itemTaxAmount decimal.Decimal
		if itemReq.TaxRateID != nil {
			var taxRate models.TaxRate
			if err := tx.Where("id = ? AND branch_id = ?", *itemReq.TaxRateID, req.MerchantID).First(&taxRate).Error; err == nil {
				itemTaxAmount = totalPrice.Mul(taxRate.Rate).Div(decimal.NewFromInt(100))
			}
		}

		item := models.InvoiceItem{
			ID:          uuid.New().String(),
			InvoiceID:   invoice.ID,
			Description: itemReq.Description,
			Quantity:    itemReq.Quantity,
			UnitPrice:   itemReq.UnitPrice,
			TotalPrice:  totalPrice,
			TaxRateID:   itemReq.TaxRateID,
			TaxAmount:   itemTaxAmount,
			SortOrder:   i,
		}

		if err := tx.Create(&item).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		subTotal = subTotal.Add(totalPrice)
		taxAmount = taxAmount.Add(itemTaxAmount)
	}

	// Update invoice totals
	totalAmount := subTotal.Add(taxAmount)
	balanceAmount := totalAmount

	updates := map[string]interface{}{
		"sub_total":      subTotal,
		"tax_amount":     taxAmount,
		"total_amount":   totalAmount,
		"balance_amount": balanceAmount,
	}

	if err := tx.Model(&invoice).Updates(updates).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload invoice with relationships
	if err := s.db.Preload("Customer").Preload("Items").Preload("Payments").
		Where("id = ?", invoice.ID).First(&invoice).Error; err != nil {
		return nil, err
	}

	response := s.toInvoiceResponse(invoice)
	return &response, nil
}

// UpdateInvoice updates an existing invoice
func (s *InvoiceService) UpdateInvoice(id string, req UpdateInvoiceRequest, userID string) (*InvoiceResponse, error) {
	var invoice models.Invoice

	// Get invoice and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON invoices.branch_id = user_merchant_permissions.branch_id").
		Where("invoices.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invoice not found or access denied")
		}
		return nil, err
	}

	// Check if invoice can be updated (only draft invoices can be fully updated)
	if invoice.Status != models.InvoiceStatusDraft && len(req.Items) > 0 {
		return nil, errors.New("cannot update items of a non-draft invoice")
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update basic fields
	updates := make(map[string]interface{})
	if req.CustomerID != nil {
		// Verify customer belongs to the merchant
		var customer models.Customer
		if err := tx.Where("id = ? AND branch_id = ?", *req.CustomerID, invoice.BranchID).First(&customer).Error; err != nil {
			tx.Rollback()
			return nil, errors.New("customer not found or does not belong to this branch")
		}
		updates["customer_id"] = *req.CustomerID
	}
	if req.IssueDate != nil {
		updates["issue_date"] = *req.IssueDate
	}
	if req.DueDate != nil {
		updates["due_date"] = *req.DueDate
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.Terms != nil {
		updates["terms"] = *req.Terms
	}
	if req.Currency != nil {
		updates["currency"] = *req.Currency
	}
	if req.ExchangeRate != nil {
		updates["exchange_rate"] = *req.ExchangeRate
	}
	if req.Reference != nil {
		updates["reference"] = *req.Reference
	}
	if req.PoNumber != nil {
		updates["po_number"] = *req.PoNumber
	}
	if req.ShippingAddress != nil {
		updates["shipping_address"] = *req.ShippingAddress
	}
	if req.BillingAddress != nil {
		updates["billing_address"] = *req.BillingAddress
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := tx.Model(&invoice).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Update items if provided and invoice is draft
	if len(req.Items) > 0 && invoice.Status == models.InvoiceStatusDraft {
		// Delete existing items
		if err := tx.Where("invoice_id = ?", id).Delete(&models.InvoiceItem{}).Error; err != nil {
			tx.Rollback()
			return nil, err
		}

		// Create new items and recalculate totals
		var subTotal, taxAmount decimal.Decimal
		for i, itemReq := range req.Items {
			totalPrice := itemReq.Quantity.Mul(itemReq.UnitPrice)

			// Calculate tax if tax rate is provided
			var itemTaxAmount decimal.Decimal
			if itemReq.TaxRateID != nil {
				var taxRate models.TaxRate
				if err := tx.Where("id = ? AND branch_id = ?", *itemReq.TaxRateID, invoice.BranchID).First(&taxRate).Error; err == nil {
					itemTaxAmount = totalPrice.Mul(taxRate.Rate).Div(decimal.NewFromInt(100))
				}
			}

			item := models.InvoiceItem{
				ID:          uuid.New().String(),
				InvoiceID:   id,
				Description: itemReq.Description,
				Quantity:    itemReq.Quantity,
				UnitPrice:   itemReq.UnitPrice,
				TotalPrice:  totalPrice,
				TaxRateID:   itemReq.TaxRateID,
				TaxAmount:   itemTaxAmount,
				SortOrder:   i,
			}

			if err := tx.Create(&item).Error; err != nil {
				tx.Rollback()
				return nil, err
			}

			subTotal = subTotal.Add(totalPrice)
			taxAmount = taxAmount.Add(itemTaxAmount)
		}

		// Update invoice totals
		totalAmount := subTotal.Add(taxAmount)
		balanceAmount := totalAmount.Sub(invoice.PaidAmount)

		totalUpdates := map[string]interface{}{
			"sub_total":      subTotal,
			"tax_amount":     taxAmount,
			"total_amount":   totalAmount,
			"balance_amount": balanceAmount,
		}

		if err := tx.Model(&invoice).Updates(totalUpdates).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload invoice with relationships
	if err := s.db.Preload("Customer").Preload("Items").Preload("Payments").
		Where("id = ?", id).First(&invoice).Error; err != nil {
		return nil, err
	}

	response := s.toInvoiceResponse(invoice)
	return &response, nil
}

// DeleteInvoice deletes an invoice (only draft invoices can be deleted)
func (s *InvoiceService) DeleteInvoice(id, userID string) error {
	var invoice models.Invoice

	// Get invoice and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON invoices.branch_id = user_merchant_permissions.branch_id").
		Where("invoices.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("invoice not found or access denied")
		}
		return err
	}

	// Only allow deletion of draft invoices
	if invoice.Status != models.InvoiceStatusDraft {
		return errors.New("only draft invoices can be deleted")
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Delete invoice items first
	if err := tx.Where("invoice_id = ?", id).Delete(&models.InvoiceItem{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete invoice
	if err := tx.Delete(&invoice).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// AddInvoicePayment adds a payment to an invoice
func (s *InvoiceService) AddInvoicePayment(invoiceID string, req InvoicePaymentRequest, userID string) error {
	var invoice models.Invoice

	// Get invoice and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON invoices.branch_id = user_merchant_permissions.branch_id").
		Where("invoices.id = ? AND user_merchant_permissions.user_id = ?", invoiceID, userID).
		First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("invoice not found or access denied")
		}
		return err
	}

	// Check if payment amount is valid
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.New("payment amount must be greater than zero")
	}

	if req.Amount.GreaterThan(invoice.BalanceAmount) {
		return errors.New("payment amount cannot exceed balance amount")
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create payment
	payment := models.InvoicePayment{
		ID:            uuid.New().String(),
		InvoiceID:     invoiceID,
		Amount:        req.Amount,
		PaymentDate:   req.PaymentDate,
		PaymentMethod: req.PaymentMethod,
		Reference:     req.Reference,
		Notes:         req.Notes,
	}

	if err := tx.Create(&payment).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update invoice paid amount and balance
	newPaidAmount := invoice.PaidAmount.Add(req.Amount)
	newBalanceAmount := invoice.TotalAmount.Sub(newPaidAmount)

	// Update status based on payment
	var newStatus models.InvoiceStatus
	if newBalanceAmount.IsZero() {
		newStatus = models.InvoiceStatusPaid
	} else if newPaidAmount.GreaterThan(decimal.Zero) {
		newStatus = models.InvoiceStatusPartiallyPaid
	} else {
		newStatus = invoice.Status
	}

	updates := map[string]interface{}{
		"paid_amount":    newPaidAmount,
		"balance_amount": newBalanceAmount,
		"status":         newStatus,
	}

	if err := tx.Model(&invoice).Updates(updates).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// AddPaymentToInvoice adds a payment to an invoice (for customer portal)
func (s *InvoiceService) AddPaymentToInvoice(invoiceID string, payment *models.InvoicePayment, userID string) error {
	var invoice models.Invoice

	// Get invoice (for customer portal, we don't check user permissions)
	if err := s.db.Where("id = ?", invoiceID).First(&invoice).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("invoice not found")
		}
		return err
	}

	// Check if payment amount is valid
	if payment.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.New("payment amount must be greater than zero")
	}

	if payment.Amount.GreaterThan(invoice.BalanceAmount) {
		return errors.New("payment amount cannot exceed balance amount")
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Set payment ID if not set
	if payment.ID == "" {
		payment.ID = uuid.New().String()
	}

	// Create payment
	if err := tx.Create(payment).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update invoice paid amount and balance
	newPaidAmount := invoice.PaidAmount.Add(payment.Amount)
	newBalanceAmount := invoice.TotalAmount.Sub(newPaidAmount)

	// Update status based on payment
	var newStatus models.InvoiceStatus
	if newBalanceAmount.IsZero() {
		newStatus = models.InvoiceStatusPaid
	} else if newPaidAmount.GreaterThan(decimal.Zero) {
		newStatus = models.InvoiceStatusPartiallyPaid
	} else {
		newStatus = invoice.Status
	}

	updates := map[string]interface{}{
		"paid_amount":    newPaidAmount,
		"balance_amount": newBalanceAmount,
		"status":         newStatus,
	}

	if err := tx.Model(&invoice).Updates(updates).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Helper methods
func (s *InvoiceService) hasMerchantAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *InvoiceService) generateInvoiceNumber(branchID string) string {
	var count int64
	s.db.Model(&models.Invoice{}).Where("branch_id = ?", branchID).Count(&count)
	return "INV-" + branchID[:8] + "-" + fmt.Sprintf("%06d", count+1)
}

// toInvoiceResponse converts an Invoice model to InvoiceResponse
func (s *InvoiceService) toInvoiceResponse(invoice models.Invoice) InvoiceResponse {
	var dueDate *string
	if invoice.DueDate != nil {
		formatted := invoice.DueDate.Format("2006-01-02T15:04:05Z")
		dueDate = &formatted
	}

	return InvoiceResponse{
		ID:              invoice.ID,
		MerchantID:      invoice.BranchID, // TODO: Update response struct to use BranchID
		CustomerID:      invoice.CustomerID,
		InvoiceNumber:   invoice.InvoiceNumber,
		Status:          invoice.Status,
		IssueDate:       invoice.IssueDate.Format("2006-01-02T15:04:05Z"),
		DueDate:         dueDate,
		SubTotal:        invoice.SubTotal,
		TaxAmount:       invoice.TaxAmount,
		DiscountAmount:  invoice.DiscountAmount,
		TotalAmount:     invoice.TotalAmount,
		PaidAmount:      invoice.PaidAmount,
		BalanceAmount:   invoice.BalanceAmount,
		Notes:           invoice.Notes,
		Terms:           invoice.Terms,
		CreatedAt:       invoice.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:       invoice.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Currency:        invoice.Currency,
		ExchangeRate:    invoice.ExchangeRate,
		Reference:       invoice.Reference,
		PoNumber:        invoice.PoNumber,
		ShippingAddress: invoice.ShippingAddress,
		BillingAddress:  invoice.BillingAddress,
		Customer:        &invoice.Customer,
		Items:           invoice.Items,
		Payments:        invoice.Payments,
	}
}
