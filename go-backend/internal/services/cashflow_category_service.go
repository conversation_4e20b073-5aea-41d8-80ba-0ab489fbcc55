package services

import (
	"errors"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CashFlowCategoryService struct {
	db *gorm.DB
}

func NewCashFlowCategoryService(db *gorm.DB) *CashFlowCategoryService {
	return &CashFlowCategoryService{db: db}
}

// Request/Response types
type CreateCashFlowCategoryRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
	Color       *string `json:"color"`
	IsActive    bool    `json:"isActive"`
}

type UpdateCashFlowCategoryRequest struct {
	Name        *string `json:"name"`
	Description *string `json:"description"`
	Color       *string `json:"color"`
	IsActive    *bool   `json:"isActive"`
}

type CashFlowCategoryFilters struct {
	Type   string `json:"type"`
	Search string `json:"search"`
	Active *bool  `json:"active"`
}

// GetAllCashFlowCategories returns all cash flow categories with pagination and filtering
func (s *CashFlowCategoryService) GetAllCashFlowCategories(page, limit int, userID string, filters CashFlowCategoryFilters) ([]models.CashFlowCategory, int64, error) {
	var categories []models.CashFlowCategory
	var total int64

	// Build query to get categories from merchants user has access to
	query := s.db.Model(&models.CashFlowCategory{}).
		Joins("JOIN user_merchant_permissions ON cash_flow_categories.branch_id = user_merchant_permissions.branch_id").
		Where("user_merchant_permissions.user_id = ?", userID)

	// Apply filters
	if filters.Type != "" && filters.Type != "Both" {
		query = query.Where("cash_flow_categories.type = ? OR cash_flow_categories.type = 'Both'", filters.Type)
	}
	if filters.Search != "" {
		query = query.Where("cash_flow_categories.name ILIKE ? OR cash_flow_categories.description ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%")
	}
	if filters.Active != nil {
		query = query.Where("cash_flow_categories.is_active = ?", *filters.Active)
	}

	// Count total categories
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get categories with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").
		Order("cash_flow_categories.name ASC").
		Offset(offset).Limit(limit).Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// GetCashFlowCategoryByID returns a cash flow category by ID
func (s *CashFlowCategoryService) GetCashFlowCategoryByID(id, userID string) (*models.CashFlowCategory, error) {
	var category models.CashFlowCategory

	// Get category and check access through merchant
	if err := s.db.Joins("JOIN user_merchant_permissions ON cash_flow_categories.branch_id = user_merchant_permissions.branch_id").
		Where("cash_flow_categories.id = ? AND user_merchant_permissions.user_id = ?", id, userID).
		Preload("Merchant").
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("cash flow category not found or access denied")
		}
		return nil, err
	}

	return &category, nil
}

// CreateCashFlowCategory creates a new cash flow category
func (s *CashFlowCategoryService) CreateCashFlowCategory(req CreateCashFlowCategoryRequest, userID string) (*models.CashFlowCategory, error) {
	// Get user's default branch
	branchID, err := s.getUserDefaultBranch(userID)
	if err != nil {
		return nil, err
	}

	// Check if category name already exists for this branch
	var existingCategory models.CashFlowCategory
	if err := s.db.Where("branch_id = ? AND name = ?", branchID, req.Name).First(&existingCategory).Error; err == nil {
		return nil, errors.New("category name already exists for this branch")
	}

	// Note: Parent categories not supported in current model

	// Create category
	category := models.CashFlowCategory{
		ID:          uuid.New().String(),
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		IsActive:    req.IsActive,
	}

	if err := s.db.Create(&category).Error; err != nil {
		return nil, err
	}

	// Reload category with relationships
	if err := s.db.Preload("Branch").
		Where("id = ?", category.ID).First(&category).Error; err != nil {
		return nil, err
	}

	return &category, nil
}

// UpdateCashFlowCategory updates an existing cash flow category
func (s *CashFlowCategoryService) UpdateCashFlowCategory(id string, req UpdateCashFlowCategoryRequest, userID string) (*models.CashFlowCategory, error) {
	var category models.CashFlowCategory

	// Get category and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON cash_flow_categories.branch_id = user_branch_permissions.branch_id").
		Where("cash_flow_categories.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("cash flow category not found or access denied")
		}
		return nil, err
	}

	// Check if category name is being changed and if it conflicts
	if req.Name != nil && *req.Name != category.Name {
		var existingCategory models.CashFlowCategory
		if err := s.db.Where("branch_id = ? AND name = ? AND id != ?", category.BranchID, *req.Name, id).First(&existingCategory).Error; err == nil {
			return nil, errors.New("category name already exists for this branch")
		}
	}

	// Note: Parent categories not supported in current model

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Color != nil {
		updates["color"] = *req.Color
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) > 0 {
		if err := s.db.Model(&category).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload category with relationships
	if err := s.db.Preload("Branch").
		Where("id = ?", id).First(&category).Error; err != nil {
		return nil, err
	}

	return &category, nil
}

// DeleteCashFlowCategory soft deletes a cash flow category
func (s *CashFlowCategoryService) DeleteCashFlowCategory(id, userID string) error {
	var category models.CashFlowCategory

	// Get category and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON cash_flow_categories.branch_id = user_branch_permissions.branch_id").
		Where("cash_flow_categories.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("cash flow category not found or access denied")
		}
		return err
	}

	// Check if category has children
	var childCount int64
	if err := s.db.Model(&models.CashFlowCategory{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return err
	}
	if childCount > 0 {
		return errors.New("cannot delete category with child categories")
	}

	// Check if category is being used by cash flow items
	var itemCount int64
	if err := s.db.Model(&models.CashFlowItem{}).Where("category_id = ?", id).Count(&itemCount).Error; err != nil {
		return err
	}
	if itemCount > 0 {
		return errors.New("cannot delete category with existing cash flow items")
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&category).Update("is_active", false).Error
}

// Helper methods
func (s *CashFlowCategoryService) getUserDefaultBranch(userID string) (string, error) {
	var permission models.UserBranchPermission
	if err := s.db.Where("user_id = ?", userID).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", errors.New("no branch found for user")
		}
		return "", err
	}
	return permission.BranchID, nil
}
