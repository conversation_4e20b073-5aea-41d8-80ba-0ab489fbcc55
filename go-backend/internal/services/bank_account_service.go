package services

import (
	"errors"
	"fmt"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// BankAccountService handles bank account-related business logic
type BankAccountService struct {
	db *gorm.DB
}

// NewBankAccountService creates a new BankAccountService
func NewBankAccountService(db *gorm.DB) *BankAccountService {
	return &BankAccountService{db: db}
}

// GetAllBankAccounts retrieves all bank accounts with pagination, filtered by user's accessible branches
func (s *BankAccountService) GetAllBankAccounts(page, limit int, search, accountType, bankName string, isActive *bool, sortBy, sortOrder, userID string) ([]models.BankAccount, int64, error) {
	var bankAccounts []models.BankAccount
	var total int64

	// Build query to get bank accounts from branches user has access to
	query := s.db.Model(&models.BankAccount{}).
		Joins("JOIN user_branch_permissions ON bank_accounts.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ?", userID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("bank_accounts.account_name ILIKE ? OR bank_accounts.bank_name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account type filter if provided
	if accountType != "" {
		query = query.Where("bank_accounts.account_type = ?", accountType)
	}

	// Apply bank name filter if provided
	if bankName != "" {
		query = query.Where("bank_accounts.bank_name ILIKE ?", "%"+bankName+"%")
	}

	// Apply active status filter if provided
	if isActive != nil {
		query = query.Where("bank_accounts.is_active = ?", *isActive)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank accounts: %w", err)
	}

	// Build order clause
	var orderClause string
	switch sortBy {
	case "accountName":
		orderClause = "bank_accounts.account_name " + sortOrder
	case "bankName":
		orderClause = "bank_accounts.bank_name " + sortOrder
	case "accountType":
		orderClause = "bank_accounts.account_type " + sortOrder
	case "balance":
		orderClause = "bank_accounts.balance " + sortOrder
	default:
		orderClause = "bank_accounts.account_name " + sortOrder
	}

	// Apply pagination and sorting
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("ChartOfAccount").
		Offset(offset).Limit(limit).
		Order(orderClause).
		Find(&bankAccounts).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank accounts: %w", err)
	}

	return bankAccounts, total, nil
}

// GetBankAccountByID retrieves a bank account by ID
func (s *BankAccountService) GetBankAccountByID(id string) (*models.BankAccount, error) {
	var bankAccount models.BankAccount

	if err := s.db.Preload("Branch").Preload("ChartOfAccount").
		First(&bankAccount, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank account not found")
		}
		return nil, fmt.Errorf("failed to fetch bank account: %w", err)
	}

	return &bankAccount, nil
}

// GetBankAccountsByBranch retrieves bank accounts for a specific branch
func (s *BankAccountService) GetBankAccountsByBranch(branchID string, page, limit int, search string) ([]models.BankAccount, int64, error) {
	var bankAccounts []models.BankAccount
	var total int64

	query := s.db.Model(&models.BankAccount{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("account_name ILIKE ? OR bank_name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count bank accounts: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("ChartOfAccount").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&bankAccounts).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch bank accounts: %w", err)
	}

	return bankAccounts, total, nil
}

// GetBankAccountsByMerchant retrieves bank accounts for a specific merchant (deprecated - use GetBankAccountsByBranch)
func (s *BankAccountService) GetBankAccountsByMerchant(branchID string, page, limit int, search string) ([]models.BankAccount, int64, error) {
	// For backward compatibility, we'll try to find the branch associated with this merchant
	// This is a temporary solution during the migration period
	return s.GetBankAccountsByBranch(branchID, page, limit, search)
}

// CreateBankAccount creates a new bank account
func (s *BankAccountService) CreateBankAccount(bankAccount *models.BankAccount) error {
	// Validate branch exists
	var branch models.Branch
	if err := s.db.First(&branch, "id = ?", bankAccount.BranchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("branch not found")
		}
		return fmt.Errorf("failed to validate branch: %w", err)
	}

	// Validate account exists
	var account models.ChartOfAccount
	if err := s.db.First(&account, "id = ?", bankAccount.ChartOfAccountID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("chart of account not found")
		}
		return fmt.Errorf("failed to validate account: %w", err)
	}

	// Check if account is already linked to another bank account
	var existingBankAccount models.BankAccount
	if err := s.db.First(&existingBankAccount, "chart_of_account_id = ?", bankAccount.ChartOfAccountID).Error; err == nil {
		return fmt.Errorf("chart of account is already linked to another bank account")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check account linkage: %w", err)
	}

	// Create the bank account
	if err := s.db.Create(bankAccount).Error; err != nil {
		return fmt.Errorf("failed to create bank account: %w", err)
	}

	return nil
}

// UpdateBankAccount updates an existing bank account
func (s *BankAccountService) UpdateBankAccount(id string, updates *models.BankAccount) (*models.BankAccount, error) {
	var bankAccount models.BankAccount

	// Check if bank account exists
	if err := s.db.First(&bankAccount, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank account not found")
		}
		return nil, fmt.Errorf("failed to fetch bank account: %w", err)
	}

	// If updating account ID, validate the new account
	if updates.ChartOfAccountID != "" && updates.ChartOfAccountID != bankAccount.ChartOfAccountID {
		var account models.ChartOfAccount
		if err := s.db.First(&account, "id = ?", updates.ChartOfAccountID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("chart of account not found")
			}
			return nil, fmt.Errorf("failed to validate account: %w", err)
		}

		// Check if new account is already linked to another bank account
		var existingBankAccount models.BankAccount
		if err := s.db.First(&existingBankAccount, "chart_of_account_id = ? AND id != ?", updates.ChartOfAccountID, id).Error; err == nil {
			return nil, fmt.Errorf("chart of account is already linked to another bank account")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to check account linkage: %w", err)
		}
	}

	// Update the bank account
	if err := s.db.Model(&bankAccount).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update bank account: %w", err)
	}

	// Fetch updated bank account with relationships
	if err := s.db.Preload("Branch").Preload("ChartOfAccount").
		First(&bankAccount, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated bank account: %w", err)
	}

	return &bankAccount, nil
}

// DeleteBankAccount deletes a bank account
func (s *BankAccountService) DeleteBankAccount(id string) error {
	var bankAccount models.BankAccount

	// Check if bank account exists
	if err := s.db.First(&bankAccount, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("bank account not found")
		}
		return fmt.Errorf("failed to fetch bank account: %w", err)
	}

	// Check if bank account has related transactions
	var transactionCount int64
	if err := s.db.Model(&models.BankTransaction{}).
		Where("bank_account_id = ?", id).Count(&transactionCount).Error; err != nil {
		return fmt.Errorf("failed to check related transactions: %w", err)
	}

	if transactionCount > 0 {
		return fmt.Errorf("cannot delete bank account with existing transactions")
	}

	// Delete the bank account
	if err := s.db.Delete(&bankAccount).Error; err != nil {
		return fmt.Errorf("failed to delete bank account: %w", err)
	}

	return nil
}

// ValidateBankAccount validates bank account data
func (s *BankAccountService) ValidateBankAccount(bankAccount *models.BankAccount) error {
	if bankAccount.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if bankAccount.ChartOfAccountID == "" {
		return fmt.Errorf("chart of account ID is required")
	}
	if bankAccount.AccountName == "" {
		return fmt.Errorf("account name is required")
	}

	return nil
}

// GetBankAccountByChartOfAccountID retrieves a bank account by chart of account ID
func (s *BankAccountService) GetBankAccountByChartOfAccountID(chartOfAccountID string) (*models.BankAccount, error) {
	var bankAccount models.BankAccount

	if err := s.db.Where("chart_of_account_id = ?", chartOfAccountID).First(&bankAccount).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("bank account not found")
		}
		return nil, fmt.Errorf("failed to fetch bank account: %w", err)
	}

	return &bankAccount, nil
}

// GetUserDefaultBranchID gets the user's default branch ID
func (s *BankAccountService) GetUserDefaultBranchID(userID string) (string, error) {
	var user models.User

	if err := s.db.Preload("DefaultOrganization.Branches").First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", fmt.Errorf("user not found")
		}
		return "", fmt.Errorf("failed to fetch user: %w", err)
	}

	if user.DefaultOrganization == nil {
		return "", fmt.Errorf("user has no default organization")
	}

	if len(user.DefaultOrganization.Branches) == 0 {
		return "", fmt.Errorf("user's default organization has no branches")
	}

	// Return the first active branch
	for _, branch := range user.DefaultOrganization.Branches {
		if branch.IsActive {
			return branch.ID, nil
		}
	}

	return "", fmt.Errorf("user's default organization has no active branches")
}
