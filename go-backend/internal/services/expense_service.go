package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type ExpenseService struct {
	db *gorm.DB
}

func NewExpenseService(db *gorm.DB) *ExpenseService {
	return &ExpenseService{db: db}
}

// CreateExpenseRequest represents a request to create an expense (frontend compatible)
type CreateExpenseRequest struct {
	Date          string  `json:"date" binding:"required"`
	VendorID      string  `json:"vendor_id" binding:"required"`
	Category      string  `json:"category" binding:"required"`
	Description   string  `json:"description" binding:"required"`
	Amount        float64 `json:"amount" binding:"required"`
	PaymentMethod string  `json:"payment_method" binding:"required"`
	ReceiptURL    *string `json:"receipt_url"`
	BranchID      string  `json:"branch_id"` // Will be set by the handler
}

// CreateExpenseRequestInternal represents the internal structure for creating an expense
type CreateExpenseRequestInternal struct {
	MerchantID   string          `json:"merchantId" binding:"required"`
	VendorID     *string         `json:"vendorId"`
	AccountID    string          `json:"accountId" binding:"required"`
	Amount       decimal.Decimal `json:"amount" binding:"required"`
	Description  string          `json:"description" binding:"required"`
	ExpenseDate  time.Time       `json:"expenseDate" binding:"required"`
	Reference    *string         `json:"reference"`
	Notes        *string         `json:"notes"`
	ReceiptURL   *string         `json:"receiptUrl"`
	TaxAmount    decimal.Decimal `json:"taxAmount"`
	TaxRateID    *string         `json:"taxRateId"`
	Currency     string          `json:"currency"`
	ExchangeRate decimal.Decimal `json:"exchangeRate"`
}

// UpdateExpenseRequest represents a request to update an expense
type UpdateExpenseRequest struct {
	VendorID     *string               `json:"vendorId"`
	Amount       *decimal.Decimal      `json:"amount"`
	Description  *string               `json:"description"`
	ExpenseDate  *time.Time            `json:"expenseDate"`
	Reference    *string               `json:"reference"`
	Notes        *string               `json:"notes"`
	ReceiptURL   *string               `json:"receiptUrl"`
	TaxAmount    *decimal.Decimal      `json:"taxAmount"`
	Currency     *string               `json:"currency"`
	ExchangeRate *decimal.Decimal      `json:"exchangeRate"`
	Status       *models.ExpenseStatus `json:"status"`
}

// ExpenseResponse represents an expense response (frontend compatible)
type ExpenseResponse struct {
	ID            string  `json:"id"`
	Date          string  `json:"date"`
	VendorID      string  `json:"vendor_id"`
	VendorName    string  `json:"vendor_name,omitempty"`
	Category      string  `json:"category"`
	Description   string  `json:"description"`
	Amount        float64 `json:"amount"`
	PaymentMethod string  `json:"payment_method"`
	Status        string  `json:"status"`
	ReceiptURL    *string `json:"receipt_url,omitempty"`
	CreatedAt     string  `json:"created_at"`
	UpdatedAt     string  `json:"updated_at"`

	// Enhanced fields for better expense tracking
	BillID            *string `json:"bill_id,omitempty"`
	AssetID           *string `json:"asset_id,omitempty"`
	AssetName         *string `json:"asset_name,omitempty"`
	EmployeeID        *string `json:"employee_id,omitempty"`
	EmployeeName      *string `json:"employee_name,omitempty"`
	PurchaseOrderID   *string `json:"purchase_order_id,omitempty"`
	ProjectID         *string `json:"project_id,omitempty"`
	CostCenter        *string `json:"cost_center,omitempty"`
	Department        *string `json:"department,omitempty"`
	ExpenseCategoryID *string `json:"expense_category_id,omitempty"`
	CategoryName      *string `json:"category_name,omitempty"`
	BankAccountID     *string `json:"bank_account_id,omitempty"`
	BankAccountName   *string `json:"bank_account_name,omitempty"`
	ApprovedBy        *string `json:"approved_by,omitempty"`
	ApprovedByName    *string `json:"approved_by_name,omitempty"`
	ApprovedAt        *string `json:"approved_at,omitempty"`
	PaidAt            *string `json:"paid_at,omitempty"`
	SubTotal          float64 `json:"sub_total"`
	TaxAmount         float64 `json:"tax_amount"`
	DiscountAmount    float64 `json:"discount_amount"`
	CreatedBy         *string `json:"created_by,omitempty"`
	CreatedByName     *string `json:"created_by_name,omitempty"`
	IsRecurring       bool    `json:"is_recurring"`

	// Internal fields (not sent to frontend)
	MerchantID   string          `json:"-"`
	AccountID    string          `json:"-"`
	Reference    *string         `json:"-"`
	Notes        *string         `json:"-"`
	TaxRateID    *string         `json:"-"`
	Currency     string          `json:"-"`
	ExchangeRate decimal.Decimal `json:"-"`
	Vendor       *models.Vendor  `json:"-"`
}

// GetAllExpenses returns all expenses with pagination
func (s *ExpenseService) GetAllExpenses(page, limit int, userID string) ([]ExpenseResponse, int64, error) {
	var expenses []models.Expense
	var total int64

	// Build query to get expenses from branches/merchants user has access to
	// Support both branch permissions (new) and merchant permissions (legacy)
	query := s.db.Model(&models.Expense{}).
		Where(`
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions WHERE user_id = ?
			)
		`, userID, userID)

	// Count total expenses
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get expenses with pagination and preload relationships
	offset := (page - 1) * limit
	if err := query.Preload("Vendor").
		Preload("Account").
		Preload("TaxRate").
		Preload("Bill").
		Preload("Asset").
		Preload("Employee").
		Preload("PurchaseOrder").
		Preload("ExpenseCategory").
		Preload("BankAccount").
		Preload("ApproverUser").
		Preload("CreatorUser").
		Preload("ExpensePayments").
		Offset(offset).Limit(limit).Find(&expenses).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]ExpenseResponse, 0, len(expenses))
	for _, expense := range expenses {
		responses = append(responses, s.toExpenseResponse(expense))
	}

	return responses, total, nil
}

// GetExpensesByMerchant returns expenses for a specific merchant
func (s *ExpenseService) GetExpensesByMerchant(branchID string, page, limit int, userID string) ([]ExpenseResponse, int64, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this merchant")
	}

	var expenses []models.Expense
	var total int64

	// Count total expenses for this merchant
	if err := s.db.Model(&models.Expense{}).Where("branch_id = ?", branchID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get expenses with pagination and preload relationships
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ?", branchID).
		Preload("Vendor").
		Preload("Account").
		Preload("TaxRate").
		Preload("Bill").
		Preload("Asset").
		Preload("Employee").
		Preload("PurchaseOrder").
		Preload("ExpenseCategory").
		Preload("BankAccount").
		Preload("ApproverUser").
		Preload("CreatorUser").
		Preload("ExpensePayments").
		Offset(offset).Limit(limit).Find(&expenses).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]ExpenseResponse, 0, len(expenses))
	for _, expense := range expenses {
		responses = append(responses, s.toExpenseResponse(expense))
	}

	return responses, total, nil
}

// GetExpenseByID returns an expense by ID
func (s *ExpenseService) GetExpenseByID(id, userID string) (*ExpenseResponse, error) {
	var expense models.Expense

	// Get expense and check access through branch/merchant permissions
	if err := s.db.Where(`
		expenses.id = ? AND (
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		Preload("Vendor").
		Preload("Account").
		Preload("TaxRate").
		Preload("Bill").
		Preload("Asset").
		Preload("Employee").
		Preload("PurchaseOrder").
		Preload("ExpenseCategory").
		Preload("BankAccount").
		Preload("ApproverUser").
		Preload("CreatorUser").
		Preload("ExpensePayments").
		First(&expense).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("expense not found or access denied")
		}
		return nil, err
	}

	response := s.toExpenseResponse(expense)
	return &response, nil
}

// CreateExpense creates a new expense (frontend compatible)
func (s *ExpenseService) CreateExpense(req CreateExpenseRequest, userID string) (*ExpenseResponse, error) {
	// Parse the date
	expenseDate, err := time.Parse(time.RFC3339, req.Date)
	if err != nil {
		return nil, errors.New("invalid date format")
	}

	// Convert amount to decimal
	amount := decimal.NewFromFloat(req.Amount)

	// Get the user's default branch if not provided
	branchID := req.BranchID
	if branchID == "" {
		// Try to get user's default branch first
		var userBranch models.UserBranchPermission
		if err := s.db.Where("user_id = ?", userID).First(&userBranch).Error; err != nil {
			// If no branch permissions found, fall back to merchant permissions
			// This is for backward compatibility during the transition period
			var userMerchant models.UserMerchantPermission
			if err := s.db.Where("user_id = ?", userID).First(&userMerchant).Error; err != nil {
				return nil, errors.New("no branch or merchant access found for user")
			}
			// Use merchant ID as branch ID for now (temporary solution)
			branchID = userMerchant.MerchantID
		} else {
			branchID = userBranch.BranchID
		}
	}

	// Check if user has access to this branch (or merchant for backward compatibility)
	if !s.hasBranchAccess(userID, branchID) && !s.hasMerchantAccess(userID, branchID) {
		return nil, errors.New("access denied to this branch")
	}

	// Verify vendor belongs to the branch
	var vendor models.Vendor
	if err := s.db.Where("id = ? AND branch_id = ?", req.VendorID, branchID).First(&vendor).Error; err != nil {
		return nil, errors.New("vendor not found or does not belong to this branch")
	}

	// Get a default expense account (we'll need to create a mapping or use a default)
	// For now, let's get the first expense account for this branch
	var account models.ChartOfAccount
	if err := s.db.Where("branch_id = ? AND account_type = ?", branchID, "Expense").First(&account).Error; err != nil {
		// If no expense account found, create a default one or use any account
		if err := s.db.Where("branch_id = ?", branchID).First(&account).Error; err != nil {
			return nil, errors.New("no accounts found for this branch")
		}
	}

	// Create expense
	expense := models.Expense{
		ID:           uuid.New().String(),
		BranchID:     branchID,
		VendorID:     &req.VendorID,
		AccountID:    account.ID,
		Amount:       amount,
		Description:  req.Description,
		ExpenseDate:  expenseDate,
		ReceiptURL:   req.ReceiptURL,
		Currency:     "USD",
		ExchangeRate: decimal.NewFromInt(1),
		Status:       models.ExpenseStatusPending,
		CreatedBy:    &userID,
		SubTotal:     amount, // For now, set subtotal same as amount
		// Note: Category and PaymentMethod are not stored in the current model
		// They could be stored in Notes or Reference for now
		Notes: &req.Category, // Store category in notes for now
	}

	if req.PaymentMethod != "" {
		reference := req.PaymentMethod
		expense.Reference = &reference
	}

	if err := s.db.Create(&expense).Error; err != nil {
		return nil, err
	}

	// Reload expense with relationships
	if err := s.db.Preload("Vendor").
		Preload("Account").
		Preload("TaxRate").
		Preload("Bill").
		Preload("Asset").
		Preload("Employee").
		Preload("PurchaseOrder").
		Preload("ExpenseCategory").
		Preload("BankAccount").
		Preload("ApproverUser").
		Preload("CreatorUser").
		Preload("ExpensePayments").
		Where("id = ?", expense.ID).First(&expense).Error; err != nil {
		return nil, err
	}

	response := s.toExpenseResponse(expense)
	return &response, nil
}

// UpdateExpense updates an existing expense
func (s *ExpenseService) UpdateExpense(id string, req UpdateExpenseRequest, userID string) (*ExpenseResponse, error) {
	var expense models.Expense

	// Get expense and check access through branch/merchant permissions
	if err := s.db.Where(`
		expenses.id = ? AND (
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		First(&expense).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("expense not found or access denied")
		}
		return nil, err
	}

	// Check if expense can be updated (only pending expenses can be fully updated)
	if expense.Status == models.ExpenseStatusApproved && req.Amount != nil {
		return nil, errors.New("cannot update amount of an approved expense")
	}

	// Update fields
	updates := make(map[string]interface{})

	if req.VendorID != nil {
		// Verify vendor belongs to the merchant if provided
		if *req.VendorID != "" {
			var vendor models.Vendor
			if err := s.db.Where("id = ? AND branch_id = ?", *req.VendorID, expense.BranchID).First(&vendor).Error; err != nil {
				return nil, errors.New("vendor not found or does not belong to this branch")
			}
		}
		updates["vendor_id"] = req.VendorID
	}

	if req.Amount != nil {
		updates["amount"] = *req.Amount
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.ExpenseDate != nil {
		updates["expense_date"] = *req.ExpenseDate
	}
	if req.Reference != nil {
		updates["reference"] = *req.Reference
	}
	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}
	if req.ReceiptURL != nil {
		updates["receipt_url"] = *req.ReceiptURL
	}
	if req.TaxAmount != nil {
		updates["tax_amount"] = *req.TaxAmount
	}
	if req.Currency != nil {
		updates["currency"] = *req.Currency
	}
	if req.ExchangeRate != nil {
		updates["exchange_rate"] = *req.ExchangeRate
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&expense).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// Reload expense with relationships
	if err := s.db.Preload("Vendor").
		Preload("Account").
		Preload("TaxRate").
		Preload("Bill").
		Preload("Asset").
		Preload("Employee").
		Preload("PurchaseOrder").
		Preload("ExpenseCategory").
		Preload("BankAccount").
		Preload("ApproverUser").
		Preload("CreatorUser").
		Preload("ExpensePayments").
		Where("id = ?", id).First(&expense).Error; err != nil {
		return nil, err
	}

	response := s.toExpenseResponse(expense)
	return &response, nil
}

// DeleteExpense deletes an expense (only pending expenses can be deleted)
func (s *ExpenseService) DeleteExpense(id, userID string) error {
	var expense models.Expense

	// Get expense and check access through branch/merchant permissions
	if err := s.db.Where(`
		expenses.id = ? AND (
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions WHERE user_id = ?
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions WHERE user_id = ?
			)
		)
	`, id, userID, userID).
		First(&expense).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("expense not found or access denied")
		}
		return err
	}

	// Only allow deletion of pending expenses
	if expense.Status != models.ExpenseStatusPending {
		return errors.New("only pending expenses can be deleted")
	}

	return s.db.Delete(&expense).Error
}

// ApproveExpense approves an expense
func (s *ExpenseService) ApproveExpense(id, userID string) error {
	var expense models.Expense

	// Get expense and check access through branch/merchant permissions with appropriate permission levels
	if err := s.db.Where(`
		expenses.id = ? AND (
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions
				WHERE user_id = ? AND permission_level IN ('Owner', 'Admin', 'Manager')
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions
				WHERE user_id = ? AND permission_level IN ('Owner', 'Admin', 'Manager')
			)
		)
	`, id, userID, userID).
		First(&expense).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("expense not found or insufficient permissions")
		}
		return err
	}

	// Only pending expenses can be approved
	if expense.Status != models.ExpenseStatusPending {
		return errors.New("only pending expenses can be approved")
	}

	// Update status and approval fields
	now := time.Now()
	updates := map[string]interface{}{
		"status":      models.ExpenseStatusApproved,
		"approved_by": userID,
		"approved_at": now,
	}

	return s.db.Model(&expense).Updates(updates).Error
}

// RejectExpense rejects an expense
func (s *ExpenseService) RejectExpense(id, userID string, reason *string) error {
	var expense models.Expense

	// Get expense and check access through branch/merchant permissions with appropriate permission levels
	if err := s.db.Where(`
		expenses.id = ? AND (
			expenses.branch_id IN (
				SELECT branch_id FROM user_branch_permissions
				WHERE user_id = ? AND permission_level IN ('Owner', 'Admin', 'Manager')
			) OR expenses.branch_id IN (
				SELECT branch_id FROM user_merchant_permissions
				WHERE user_id = ? AND permission_level IN ('Owner', 'Admin', 'Manager')
			)
		)
	`, id, userID, userID).
		First(&expense).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("expense not found or insufficient permissions")
		}
		return err
	}

	// Only pending expenses can be rejected
	if expense.Status != models.ExpenseStatusPending {
		return errors.New("only pending expenses can be rejected")
	}

	updates := map[string]interface{}{
		"status": models.ExpenseStatusRejected,
	}
	if reason != nil {
		updates["notes"] = *reason
	}

	return s.db.Model(&expense).Updates(updates).Error
}

// Helper methods
func (s *ExpenseService) hasMerchantAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *ExpenseService) hasBranchAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserBranchPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *ExpenseService) generateExpenseNumber(branchID string) string {
	var count int64
	s.db.Model(&models.Expense{}).Where("branch_id = ?", branchID).Count(&count)
	return "EXP-" + branchID[:8] + "-" + fmt.Sprintf("%06d", count+1)
}

// toExpenseResponse converts an Expense model to ExpenseResponse
func (s *ExpenseService) toExpenseResponse(expense models.Expense) ExpenseResponse {
	// Extract vendor ID and name
	vendorID := ""
	vendorName := ""
	if expense.VendorID != nil {
		vendorID = *expense.VendorID
		if expense.Vendor != nil {
			vendorName = expense.Vendor.Name
		}
	}

	// Extract category from notes (temporary solution)
	category := ""
	if expense.Notes != nil {
		category = *expense.Notes
	}

	// Extract payment method from reference (temporary solution)
	paymentMethod := ""
	if expense.Reference != nil {
		paymentMethod = *expense.Reference
	}

	// Convert amounts to float64
	amount, _ := expense.Amount.Float64()
	subTotal, _ := expense.SubTotal.Float64()
	taxAmount, _ := expense.TaxAmount.Float64()
	discountAmount, _ := expense.DiscountAmount.Float64()

	// Convert status to string
	status := string(expense.Status)

	// Format approval and payment dates
	var approvedAt *string
	if expense.ApprovedAt != nil {
		formatted := expense.ApprovedAt.Format("2006-01-02T15:04:05Z")
		approvedAt = &formatted
	}

	var paidAt *string
	if expense.PaidAt != nil {
		formatted := expense.PaidAt.Format("2006-01-02T15:04:05Z")
		paidAt = &formatted
	}

	// Get related entity names (these would need to be preloaded)
	var assetName *string
	if expense.Asset != nil {
		assetName = &expense.Asset.Name
	}

	var employeeName *string
	if expense.Employee != nil {
		fullName := expense.Employee.FirstName + " " + expense.Employee.LastName
		employeeName = &fullName
	}

	var categoryName *string
	if expense.ExpenseCategory != nil {
		categoryName = &expense.ExpenseCategory.Name
	}

	var bankAccountName *string
	if expense.BankAccount != nil {
		bankAccountName = &expense.BankAccount.AccountName
	}

	var approvedByName *string
	if expense.ApproverUser != nil && expense.ApproverUser.Name != nil {
		approvedByName = expense.ApproverUser.Name
	}

	var createdByName *string
	if expense.CreatorUser != nil && expense.CreatorUser.Name != nil {
		createdByName = expense.CreatorUser.Name
	}

	return ExpenseResponse{
		ID:            expense.ID,
		Date:          expense.ExpenseDate.Format("2006-01-02T15:04:05Z"),
		VendorID:      vendorID,
		VendorName:    vendorName,
		Category:      category,
		Description:   expense.Description,
		Amount:        amount,
		PaymentMethod: paymentMethod,
		Status:        status,
		ReceiptURL:    expense.ReceiptURL,
		CreatedAt:     expense.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:     expense.UpdatedAt.Format("2006-01-02T15:04:05Z"),

		// Enhanced fields
		BillID:            expense.BillID,
		AssetID:           expense.AssetID,
		AssetName:         assetName,
		EmployeeID:        expense.EmployeeID,
		EmployeeName:      employeeName,
		PurchaseOrderID:   expense.PurchaseOrderID,
		ProjectID:         expense.ProjectID,
		CostCenter:        expense.CostCenter,
		Department:        expense.Department,
		ExpenseCategoryID: expense.ExpenseCategoryID,
		CategoryName:      categoryName,
		BankAccountID:     expense.BankAccountID,
		BankAccountName:   bankAccountName,
		ApprovedBy:        expense.ApprovedBy,
		ApprovedByName:    approvedByName,
		ApprovedAt:        approvedAt,
		PaidAt:            paidAt,
		SubTotal:          subTotal,
		TaxAmount:         taxAmount,
		DiscountAmount:    discountAmount,
		CreatedBy:         expense.CreatedBy,
		CreatedByName:     createdByName,
		IsRecurring:       expense.IsRecurring,

		// Internal fields
		MerchantID:   expense.BranchID,
		AccountID:    expense.AccountID,
		Reference:    expense.Reference,
		Notes:        expense.Notes,
		TaxRateID:    expense.TaxRateID,
		Currency:     expense.Currency,
		ExchangeRate: expense.ExchangeRate,
		Vendor:       expense.Vendor,
	}
}
