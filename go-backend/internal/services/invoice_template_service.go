package services

import (
	"errors"
	"fmt"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// InvoiceTemplateService handles invoice template-related business logic
type InvoiceTemplateService struct {
	db *gorm.DB
}

// NewInvoiceTemplateService creates a new InvoiceTemplateService
func NewInvoiceTemplateService(db *gorm.DB) *InvoiceTemplateService {
	return &InvoiceTemplateService{db: db}
}

// GetAllInvoiceTemplates retrieves all invoice templates with pagination
func (s *InvoiceTemplateService) GetAllInvoiceTemplates(page, limit int, search string) ([]models.InvoiceTemplate, int64, error) {
	var templates []models.InvoiceTemplate
	var total int64

	query := s.db.Model(&models.InvoiceTemplate{})

	// Apply search filter if provided
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count invoice templates: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch invoice templates: %w", err)
	}

	return templates, total, nil
}

// GetInvoiceTemplateByID retrieves an invoice template by ID
func (s *InvoiceTemplateService) GetInvoiceTemplateByID(id string) (*models.InvoiceTemplate, error) {
	var template models.InvoiceTemplate

	if err := s.db.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
		First(&template, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invoice template not found")
		}
		return nil, fmt.Errorf("failed to fetch invoice template: %w", err)
	}

	return &template, nil
}

// GetInvoiceTemplatesByMerchant retrieves invoice templates for a specific merchant
func (s *InvoiceTemplateService) GetInvoiceTemplatesByMerchant(branchID string, page, limit int, search string, activeOnly bool) ([]models.InvoiceTemplate, int64, error) {
	var templates []models.InvoiceTemplate
	var total int64

	query := s.db.Model(&models.InvoiceTemplate{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply active filter if requested
	if activeOnly {
		query = query.Where("is_active = ?", true)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count invoice templates: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("is_default DESC, name ASC").
		Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch invoice templates: %w", err)
	}

	return templates, total, nil
}

// GetDefaultInvoiceTemplate retrieves the default invoice template for a merchant
func (s *InvoiceTemplateService) GetDefaultInvoiceTemplate(branchID string) (*models.InvoiceTemplate, error) {
	var template models.InvoiceTemplate

	if err := s.db.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
		Where("branch_id = ? AND is_default = ? AND is_active = ?", branchID, true, true).
		First(&template).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("default invoice template not found")
		}
		return nil, fmt.Errorf("failed to fetch default invoice template: %w", err)
	}

	return &template, nil
}

// CreateInvoiceTemplate creates a new invoice template with items
func (s *InvoiceTemplateService) CreateInvoiceTemplate(template *models.InvoiceTemplate, items []models.InvoiceTemplateItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", template.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// If this is set as default, unset other default templates for this branch
		if template.IsDefault {
			if err := tx.Model(&models.InvoiceTemplate{}).
				Where("branch_id = ? AND is_default = ?", template.BranchID, true).
				Update("is_default", false).Error; err != nil {
				return fmt.Errorf("failed to unset existing default templates: %w", err)
			}
		}

		// Create the invoice template
		if err := tx.Create(template).Error; err != nil {
			return fmt.Errorf("failed to create invoice template: %w", err)
		}

		// Create invoice template items
		for i := range items {
			items[i].TemplateID = template.ID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
		}

		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create invoice template items: %w", err)
			}
		}

		return nil
	})
}

// UpdateInvoiceTemplate updates an existing invoice template
func (s *InvoiceTemplateService) UpdateInvoiceTemplate(id string, updates *models.InvoiceTemplate) (*models.InvoiceTemplate, error) {
	var template models.InvoiceTemplate

	// Check if invoice template exists
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invoice template not found")
		}
		return nil, fmt.Errorf("failed to fetch invoice template: %w", err)
	}

	var updatedTemplate *models.InvoiceTemplate
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If this is being set as default, unset other default templates for this merchant
		if updates.IsDefault && !template.IsDefault {
			if err := tx.Model(&models.InvoiceTemplate{}).
				Where("branch_id = ? AND is_default = ? AND id != ?", template.BranchID, true, id).
				Update("is_default", false).Error; err != nil {
				return fmt.Errorf("failed to unset existing default templates: %w", err)
			}
		}

		// Update the invoice template
		if err := tx.Model(&template).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update invoice template: %w", err)
		}

		// Fetch updated invoice template with relationships
		if err := tx.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
			First(&template, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated invoice template: %w", err)
		}

		updatedTemplate = &template
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedTemplate, nil
}

// UpdateInvoiceTemplateItems updates the items for an invoice template
func (s *InvoiceTemplateService) UpdateInvoiceTemplateItems(templateID string, items []models.InvoiceTemplateItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Verify template exists
		var template models.InvoiceTemplate
		if err := tx.First(&template, "id = ?", templateID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("invoice template not found")
			}
			return fmt.Errorf("failed to validate template: %w", err)
		}

		// Delete existing items
		if err := tx.Where("template_id = ?", templateID).Delete(&models.InvoiceTemplateItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing template items: %w", err)
		}

		// Create new items
		for i := range items {
			items[i].TemplateID = templateID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
		}

		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create template items: %w", err)
			}
		}

		return nil
	})
}

// SetDefaultTemplate sets a template as the default for a merchant
func (s *InvoiceTemplateService) SetDefaultTemplate(templateID string) (*models.InvoiceTemplate, error) {
	var template models.InvoiceTemplate

	// Check if template exists
	if err := s.db.First(&template, "id = ?", templateID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invoice template not found")
		}
		return nil, fmt.Errorf("failed to fetch invoice template: %w", err)
	}

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Unset other default templates for this merchant
		if err := tx.Model(&models.InvoiceTemplate{}).
			Where("branch_id = ? AND is_default = ? AND id != ?", template.BranchID, true, templateID).
			Update("is_default", false).Error; err != nil {
			return fmt.Errorf("failed to unset existing default templates: %w", err)
		}

		// Set this template as default
		if err := tx.Model(&template).Update("is_default", true).Error; err != nil {
			return fmt.Errorf("failed to set template as default: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &template, nil
}

// DeleteInvoiceTemplate deletes an invoice template
func (s *InvoiceTemplateService) DeleteInvoiceTemplate(id string) error {
	var template models.InvoiceTemplate

	// Check if invoice template exists
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("invoice template not found")
		}
		return fmt.Errorf("failed to fetch invoice template: %w", err)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete template items first
		if err := tx.Where("template_id = ?", id).Delete(&models.InvoiceTemplateItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete template items: %w", err)
		}

		// Delete the template
		if err := tx.Delete(&template).Error; err != nil {
			return fmt.Errorf("failed to delete invoice template: %w", err)
		}

		return nil
	})
}

// CloneInvoiceTemplate creates a copy of an existing template
func (s *InvoiceTemplateService) CloneInvoiceTemplate(templateID, newName string) (*models.InvoiceTemplate, error) {
	// Get the original template with items
	originalTemplate, err := s.GetInvoiceTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	var clonedTemplate *models.InvoiceTemplate
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// Create new template (copy of original)
		newTemplate := &models.InvoiceTemplate{
			BranchID:    originalTemplate.BranchID,
			Name:        newName,
			Description: originalTemplate.Description,
			Terms:       originalTemplate.Terms,
			Notes:       originalTemplate.Notes,
			IsDefault:   false, // Cloned templates are never default
			IsActive:    true,
		}

		if err := tx.Create(newTemplate).Error; err != nil {
			return fmt.Errorf("failed to create cloned template: %w", err)
		}

		// Clone template items
		if len(originalTemplate.Items) > 0 {
			newItems := make([]models.InvoiceTemplateItem, len(originalTemplate.Items))
			for i, item := range originalTemplate.Items {
				newItems[i] = models.InvoiceTemplateItem{
					TemplateID:  newTemplate.ID,
					Description: item.Description,
					Quantity:    item.Quantity,
					UnitPrice:   item.UnitPrice,
					TaxRateID:   item.TaxRateID,
					SortOrder:   item.SortOrder,
				}
			}

			if err := tx.Create(&newItems).Error; err != nil {
				return fmt.Errorf("failed to create cloned template items: %w", err)
			}
		}

		// Fetch the complete cloned template
		if err := tx.Preload("Merchant").Preload("Items").Preload("Items.TaxRate").
			First(newTemplate, "id = ?", newTemplate.ID).Error; err != nil {
			return fmt.Errorf("failed to fetch cloned template: %w", err)
		}

		clonedTemplate = newTemplate
		return nil
	})

	if err != nil {
		return nil, err
	}

	return clonedTemplate, nil
}

// GetInvoiceTemplateSummary gets summary statistics for invoice templates
func (s *InvoiceTemplateService) GetInvoiceTemplateSummary(branchID string) (*InvoiceTemplateSummary, error) {
	var summary InvoiceTemplateSummary

	// Get total count
	if err := s.db.Model(&models.InvoiceTemplate{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count templates: %w", err)
	}

	// Get active count
	if err := s.db.Model(&models.InvoiceTemplate{}).
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Count(&summary.ActiveTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count active templates: %w", err)
	}

	// Get default template count (should be 0 or 1)
	if err := s.db.Model(&models.InvoiceTemplate{}).
		Where("branch_id = ? AND is_default = ?", branchID, true).
		Count(&summary.DefaultTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count default templates: %w", err)
	}

	summary.InactiveTemplates = summary.TotalTemplates - summary.ActiveTemplates

	return &summary, nil
}

// InvoiceTemplateSummary represents summary statistics for invoice templates
type InvoiceTemplateSummary struct {
	TotalTemplates    int64 `json:"totalTemplates"`
	ActiveTemplates   int64 `json:"activeTemplates"`
	InactiveTemplates int64 `json:"inactiveTemplates"`
	DefaultTemplates  int64 `json:"defaultTemplates"`
}

// ValidateInvoiceTemplate validates invoice template data
func (s *InvoiceTemplateService) ValidateInvoiceTemplate(template *models.InvoiceTemplate) error {
	if template.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if template.Name == "" {
		return fmt.Errorf("template name is required")
	}
	if len(template.Name) > 255 {
		return fmt.Errorf("template name cannot exceed 255 characters")
	}

	return nil
}

// ValidateInvoiceTemplateItem validates invoice template item data
func (s *InvoiceTemplateService) ValidateInvoiceTemplateItem(item *models.InvoiceTemplateItem) error {
	if item.TemplateID == "" {
		return fmt.Errorf("template ID is required")
	}
	if item.Description == "" {
		return fmt.Errorf("item description is required")
	}
	if item.Quantity.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("item quantity must be greater than zero")
	}
	if item.UnitPrice.LessThan(decimal.Zero) {
		return fmt.Errorf("item unit price cannot be negative")
	}

	return nil
}
