package services

import (
	"errors"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type TaxRateService struct {
	db *gorm.DB
}

func NewTaxRateService(db *gorm.DB) *TaxRateService {
	return &TaxRateService{db: db}
}

// CreateTaxRateRequest represents a request to create a tax rate
type CreateTaxRateRequest struct {
	MerchantID  string          `json:"merchantId" binding:"required"`
	Name        string          `json:"name" binding:"required"`
	Rate        decimal.Decimal `json:"rate" binding:"required"`
	Description *string         `json:"description"`
	IsActive    bool            `json:"isActive"`
	IsDefault   bool            `json:"isDefault"`
}

// UpdateTaxRateRequest represents a request to update a tax rate
type UpdateTaxRateRequest struct {
	Name        *string          `json:"name"`
	Rate        *decimal.Decimal `json:"rate"`
	Description *string          `json:"description"`
	IsActive    *bool            `json:"isActive"`
	IsDefault   *bool            `json:"isDefault"`
}

// TaxRateResponse represents a tax rate response
type TaxRateResponse struct {
	ID          string          `json:"id"`
	MerchantID  string          `json:"merchantId"`
	Name        string          `json:"name"`
	Rate        decimal.Decimal `json:"rate"`
	Description *string         `json:"description"`
	IsActive    bool            `json:"isActive"`
	IsDefault   bool            `json:"isDefault"`
	CreatedAt   string          `json:"createdAt"`
	UpdatedAt   string          `json:"updatedAt"`
}

// GetAllTaxRates returns all tax rates with pagination
func (s *TaxRateService) GetAllTaxRates(page, limit int, userID string) ([]TaxRateResponse, int64, error) {
	var taxRates []models.TaxRate
	var total int64

	// Build query to get tax rates from branches user has access to
	query := s.db.Model(&models.TaxRate{}).
		Joins("JOIN user_branch_permissions ON tax_rates.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ?", userID)

	// Count total tax rates
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get tax rates with pagination
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&taxRates).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]TaxRateResponse, 0, len(taxRates))
	for _, taxRate := range taxRates {
		responses = append(responses, s.toTaxRateResponse(taxRate))
	}

	return responses, total, nil
}

// GetTaxRatesByBranch returns tax rates for a specific branch
func (s *TaxRateService) GetTaxRatesByBranch(branchID string, page, limit int, userID string) ([]TaxRateResponse, int64, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this branch")
	}

	var taxRates []models.TaxRate
	var total int64

	// Count total tax rates for this branch
	if err := s.db.Model(&models.TaxRate{}).Where("branch_id = ? AND is_active = ?", branchID, true).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get tax rates with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("is_default DESC, name ASC").
		Offset(offset).Limit(limit).Find(&taxRates).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]TaxRateResponse, 0, len(taxRates))
	for _, taxRate := range taxRates {
		responses = append(responses, s.toTaxRateResponse(taxRate))
	}

	return responses, total, nil
}

// GetTaxRateByID returns a tax rate by ID
func (s *TaxRateService) GetTaxRateByID(id, userID string) (*TaxRateResponse, error) {
	var taxRate models.TaxRate

	// Get tax rate and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON tax_rates.branch_id = user_branch_permissions.branch_id").
		Where("tax_rates.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&taxRate).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("tax rate not found or access denied")
		}
		return nil, err
	}

	response := s.toTaxRateResponse(taxRate)
	return &response, nil
}

// CreateTaxRate creates a new tax rate
func (s *TaxRateService) CreateTaxRate(req CreateTaxRateRequest, userID string) (*TaxRateResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, req.MerchantID) {
		return nil, errors.New("access denied to this branch")
	}

	// Validate rate (expecting decimal format: 0.08 for 8%)
	if req.Rate.LessThan(decimal.Zero) || req.Rate.GreaterThan(decimal.NewFromInt(1)) {
		return nil, errors.New("tax rate must be between 0 and 1 (e.g., 0.08 for 8%)")
	}

	// Check if tax rate name already exists for this branch
	var existingTaxRate models.TaxRate
	if err := s.db.Where("branch_id = ? AND name = ?", req.MerchantID, req.Name).First(&existingTaxRate).Error; err == nil {
		return nil, errors.New("tax rate with this name already exists for this branch")
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// If this is set as default, unset other defaults
	if req.IsDefault {
		if err := tx.Model(&models.TaxRate{}).
			Where("branch_id = ?", req.MerchantID).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Create tax rate
	taxRate := models.TaxRate{
		ID:          uuid.New().String(),
		BranchID:    req.MerchantID, // TODO: Update request struct to use BranchID
		Name:        req.Name,
		Rate:        req.Rate,
		Description: req.Description,
		IsActive:    req.IsActive,
		IsDefault:   req.IsDefault,
	}

	if err := tx.Create(&taxRate).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	response := s.toTaxRateResponse(taxRate)
	return &response, nil
}

// UpdateTaxRate updates an existing tax rate
func (s *TaxRateService) UpdateTaxRate(id string, req UpdateTaxRateRequest, userID string) (*TaxRateResponse, error) {
	var taxRate models.TaxRate

	// Get tax rate and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON tax_rates.branch_id = user_branch_permissions.branch_id").
		Where("tax_rates.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&taxRate).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("tax rate not found or access denied")
		}
		return nil, err
	}

	// Validate rate if being updated (expecting decimal format: 0.08 for 8%)
	if req.Rate != nil {
		if req.Rate.LessThan(decimal.Zero) || req.Rate.GreaterThan(decimal.NewFromInt(1)) {
			return nil, errors.New("tax rate must be between 0 and 1 (e.g., 0.08 for 8%)")
		}
	}

	// Check if name is being changed and if it conflicts
	if req.Name != nil && *req.Name != taxRate.Name {
		var existingTaxRate models.TaxRate
		if err := s.db.Where("branch_id = ? AND name = ? AND id != ?", taxRate.BranchID, *req.Name, id).First(&existingTaxRate).Error; err == nil {
			return nil, errors.New("tax rate with this name already exists for this branch")
		}
	}

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// If this is being set as default, unset other defaults
	if req.IsDefault != nil && *req.IsDefault {
		if err := tx.Model(&models.TaxRate{}).
			Where("branch_id = ? AND id != ?", taxRate.BranchID, id).
			Update("is_default", false).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Rate != nil {
		updates["rate"] = *req.Rate
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	if req.IsDefault != nil {
		updates["is_default"] = *req.IsDefault
	}

	if len(updates) > 0 {
		if err := tx.Model(&taxRate).Updates(updates).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload tax rate
	if err := s.db.Where("id = ?", id).First(&taxRate).Error; err != nil {
		return nil, err
	}

	response := s.toTaxRateResponse(taxRate)
	return &response, nil
}

// DeleteTaxRate soft deletes a tax rate
func (s *TaxRateService) DeleteTaxRate(id, userID string) error {
	var taxRate models.TaxRate

	// Get tax rate and check access through branch
	if err := s.db.Joins("JOIN user_branch_permissions ON tax_rates.branch_id = user_branch_permissions.branch_id").
		Where("tax_rates.id = ? AND user_branch_permissions.user_id = ?", id, userID).
		First(&taxRate).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("tax rate not found or access denied")
		}
		return err
	}

	// Check if tax rate is being used in invoices or bills
	var invoiceItemCount int64
	if err := s.db.Model(&models.InvoiceItem{}).Where("tax_rate_id = ?", id).Count(&invoiceItemCount).Error; err != nil {
		return err
	}
	if invoiceItemCount > 0 {
		return errors.New("cannot delete tax rate that is being used in invoices")
	}

	var billItemCount int64
	if err := s.db.Model(&models.BillItem{}).Where("tax_rate_id = ?", id).Count(&billItemCount).Error; err != nil {
		return err
	}
	if billItemCount > 0 {
		return errors.New("cannot delete tax rate that is being used in bills")
	}

	// Soft delete by setting is_active to false
	return s.db.Model(&taxRate).Update("is_active", false).Error
}

// GetDefaultTaxRate returns the default tax rate for a branch
func (s *TaxRateService) GetDefaultTaxRate(branchID, userID string) (*TaxRateResponse, error) {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, branchID) {
		return nil, errors.New("access denied to this branch")
	}

	var taxRate models.TaxRate
	if err := s.db.Where("branch_id = ? AND is_default = ? AND is_active = ?", branchID, true, true).First(&taxRate).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("no default tax rate found for this branch")
		}
		return nil, err
	}

	response := s.toTaxRateResponse(taxRate)
	return &response, nil
}

// CalculateTax calculates tax amount based on tax rate and base amount
func (s *TaxRateService) CalculateTax(taxRateID string, baseAmount decimal.Decimal, userID string) (decimal.Decimal, error) {
	taxRate, err := s.GetTaxRateByID(taxRateID, userID)
	if err != nil {
		return decimal.Zero, err
	}

	if !taxRate.IsActive {
		return decimal.Zero, errors.New("tax rate is not active")
	}

	// Calculate tax: (base amount * tax rate) / 100
	taxAmount := baseAmount.Mul(taxRate.Rate).Div(decimal.NewFromInt(100))
	return taxAmount, nil
}

// CreateDefaultTaxRates creates default tax rates for a new branch
func (s *TaxRateService) CreateDefaultTaxRates(branchID, userID string) error {
	// Check if user has access to this branch
	if !s.hasBranchAccess(userID, branchID) {
		return errors.New("access denied to this branch")
	}

	// Check if tax rates already exist
	var count int64
	if err := s.db.Model(&models.TaxRate{}).Where("branch_id = ?", branchID).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("tax rates already exist for this branch")
	}

	// Default tax rates
	defaultTaxRates := []CreateTaxRateRequest{
		{
			MerchantID: branchID,
			Name:       "No Tax",
			Rate:       decimal.Zero,
			IsActive:   true,
			IsDefault:  true,
		},
		{
			MerchantID: branchID,
			Name:       "Sales Tax",
			Rate:       decimal.NewFromFloat(8.25),
			IsActive:   true,
			IsDefault:  false,
		},
		{
			MerchantID: branchID,
			Name:       "VAT",
			Rate:       decimal.NewFromFloat(20.0),
			IsActive:   true,
			IsDefault:  false,
		},
	}

	// Create tax rates in transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, taxRateReq := range defaultTaxRates {
		taxRate := models.TaxRate{
			ID:        uuid.New().String(),
			BranchID:  taxRateReq.MerchantID, // TODO: Update request struct to use BranchID
			Name:      taxRateReq.Name,
			Rate:      taxRateReq.Rate,
			IsActive:  taxRateReq.IsActive,
			IsDefault: taxRateReq.IsDefault,
		}

		if err := tx.Create(&taxRate).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// Helper methods
func (s *TaxRateService) hasBranchAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserBranchPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

// toTaxRateResponse converts a TaxRate model to TaxRateResponse
func (s *TaxRateService) toTaxRateResponse(taxRate models.TaxRate) TaxRateResponse {
	return TaxRateResponse{
		ID:          taxRate.ID,
		MerchantID:  taxRate.BranchID, // TODO: Update response struct to use BranchID
		Name:        taxRate.Name,
		Rate:        taxRate.Rate,
		Description: taxRate.Description,
		IsActive:    taxRate.IsActive,
		IsDefault:   taxRate.IsDefault,
		CreatedAt:   taxRate.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:   taxRate.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}
}
