package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// TaxReportService handles tax report-related business logic
type TaxReportService struct {
	db *gorm.DB
}

// NewTaxReportService creates a new TaxReportService
func NewTaxReportService(db *gorm.DB) *TaxReportService {
	return &TaxReportService{db: db}
}

// TaxRateBreakdown represents tax breakdown by rate
type TaxRateBreakdown struct {
	TaxRateID     string          `json:"taxRateId"`
	TaxRateName   string          `json:"taxRateName"`
	Rate          decimal.Decimal `json:"rate"`
	TaxableAmount decimal.Decimal `json:"taxableAmount"`
	TaxAmount     decimal.Decimal `json:"taxAmount"`
}

// TaxReportData represents the structured data stored in the tax report
type TaxReportData struct {
	TotalTaxableAmount decimal.Decimal        `json:"totalTaxableAmount"`
	TotalTaxAmount     decimal.Decimal        `json:"totalTaxAmount"`
	TaxRateBreakdowns  []TaxRateBreakdown     `json:"taxRateBreakdowns"`
	GeneratedAt        time.Time              `json:"generatedAt"`
	Summary            map[string]interface{} `json:"summary,omitempty"`
}

// GetAllTaxReports retrieves all tax reports with pagination
func (s *TaxReportService) GetAllTaxReports(page, limit int, search string) ([]models.TaxReport, int64, error) {
	var reports []models.TaxReport
	var total int64

	query := s.db.Model(&models.TaxReport{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN merchants ON tax_reports.branch_id = merchants.id").
			Where("merchants.name ILIKE ?", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count tax reports: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&reports).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch tax reports: %w", err)
	}

	return reports, total, nil
}

// GetTaxReportByID retrieves a tax report by ID
func (s *TaxReportService) GetTaxReportByID(id string) (*models.TaxReport, error) {
	var report models.TaxReport

	if err := s.db.Preload("Merchant").
		First(&report, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tax report not found")
		}
		return nil, fmt.Errorf("failed to fetch tax report: %w", err)
	}

	return &report, nil
}

// GetTaxReportsByMerchant retrieves tax reports for a specific merchant
func (s *TaxReportService) GetTaxReportsByMerchant(branchID string, page, limit int, reportType string, startDate, endDate *time.Time) ([]models.TaxReport, int64, error) {
	var reports []models.TaxReport
	var total int64

	query := s.db.Model(&models.TaxReport{}).Where("branch_id = ?", branchID)

	// Apply report type filter if provided
	if reportType != "" {
		query = query.Where("type = ?", reportType)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("start_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("end_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count tax reports: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").
		Offset(offset).Limit(limit).
		Order("created_at DESC").
		Find(&reports).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch tax reports: %w", err)
	}

	return reports, total, nil
}

// GenerateSalesTaxReport generates a sales tax report for a given period
func (s *TaxReportService) GenerateSalesTaxReport(branchID string, startDate, endDate time.Time) (*models.TaxReport, error) {
	// Validate merchant exists
	var merchant models.Merchant
	if err := s.db.First(&merchant, "id = ?", branchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("merchant not found")
		}
		return nil, fmt.Errorf("failed to validate merchant: %w", err)
	}

	// Get all invoices in the period
	var invoices []models.Invoice
	if err := s.db.Preload("Items").Preload("Items.TaxRate").
		Where("branch_id = ? AND invoice_date >= ? AND invoice_date <= ? AND status != ?",
			branchID, startDate, endDate, "Draft").
		Find(&invoices).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch invoices: %w", err)
	}

	// Calculate tax breakdown
	taxBreakdowns := make(map[string]*TaxRateBreakdown)
	totalTaxableAmount := decimal.Zero
	totalTaxAmount := decimal.Zero

	for _, invoice := range invoices {
		for _, item := range invoice.Items {
			if item.TaxRate != nil {
				taxRateID := item.TaxRate.ID

				if _, exists := taxBreakdowns[taxRateID]; !exists {
					taxBreakdowns[taxRateID] = &TaxRateBreakdown{
						TaxRateID:     item.TaxRate.ID,
						TaxRateName:   item.TaxRate.Name,
						Rate:          item.TaxRate.Rate,
						TaxableAmount: decimal.Zero,
						TaxAmount:     decimal.Zero,
					}
				}

				itemTotal := item.Quantity.Mul(item.UnitPrice)
				taxBreakdowns[taxRateID].TaxableAmount = taxBreakdowns[taxRateID].TaxableAmount.Add(itemTotal)
				taxBreakdowns[taxRateID].TaxAmount = taxBreakdowns[taxRateID].TaxAmount.Add(item.TaxAmount)

				totalTaxableAmount = totalTaxableAmount.Add(itemTotal)
				totalTaxAmount = totalTaxAmount.Add(item.TaxAmount)
			}
		}
	}

	// Convert map to slice
	breakdownSlice := make([]TaxRateBreakdown, 0, len(taxBreakdowns))
	for _, breakdown := range taxBreakdowns {
		breakdownSlice = append(breakdownSlice, *breakdown)
	}

	// Create report data
	reportData := TaxReportData{
		TotalTaxableAmount: totalTaxableAmount,
		TotalTaxAmount:     totalTaxAmount,
		TaxRateBreakdowns:  breakdownSlice,
		GeneratedAt:        time.Now(),
		Summary: map[string]interface{}{
			"invoiceCount": len(invoices),
			"reportType":   "Sales Tax Report",
		},
	}

	// Marshal to JSON
	dataJSON, err := json.Marshal(reportData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal report data: %w", err)
	}

	// Create tax report
	taxReport := &models.TaxReport{
		BranchID:  branchID, // TODO: Update parameter to use branchID
		Type:      models.TaxReportTypeSales,
		StartDate: startDate,
		EndDate:   endDate,
		Data:      string(dataJSON),
	}

	if err := s.db.Create(taxReport).Error; err != nil {
		return nil, fmt.Errorf("failed to create tax report: %w", err)
	}

	// Fetch with relationships
	if err := s.db.Preload("Merchant").First(taxReport, "id = ?", taxReport.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch created tax report: %w", err)
	}

	return taxReport, nil
}

// GenerateIncomeTaxReport generates an income tax report for a given period
func (s *TaxReportService) GenerateIncomeTaxReport(branchID string, startDate, endDate time.Time) (*models.TaxReport, error) {
	// Validate merchant exists
	var merchant models.Merchant
	if err := s.db.First(&merchant, "id = ?", branchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("merchant not found")
		}
		return nil, fmt.Errorf("failed to validate merchant: %w", err)
	}

	// Get revenue (from invoices)
	var totalRevenue decimal.Decimal
	if err := s.db.Model(&models.Invoice{}).
		Select("COALESCE(SUM(total_amount), 0)").
		Where("branch_id = ? AND invoice_date >= ? AND invoice_date <= ? AND status != ?",
			branchID, startDate, endDate, "Draft").
		Scan(&totalRevenue).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate revenue: %w", err)
	}

	// Get expenses
	var totalExpenses decimal.Decimal
	if err := s.db.Model(&models.Expense{}).
		Select("COALESCE(SUM(amount), 0)").
		Where("branch_id = ? AND expense_date >= ? AND expense_date <= ?",
			branchID, startDate, endDate).
		Scan(&totalExpenses).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate expenses: %w", err)
	}

	// Calculate net income
	netIncome := totalRevenue.Sub(totalExpenses)

	// For simplicity, assume a flat tax rate (this would be configurable in a real system)
	taxRate := decimal.NewFromFloat(0.21) // 21% corporate tax rate
	estimatedTax := netIncome.Mul(taxRate)
	if estimatedTax.IsNegative() {
		estimatedTax = decimal.Zero
	}

	// Create report data
	reportData := TaxReportData{
		TotalTaxableAmount: netIncome,
		TotalTaxAmount:     estimatedTax,
		TaxRateBreakdowns: []TaxRateBreakdown{
			{
				TaxRateID:     "income-tax",
				TaxRateName:   "Corporate Income Tax",
				Rate:          taxRate,
				TaxableAmount: netIncome,
				TaxAmount:     estimatedTax,
			},
		},
		GeneratedAt: time.Now(),
		Summary: map[string]interface{}{
			"totalRevenue":  totalRevenue,
			"totalExpenses": totalExpenses,
			"netIncome":     netIncome,
			"reportType":    "Income Tax Report",
		},
	}

	// Marshal to JSON
	dataJSON, err := json.Marshal(reportData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal report data: %w", err)
	}

	// Create tax report
	taxReport := &models.TaxReport{
		BranchID:  branchID, // TODO: Update parameter to use branchID
		Type:      models.TaxReportTypeIncome,
		StartDate: startDate,
		EndDate:   endDate,
		Data:      string(dataJSON),
	}

	if err := s.db.Create(taxReport).Error; err != nil {
		return nil, fmt.Errorf("failed to create tax report: %w", err)
	}

	// Fetch with relationships
	if err := s.db.Preload("Merchant").First(taxReport, "id = ?", taxReport.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch created tax report: %w", err)
	}

	return taxReport, nil
}

// DeleteTaxReport deletes a tax report
func (s *TaxReportService) DeleteTaxReport(id string) error {
	var report models.TaxReport

	// Check if tax report exists
	if err := s.db.First(&report, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tax report not found")
		}
		return fmt.Errorf("failed to fetch tax report: %w", err)
	}

	// Delete the tax report
	if err := s.db.Delete(&report).Error; err != nil {
		return fmt.Errorf("failed to delete tax report: %w", err)
	}

	return nil
}

// ParseTaxReportData parses the JSON data from a tax report
func (s *TaxReportService) ParseTaxReportData(report *models.TaxReport) (*TaxReportData, error) {
	var data TaxReportData
	if err := json.Unmarshal([]byte(report.Data), &data); err != nil {
		return nil, fmt.Errorf("failed to parse tax report data: %w", err)
	}
	return &data, nil
}

// GetTaxReportSummary gets summary statistics for tax reports
func (s *TaxReportService) GetTaxReportSummary(branchID string, reportType string, startDate, endDate *time.Time) (*TaxReportSummary, error) {
	query := s.db.Model(&models.TaxReport{}).Where("branch_id = ?", branchID)

	if reportType != "" {
		query = query.Where("type = ?", reportType)
	}
	if startDate != nil {
		query = query.Where("start_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("end_date <= ?", *endDate)
	}

	var summary TaxReportSummary

	// Get total count
	if err := query.Count(&summary.TotalReports).Error; err != nil {
		return nil, fmt.Errorf("failed to count tax reports: %w", err)
	}

	// Get type counts
	typeCounts := []struct {
		Type  string
		Count int64
	}{}

	if err := query.Select("type, COUNT(*) as count").Group("type").Scan(&typeCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get type counts: %w", err)
	}

	// Map type counts
	for _, tc := range typeCounts {
		switch tc.Type {
		case string(models.TaxReportTypeSales):
			summary.SalesReports = tc.Count
		case string(models.TaxReportTypeIncome):
			summary.IncomeReports = tc.Count
		case string(models.TaxReportTypePayroll):
			summary.PayrollReports = tc.Count
		}
	}

	return &summary, nil
}

// TaxReportSummary represents summary statistics for tax reports
type TaxReportSummary struct {
	TotalReports   int64 `json:"totalReports"`
	SalesReports   int64 `json:"salesReports"`
	IncomeReports  int64 `json:"incomeReports"`
	PayrollReports int64 `json:"payrollReports"`
}

// ValidateTaxReport validates tax report data
func (s *TaxReportService) ValidateTaxReport(report *models.TaxReport) error {
	if report.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if report.Type == "" {
		return fmt.Errorf("report type is required")
	}
	if report.StartDate.IsZero() {
		return fmt.Errorf("start date is required")
	}
	if report.EndDate.IsZero() {
		return fmt.Errorf("end date is required")
	}
	if report.EndDate.Before(report.StartDate) {
		return fmt.Errorf("end date must be after start date")
	}
	if report.Data == "" {
		return fmt.Errorf("report data is required")
	}

	// Validate report type
	validTypes := []models.TaxReportType{
		models.TaxReportTypeSales,
		models.TaxReportTypeIncome,
		models.TaxReportTypePayroll,
	}
	isValidType := false
	for _, validType := range validTypes {
		if report.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid report type: %s", report.Type)
	}

	// Validate JSON data
	var data TaxReportData
	if err := json.Unmarshal([]byte(report.Data), &data); err != nil {
		return fmt.Errorf("invalid report data JSON: %w", err)
	}

	return nil
}

// GetValidReportTypes returns all valid tax report types
func (s *TaxReportService) GetValidReportTypes() []string {
	return []string{
		string(models.TaxReportTypeSales),
		string(models.TaxReportTypeIncome),
		string(models.TaxReportTypePayroll),
	}
}

// GenerateTaxReport generates a tax report based on type
func (s *TaxReportService) GenerateTaxReport(branchID string, reportType models.TaxReportType, startDate, endDate time.Time) (*models.TaxReport, error) {
	switch reportType {
	case models.TaxReportTypeSales:
		return s.GenerateSalesTaxReport(branchID, startDate, endDate)
	case models.TaxReportTypeIncome:
		return s.GenerateIncomeTaxReport(branchID, startDate, endDate)
	case models.TaxReportTypePayroll:
		return s.GeneratePayrollTaxReport(branchID, startDate, endDate)
	default:
		return nil, fmt.Errorf("unsupported report type: %s", reportType)
	}
}

// GeneratePayrollTaxReport generates a payroll tax report for a given period
func (s *TaxReportService) GeneratePayrollTaxReport(branchID string, startDate, endDate time.Time) (*models.TaxReport, error) {
	// Validate merchant exists
	var merchant models.Merchant
	if err := s.db.First(&merchant, "id = ?", branchID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("merchant not found")
		}
		return nil, fmt.Errorf("failed to validate merchant: %w", err)
	}

	// Get payroll runs in the period
	var payrollRuns []models.PayrollRun
	if err := s.db.Preload("Details").
		Where("branch_id = ? AND pay_period_start >= ? AND pay_period_end <= ?",
			branchID, startDate, endDate).
		Find(&payrollRuns).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch payroll runs: %w", err)
	}

	// Calculate payroll tax totals
	totalGrossWages := decimal.Zero
	totalEmployeeTaxes := decimal.Zero
	totalEmployerTaxes := decimal.Zero

	for _, run := range payrollRuns {
		for _, detail := range run.Details {
			totalGrossWages = totalGrossWages.Add(detail.RegularPay).Add(detail.OvertimePay).Add(detail.BonusPay).Add(detail.CommissionPay)
			// Note: In a real system, you'd calculate actual tax amounts based on tax tables
			// For now, we'll use simplified calculations
			grossPay := detail.RegularPay.Add(detail.OvertimePay).Add(detail.BonusPay).Add(detail.CommissionPay)
			employeeTax := grossPay.Mul(decimal.NewFromFloat(0.15))   // Simplified employee tax
			employerTax := grossPay.Mul(decimal.NewFromFloat(0.0765)) // Simplified employer tax (FICA)

			totalEmployeeTaxes = totalEmployeeTaxes.Add(employeeTax)
			totalEmployerTaxes = totalEmployerTaxes.Add(employerTax)
		}
	}

	totalTaxAmount := totalEmployeeTaxes.Add(totalEmployerTaxes)

	// Create report data
	reportData := TaxReportData{
		TotalTaxableAmount: totalGrossWages,
		TotalTaxAmount:     totalTaxAmount,
		TaxRateBreakdowns: []TaxRateBreakdown{
			{
				TaxRateID:     "employee-tax",
				TaxRateName:   "Employee Taxes",
				Rate:          decimal.NewFromFloat(0.15),
				TaxableAmount: totalGrossWages,
				TaxAmount:     totalEmployeeTaxes,
			},
			{
				TaxRateID:     "employer-tax",
				TaxRateName:   "Employer Taxes",
				Rate:          decimal.NewFromFloat(0.0765),
				TaxableAmount: totalGrossWages,
				TaxAmount:     totalEmployerTaxes,
			},
		},
		GeneratedAt: time.Now(),
		Summary: map[string]interface{}{
			"payrollRunCount":    len(payrollRuns),
			"totalGrossWages":    totalGrossWages,
			"totalEmployeeTaxes": totalEmployeeTaxes,
			"totalEmployerTaxes": totalEmployerTaxes,
			"reportType":         "Payroll Tax Report",
		},
	}

	// Marshal to JSON
	dataJSON, err := json.Marshal(reportData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal report data: %w", err)
	}

	// Create tax report
	taxReport := &models.TaxReport{
		BranchID:  branchID, // TODO: Update parameter to use branchID
		Type:      models.TaxReportTypePayroll,
		StartDate: startDate,
		EndDate:   endDate,
		Data:      string(dataJSON),
	}

	if err := s.db.Create(taxReport).Error; err != nil {
		return nil, fmt.Errorf("failed to create tax report: %w", err)
	}

	// Fetch with relationships
	if err := s.db.Preload("Merchant").First(taxReport, "id = ?", taxReport.ID).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch created tax report: %w", err)
	}

	return taxReport, nil
}
