package services

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CollectionCaseService handles collection case business logic
type CollectionCaseService struct {
	db *gorm.DB
}

// NewCollectionCaseService creates a new CollectionCaseService
func NewCollectionCaseService(db *gorm.DB) *CollectionCaseService {
	return &CollectionCaseService{db: db}
}

// GetAllCollectionCases retrieves all collection cases with pagination
func (s *CollectionCaseService) GetAllCollectionCases(page, limit int, search string) ([]models.CollectionCase, int64, error) {
	var cases []models.CollectionCase
	var total int64

	query := s.db.Model(&models.CollectionCase{})

	// Apply search filter
	if search != "" {
		query = query.Where("case_number ILIKE ? OR notes ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection cases: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("AssignedUser").Preload("Activities").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&cases).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection cases: %w", err)
	}

	return cases, total, nil
}

// GetCollectionCaseByID retrieves a collection case by ID
func (s *CollectionCaseService) GetCollectionCaseByID(id string) (*models.CollectionCase, error) {
	var collectionCase models.CollectionCase
	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("AssignedUser").Preload("Activities").
		Where("id = ?", id).First(&collectionCase).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection case not found")
		}
		return nil, fmt.Errorf("failed to fetch collection case: %w", err)
	}
	return &collectionCase, nil
}

// GetCollectionCasesByMerchant retrieves collection cases for a specific merchant
func (s *CollectionCaseService) GetCollectionCasesByMerchant(branchID string, page, limit int, search string, status *string, priority *string) ([]models.CollectionCase, int64, error) {
	var cases []models.CollectionCase
	var total int64

	query := s.db.Model(&models.CollectionCase{}).Where("branch_id = ?", branchID)

	// Apply search filter
	if search != "" {
		query = query.Where("case_number ILIKE ? OR notes ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter
	if status != nil && *status != "" {
		query = query.Where("status = ?", *status)
	}

	// Apply priority filter
	if priority != nil && *priority != "" {
		query = query.Where("priority = ?", *priority)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection cases: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("AssignedUser").Preload("Activities").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&cases).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection cases: %w", err)
	}

	return cases, total, nil
}

// GetCollectionCasesByCustomer retrieves collection cases for a specific customer
func (s *CollectionCaseService) GetCollectionCasesByCustomer(customerID string, page, limit int) ([]models.CollectionCase, int64, error) {
	var cases []models.CollectionCase
	var total int64

	query := s.db.Model(&models.CollectionCase{}).Where("customer_id = ?", customerID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection cases: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Invoice").
		Preload("AssignedUser").Preload("Activities").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&cases).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection cases: %w", err)
	}

	return cases, total, nil
}

// CreateCollectionCase creates a new collection case
func (s *CollectionCaseService) CreateCollectionCase(collectionCase *models.CollectionCase) error {
	// Validate the collection case
	if err := s.ValidateCollectionCase(collectionCase); err != nil {
		return err
	}

	// Generate case number if not provided
	if collectionCase.CaseNumber == "" {
		caseNumber, err := s.generateCaseNumber(collectionCase.BranchID)
		if err != nil {
			return fmt.Errorf("failed to generate case number: %w", err)
		}
		collectionCase.CaseNumber = caseNumber
	}

	// Set default values
	if collectionCase.Status == "" {
		collectionCase.Status = models.CollectionCaseStatusNew
	}
	if collectionCase.Priority == "" {
		collectionCase.Priority = "Medium"
	}

	// Calculate balance amount
	collectionCase.BalanceAmount = collectionCase.TotalAmount.Sub(collectionCase.CollectedAmount)

	// Create the collection case
	if err := s.db.Create(collectionCase).Error; err != nil {
		return fmt.Errorf("failed to create collection case: %w", err)
	}

	return nil
}

// UpdateCollectionCase updates an existing collection case
func (s *CollectionCaseService) UpdateCollectionCase(id string, updates *models.CollectionCase) (*models.CollectionCase, error) {
	// Check if collection case exists
	var existingCase models.CollectionCase
	if err := s.db.Where("id = ?", id).First(&existingCase).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection case not found")
		}
		return nil, fmt.Errorf("failed to fetch collection case: %w", err)
	}

	// Validate updates
	if err := s.ValidateCollectionCaseUpdates(updates); err != nil {
		return nil, err
	}

	// Recalculate balance amount if amounts changed
	if !updates.TotalAmount.IsZero() || !updates.CollectedAmount.IsZero() {
		totalAmount := updates.TotalAmount
		if totalAmount.IsZero() {
			totalAmount = existingCase.TotalAmount
		}
		collectedAmount := updates.CollectedAmount
		if collectedAmount.IsZero() {
			collectedAmount = existingCase.CollectedAmount
		}
		updates.BalanceAmount = totalAmount.Sub(collectedAmount)
	}

	// Update the collection case
	if err := s.db.Model(&existingCase).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update collection case: %w", err)
	}

	// Fetch and return updated collection case
	return s.GetCollectionCaseByID(id)
}

// DeleteCollectionCase deletes a collection case
func (s *CollectionCaseService) DeleteCollectionCase(id string) error {
	// Check if collection case exists
	var collectionCase models.CollectionCase
	if err := s.db.Where("id = ?", id).First(&collectionCase).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("collection case not found")
		}
		return fmt.Errorf("failed to fetch collection case: %w", err)
	}

	// Delete related activities first
	if err := s.db.Where("collection_case_id = ?", id).Delete(&models.CollectionActivity{}).Error; err != nil {
		return fmt.Errorf("failed to delete collection activities: %w", err)
	}

	// Delete the collection case
	if err := s.db.Delete(&collectionCase).Error; err != nil {
		return fmt.Errorf("failed to delete collection case: %w", err)
	}

	return nil
}

// generateCaseNumber generates a unique case number for a branch
func (s *CollectionCaseService) generateCaseNumber(branchID string) (string, error) {
	// Get the current year
	year := time.Now().Year()

	// Count existing cases for this branch in current year
	var count int64
	if err := s.db.Model(&models.CollectionCase{}).
		Where("branch_id = ? AND EXTRACT(YEAR FROM created_at) = ?", branchID, year).
		Count(&count).Error; err != nil {
		return "", err
	}

	// Generate case number: CASE-YYYY-NNNN
	caseNumber := fmt.Sprintf("CASE-%d-%04d", year, count+1)
	return caseNumber, nil
}

// ValidateCollectionCase validates collection case data
func (s *CollectionCaseService) ValidateCollectionCase(collectionCase *models.CollectionCase) error {
	if collectionCase.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if collectionCase.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if collectionCase.TotalAmount.LessThan(decimal.Zero) {
		return fmt.Errorf("total amount cannot be negative")
	}
	if collectionCase.CollectedAmount.LessThan(decimal.Zero) {
		return fmt.Errorf("collected amount cannot be negative")
	}
	if collectionCase.CollectedAmount.GreaterThan(collectionCase.TotalAmount) {
		return fmt.Errorf("collected amount cannot exceed total amount")
	}

	return nil
}

// ValidateCollectionCaseUpdates validates collection case update data
func (s *CollectionCaseService) ValidateCollectionCaseUpdates(updates *models.CollectionCase) error {
	if !updates.TotalAmount.IsZero() && updates.TotalAmount.LessThan(decimal.Zero) {
		return fmt.Errorf("total amount cannot be negative")
	}
	if !updates.CollectedAmount.IsZero() && updates.CollectedAmount.LessThan(decimal.Zero) {
		return fmt.Errorf("collected amount cannot be negative")
	}

	return nil
}
