package services

import (
	"errors"
	"fmt"
	"strings"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// EmailTemplateService handles email template-related business logic
type EmailTemplateService struct {
	db *gorm.DB
}

// NewEmailTemplateService creates a new EmailTemplateService
func NewEmailTemplateService(db *gorm.DB) *EmailTemplateService {
	return &EmailTemplateService{db: db}
}

// GetAllEmailTemplates retrieves all email templates with pagination (user-filtered)
func (s *EmailTemplateService) GetAllEmailTemplates(page, limit int, search string) ([]models.EmailTemplate, int64, error) {
	var emailTemplates []models.EmailTemplate
	var total int64

	query := s.db.Model(&models.EmailTemplate{})

	// Apply search filter if provided
	if search != "" {
		query = query.Where("name ILIKE ? OR type ILIKE ? OR subject ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email templates: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").
		Offset(offset).Limit(limit).
		Order("name ASC, created_at DESC").
		Find(&emailTemplates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email templates: %w", err)
	}

	return emailTemplates, total, nil
}

// GetAllEmailTemplatesByUser retrieves all email templates with pagination filtered by user branch access
func (s *EmailTemplateService) GetAllEmailTemplatesByUser(page, limit int, search string, userID string) ([]models.EmailTemplate, int64, error) {
	var emailTemplates []models.EmailTemplate
	var total int64

	// Build query to get email templates from branches user has access to
	query := s.db.Model(&models.EmailTemplate{}).
		Joins("JOIN user_branch_permissions ON email_templates.branch_id = user_branch_permissions.branch_id").
		Where("user_branch_permissions.user_id = ?", userID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("email_templates.name ILIKE ? OR email_templates.type ILIKE ? OR email_templates.subject ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email templates: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").
		Offset(offset).Limit(limit).
		Order("email_templates.name ASC, email_templates.created_at DESC").
		Find(&emailTemplates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email templates: %w", err)
	}

	return emailTemplates, total, nil
}

// GetEmailTemplateByID retrieves an email template by ID
func (s *EmailTemplateService) GetEmailTemplateByID(id string) (*models.EmailTemplate, error) {
	var emailTemplate models.EmailTemplate

	if err := s.db.Preload("Branch").First(&emailTemplate, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email template not found")
		}
		return nil, fmt.Errorf("failed to fetch email template: %w", err)
	}

	return &emailTemplate, nil
}

// GetEmailTemplatesByMerchant retrieves email templates for a specific merchant
func (s *EmailTemplateService) GetEmailTemplatesByMerchant(branchID string, page, limit int, search, templateType string, isActive *bool) ([]models.EmailTemplate, int64, error) {
	var emailTemplates []models.EmailTemplate
	var total int64

	query := s.db.Model(&models.EmailTemplate{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Where("name ILIKE ? OR type ILIKE ? OR subject ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply type filter if provided
	if templateType != "" {
		query = query.Where("type = ?", templateType)
	}

	// Apply active filter if provided
	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email templates: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").
		Offset(offset).Limit(limit).
		Order("name ASC, created_at DESC").
		Find(&emailTemplates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch email templates: %w", err)
	}

	return emailTemplates, total, nil
}

// GetDefaultEmailTemplate retrieves the default email template for a branch and type
func (s *EmailTemplateService) GetDefaultEmailTemplate(branchID, templateType string) (*models.EmailTemplate, error) {
	var emailTemplate models.EmailTemplate

	if err := s.db.Preload("Branch").
		First(&emailTemplate, "branch_id = ? AND type = ? AND is_default = ? AND is_active = ?",
			branchID, templateType, true, true).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("default email template not found for type: %s", templateType)
		}
		return nil, fmt.Errorf("failed to fetch default email template: %w", err)
	}

	return &emailTemplate, nil
}

// CreateEmailTemplate creates a new email template
func (s *EmailTemplateService) CreateEmailTemplate(emailTemplate *models.EmailTemplate) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", emailTemplate.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Check if template name already exists for this branch and type
		var existingTemplate models.EmailTemplate
		if err := tx.First(&existingTemplate, "branch_id = ? AND name = ? AND type = ?",
			emailTemplate.BranchID, emailTemplate.Name, emailTemplate.Type).Error; err == nil {
			return fmt.Errorf("email template with name '%s' and type '%s' already exists for this branch",
				emailTemplate.Name, emailTemplate.Type)
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check template name uniqueness: %w", err)
		}

		// If this template is being set as default, unset any existing default for this type
		if emailTemplate.IsDefault {
			if err := s.unsetDefaultTemplate(tx, emailTemplate.BranchID, emailTemplate.Type); err != nil {
				return fmt.Errorf("failed to unset existing default template: %w", err)
			}
		}

		// Set default values
		if emailTemplate.Type == "" {
			emailTemplate.Type = "general"
		}

		// Create the email template
		if err := tx.Create(emailTemplate).Error; err != nil {
			return fmt.Errorf("failed to create email template: %w", err)
		}

		return nil
	})
}

// UpdateEmailTemplate updates an existing email template
func (s *EmailTemplateService) UpdateEmailTemplate(id string, updates *models.EmailTemplate) (*models.EmailTemplate, error) {
	var emailTemplate models.EmailTemplate

	// Check if email template exists
	if err := s.db.First(&emailTemplate, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email template not found")
		}
		return nil, fmt.Errorf("failed to fetch email template: %w", err)
	}

	var updatedTemplate *models.EmailTemplate
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating name or type, check uniqueness
		if (updates.Name != "" && updates.Name != emailTemplate.Name) ||
			(updates.Type != "" && updates.Type != emailTemplate.Type) {
			newName := updates.Name
			if newName == "" {
				newName = emailTemplate.Name
			}
			newType := updates.Type
			if newType == "" {
				newType = emailTemplate.Type
			}

			var existingTemplate models.EmailTemplate
			if err := tx.First(&existingTemplate, "branch_id = ? AND name = ? AND type = ? AND id != ?",
				emailTemplate.BranchID, newName, newType, id).Error; err == nil {
				return fmt.Errorf("email template with name '%s' and type '%s' already exists for this branch",
					newName, newType)
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to check template name uniqueness: %w", err)
			}
		}

		// If this template is being set as default, unset any existing default for this type
		if updates.IsDefault {
			templateType := updates.Type
			if templateType == "" {
				templateType = emailTemplate.Type
			}
			if err := s.unsetDefaultTemplate(tx, emailTemplate.BranchID, templateType); err != nil {
				return fmt.Errorf("failed to unset existing default template: %w", err)
			}
		}

		// Update the email template
		if err := tx.Model(&emailTemplate).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update email template: %w", err)
		}

		// Fetch updated email template with relationships
		if err := tx.Preload("Branch").First(&emailTemplate, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated email template: %w", err)
		}

		updatedTemplate = &emailTemplate
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedTemplate, nil
}

// DeleteEmailTemplate deletes an email template
func (s *EmailTemplateService) DeleteEmailTemplate(id string) error {
	var emailTemplate models.EmailTemplate

	// Check if email template exists
	if err := s.db.First(&emailTemplate, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("email template not found")
		}
		return fmt.Errorf("failed to fetch email template: %w", err)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Check if template is being used by any email logs
		var emailLogCount int64
		if err := tx.Model(&models.EmailLog{}).Where("template_id = ?", id).Count(&emailLogCount).Error; err != nil {
			return fmt.Errorf("failed to check email log usage: %w", err)
		}

		if emailLogCount > 0 {
			return fmt.Errorf("cannot delete email template: it is being used by %d email log(s)", emailLogCount)
		}

		// Delete the email template
		if err := tx.Delete(&emailTemplate).Error; err != nil {
			return fmt.Errorf("failed to delete email template: %w", err)
		}

		return nil
	})
}

// SetDefaultEmailTemplate sets an email template as the default for its type
func (s *EmailTemplateService) SetDefaultEmailTemplate(id string) (*models.EmailTemplate, error) {
	var emailTemplate models.EmailTemplate

	// Check if email template exists
	if err := s.db.First(&emailTemplate, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email template not found")
		}
		return nil, fmt.Errorf("failed to fetch email template: %w", err)
	}

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Unset any existing default for this branch and type
		if err := s.unsetDefaultTemplate(tx, emailTemplate.BranchID, emailTemplate.Type); err != nil {
			return fmt.Errorf("failed to unset existing default template: %w", err)
		}

		// Set this template as default
		if err := tx.Model(&emailTemplate).Update("is_default", true).Error; err != nil {
			return fmt.Errorf("failed to set template as default: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &emailTemplate, nil
}

// CloneEmailTemplate creates a copy of an existing email template
func (s *EmailTemplateService) CloneEmailTemplate(id, newName string) (*models.EmailTemplate, error) {
	var originalTemplate models.EmailTemplate

	// Get the original template
	if err := s.db.First(&originalTemplate, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("email template not found")
		}
		return nil, fmt.Errorf("failed to fetch email template: %w", err)
	}

	// Create new template with copied data
	newTemplate := &models.EmailTemplate{
		BranchID:  originalTemplate.BranchID,
		Name:      newName,
		Type:      originalTemplate.Type,
		Subject:   originalTemplate.Subject,
		Body:      originalTemplate.Body,
		IsDefault: false, // Cloned templates are never default
		IsActive:  true,
	}

	if err := s.CreateEmailTemplate(newTemplate); err != nil {
		return nil, fmt.Errorf("failed to create cloned template: %w", err)
	}

	// Fetch the created template with relationships
	return s.GetEmailTemplateByID(newTemplate.ID)
}

// GetEmailTemplateTypes returns all unique template types for a branch
func (s *EmailTemplateService) GetEmailTemplateTypes(branchID string) ([]string, error) {
	var types []string

	if err := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ?", branchID).
		Distinct("type").
		Pluck("type", &types).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch template types: %w", err)
	}

	return types, nil
}

// GetEmailTemplateSummary gets summary statistics for email templates
func (s *EmailTemplateService) GetEmailTemplateSummary(branchID string) (*EmailTemplateSummary, error) {
	var summary EmailTemplateSummary

	// Get total count
	if err := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count email templates: %w", err)
	}

	// Get active count
	if err := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Count(&summary.ActiveTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count active email templates: %w", err)
	}

	// Get default count
	if err := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ? AND is_default = ?", branchID, true).
		Count(&summary.DefaultTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count default email templates: %w", err)
	}

	// Get type counts
	typeCounts := []struct {
		Type  string
		Count int64
	}{}

	if err := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ?", branchID).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get type counts: %w", err)
	}

	// Map type counts
	summary.TypeCounts = make(map[string]int64)
	for _, tc := range typeCounts {
		summary.TypeCounts[tc.Type] = tc.Count
	}

	return &summary, nil
}

// ProcessEmailTemplate processes template variables in subject and body
func (s *EmailTemplateService) ProcessEmailTemplate(template *models.EmailTemplate, variables map[string]string) (*ProcessedEmailTemplate, error) {
	if template == nil {
		return nil, fmt.Errorf("template cannot be nil")
	}

	processed := &ProcessedEmailTemplate{
		ID:      template.ID,
		Name:    template.Name,
		Type:    template.Type,
		Subject: template.Subject,
		Body:    template.Body,
	}

	// Process variables in subject and body
	for key, value := range variables {
		placeholder := "{{" + key + "}}"
		processed.Subject = strings.ReplaceAll(processed.Subject, placeholder, value)
		processed.Body = strings.ReplaceAll(processed.Body, placeholder, value)
	}

	return processed, nil
}

// ValidateEmailTemplate validates email template data
func (s *EmailTemplateService) ValidateEmailTemplate(emailTemplate *models.EmailTemplate) error {
	if emailTemplate.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if emailTemplate.Name == "" {
		return fmt.Errorf("template name is required")
	}
	if len(emailTemplate.Name) > 255 {
		return fmt.Errorf("template name cannot exceed 255 characters")
	}
	if emailTemplate.Type == "" {
		return fmt.Errorf("template type is required")
	}
	if len(emailTemplate.Type) > 100 {
		return fmt.Errorf("template type cannot exceed 100 characters")
	}
	if emailTemplate.Subject == "" {
		return fmt.Errorf("template subject is required")
	}
	if len(emailTemplate.Subject) > 255 {
		return fmt.Errorf("template subject cannot exceed 255 characters")
	}
	if emailTemplate.Body == "" {
		return fmt.Errorf("template body is required")
	}

	// Validate template type
	validTypes := []string{
		"invoice", "payment_reminder", "welcome", "password_reset",
		"order_confirmation", "shipping_notification", "general",
		"quote", "receipt", "statement", "overdue_notice",
	}
	isValidType := false
	for _, validType := range validTypes {
		if emailTemplate.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid template type: %s", emailTemplate.Type)
	}

	return nil
}

// GetEmailTemplatesByType retrieves email templates by type for a branch
func (s *EmailTemplateService) GetEmailTemplatesByType(branchID, templateType string, activeOnly bool) ([]models.EmailTemplate, error) {
	var emailTemplates []models.EmailTemplate

	query := s.db.Model(&models.EmailTemplate{}).
		Where("branch_id = ? AND type = ?", branchID, templateType)

	if activeOnly {
		query = query.Where("is_active = ?", true)
	}

	if err := query.Preload("Branch").
		Order("is_default DESC, name ASC").
		Find(&emailTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch email templates by type: %w", err)
	}

	return emailTemplates, nil
}

// EmailTemplateSummary represents summary statistics for email templates
type EmailTemplateSummary struct {
	TotalTemplates   int64            `json:"totalTemplates"`
	ActiveTemplates  int64            `json:"activeTemplates"`
	DefaultTemplates int64            `json:"defaultTemplates"`
	TypeCounts       map[string]int64 `json:"typeCounts"`
}

// ProcessedEmailTemplate represents an email template with processed variables
type ProcessedEmailTemplate struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
}

// unsetDefaultTemplate removes the default flag from existing templates of the same type
func (s *EmailTemplateService) unsetDefaultTemplate(tx *gorm.DB, branchID, templateType string) error {
	return tx.Model(&models.EmailTemplate{}).
		Where("branch_id = ? AND type = ? AND is_default = ?", branchID, templateType, true).
		Update("is_default", false).Error
}
