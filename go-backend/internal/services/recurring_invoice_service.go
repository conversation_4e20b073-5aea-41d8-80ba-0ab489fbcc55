package services

import (
	"errors"
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// RecurringInvoiceService handles recurring invoice-related business logic
type RecurringInvoiceService struct {
	db             *gorm.DB
	invoiceService *InvoiceService
}

// NewRecurringInvoiceService creates a new RecurringInvoiceService
func NewRecurringInvoiceService(db *gorm.DB, invoiceService *InvoiceService) *RecurringInvoiceService {
	return &RecurringInvoiceService{
		db:             db,
		invoiceService: invoiceService,
	}
}

// GetAllRecurringInvoices retrieves all recurring invoices with pagination
func (s *RecurringInvoiceService) GetAllRecurringInvoices(page, limit int, search string) ([]models.RecurringInvoice, int64, error) {
	var recurringInvoices []models.RecurringInvoice
	var total int64

	query := s.db.Model(&models.RecurringInvoice{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON recurring_invoices.customer_id = customers.id").
			Where("recurring_invoices.name ILIKE ? OR customers.name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count recurring invoices: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Template").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("next_invoice_date ASC, created_at DESC").
		Find(&recurringInvoices).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch recurring invoices: %w", err)
	}

	return recurringInvoices, total, nil
}

// GetRecurringInvoiceByID retrieves a recurring invoice by ID
func (s *RecurringInvoiceService) GetRecurringInvoiceByID(id string) (*models.RecurringInvoice, error) {
	var recurringInvoice models.RecurringInvoice

	if err := s.db.Preload("Merchant").Preload("Customer").Preload("Template").
		Preload("Items").Preload("Items.TaxRate").
		First(&recurringInvoice, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("recurring invoice not found")
		}
		return nil, fmt.Errorf("failed to fetch recurring invoice: %w", err)
	}

	return &recurringInvoice, nil
}

// GetRecurringInvoicesByMerchant retrieves recurring invoices for a specific merchant
func (s *RecurringInvoiceService) GetRecurringInvoicesByMerchant(branchID string, page, limit int, search, status, frequency string, startDate, endDate *time.Time) ([]models.RecurringInvoice, int64, error) {
	var recurringInvoices []models.RecurringInvoice
	var total int64

	query := s.db.Model(&models.RecurringInvoice{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN customers ON recurring_invoices.customer_id = customers.id").
			Where("recurring_invoices.name ILIKE ? OR customers.name ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply frequency filter if provided
	if frequency != "" {
		query = query.Where("frequency = ?", frequency)
	}

	// Apply date range filter if provided
	if startDate != nil {
		query = query.Where("start_date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("start_date <= ?", *endDate)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count recurring invoices: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Template").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("next_invoice_date ASC, created_at DESC").
		Find(&recurringInvoices).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch recurring invoices: %w", err)
	}

	return recurringInvoices, total, nil
}

// GetRecurringInvoicesByCustomer retrieves recurring invoices for a specific customer
func (s *RecurringInvoiceService) GetRecurringInvoicesByCustomer(customerID string, page, limit int, status string) ([]models.RecurringInvoice, int64, error) {
	var recurringInvoices []models.RecurringInvoice
	var total int64

	query := s.db.Model(&models.RecurringInvoice{}).Where("customer_id = ?", customerID)

	// Apply status filter if provided
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count recurring invoices: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Customer").Preload("Template").Preload("Items").Preload("Items.TaxRate").
		Offset(offset).Limit(limit).
		Order("next_invoice_date ASC, created_at DESC").
		Find(&recurringInvoices).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch recurring invoices: %w", err)
	}

	return recurringInvoices, total, nil
}

// CreateRecurringInvoice creates a new recurring invoice with items
func (s *RecurringInvoiceService) CreateRecurringInvoice(recurringInvoice *models.RecurringInvoice, items []models.RecurringInvoiceItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", recurringInvoice.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate customer exists and belongs to branch
		var customer models.Customer
		if err := tx.First(&customer, "id = ? AND branch_id = ?", recurringInvoice.CustomerID, recurringInvoice.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("customer not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate customer: %w", err)
		}

		// Validate template exists and belongs to branch if provided
		if recurringInvoice.TemplateID != nil {
			var template models.InvoiceTemplate
			if err := tx.First(&template, "id = ? AND branch_id = ?", *recurringInvoice.TemplateID, recurringInvoice.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("template not found or does not belong to merchant")
				}
				return fmt.Errorf("failed to validate template: %w", err)
			}
		}

		// Calculate next invoice date if not provided
		if recurringInvoice.NextInvoiceDate.IsZero() {
			nextDate, err := s.calculateNextInvoiceDate(recurringInvoice.StartDate, recurringInvoice.Frequency, recurringInvoice.CustomInterval)
			if err != nil {
				return fmt.Errorf("failed to calculate next invoice date: %w", err)
			}
			recurringInvoice.NextInvoiceDate = nextDate
		}

		// Set default status if not provided
		if recurringInvoice.Status == "" {
			recurringInvoice.Status = models.RecurringStatusActive
		}

		// Create the recurring invoice
		if err := tx.Create(recurringInvoice).Error; err != nil {
			return fmt.Errorf("failed to create recurring invoice: %w", err)
		}

		// Create recurring invoice items
		for i := range items {
			items[i].RecurringInvoiceID = recurringInvoice.ID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
		}

		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create recurring invoice items: %w", err)
			}
		}

		return nil
	})
}

// UpdateRecurringInvoice updates an existing recurring invoice
func (s *RecurringInvoiceService) UpdateRecurringInvoice(id string, updates *models.RecurringInvoice) (*models.RecurringInvoice, error) {
	var recurringInvoice models.RecurringInvoice

	// Check if recurring invoice exists
	if err := s.db.First(&recurringInvoice, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("recurring invoice not found")
		}
		return nil, fmt.Errorf("failed to fetch recurring invoice: %w", err)
	}

	// Prevent updates to completed recurring invoices
	if recurringInvoice.Status == models.RecurringStatusCompleted {
		return nil, fmt.Errorf("cannot update completed recurring invoice")
	}

	var updatedRecurringInvoice *models.RecurringInvoice
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating customer ID, validate the new customer
		if updates.CustomerID != "" && updates.CustomerID != recurringInvoice.CustomerID {
			var customer models.Customer
			if err := tx.First(&customer, "id = ? AND branch_id = ?", updates.CustomerID, recurringInvoice.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("customer not found or does not belong to branch")
				}
				return fmt.Errorf("failed to validate customer: %w", err)
			}
		}

		// If updating template ID, validate the new template
		if updates.TemplateID != nil && (recurringInvoice.TemplateID == nil || *updates.TemplateID != *recurringInvoice.TemplateID) {
			var template models.InvoiceTemplate
			if err := tx.First(&template, "id = ? AND branch_id = ?", *updates.TemplateID, recurringInvoice.BranchID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return fmt.Errorf("template not found or does not belong to merchant")
				}
				return fmt.Errorf("failed to validate template: %w", err)
			}
		}

		// If frequency or start date is being updated, recalculate next invoice date
		if (updates.Frequency != "" && updates.Frequency != recurringInvoice.Frequency) ||
			(!updates.StartDate.IsZero() && !updates.StartDate.Equal(recurringInvoice.StartDate)) ||
			(updates.CustomInterval != nil && (recurringInvoice.CustomInterval == nil || *updates.CustomInterval != *recurringInvoice.CustomInterval)) {

			startDate := recurringInvoice.StartDate
			if !updates.StartDate.IsZero() {
				startDate = updates.StartDate
			}

			frequency := recurringInvoice.Frequency
			if updates.Frequency != "" {
				frequency = updates.Frequency
			}

			customInterval := recurringInvoice.CustomInterval
			if updates.CustomInterval != nil {
				customInterval = updates.CustomInterval
			}

			nextDate, err := s.calculateNextInvoiceDate(startDate, frequency, customInterval)
			if err != nil {
				return fmt.Errorf("failed to calculate next invoice date: %w", err)
			}
			updates.NextInvoiceDate = nextDate
		}

		// Update the recurring invoice
		if err := tx.Model(&recurringInvoice).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update recurring invoice: %w", err)
		}

		// Fetch updated recurring invoice with relationships
		if err := tx.Preload("Merchant").Preload("Customer").Preload("Template").
			Preload("Items").Preload("Items.TaxRate").
			First(&recurringInvoice, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated recurring invoice: %w", err)
		}

		updatedRecurringInvoice = &recurringInvoice
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedRecurringInvoice, nil
}

// UpdateRecurringInvoiceStatus updates the status of a recurring invoice
func (s *RecurringInvoiceService) UpdateRecurringInvoiceStatus(id string, status models.RecurringStatus) (*models.RecurringInvoice, error) {
	var recurringInvoice models.RecurringInvoice

	// Check if recurring invoice exists
	if err := s.db.First(&recurringInvoice, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("recurring invoice not found")
		}
		return nil, fmt.Errorf("failed to fetch recurring invoice: %w", err)
	}

	// Validate status transition
	if !s.isValidStatusTransition(recurringInvoice.Status, status) {
		return nil, fmt.Errorf("invalid status transition from %s to %s", recurringInvoice.Status, status)
	}

	// Update status
	recurringInvoice.Status = status
	if err := s.db.Save(&recurringInvoice).Error; err != nil {
		return nil, fmt.Errorf("failed to update recurring invoice status: %w", err)
	}

	return &recurringInvoice, nil
}

// DeleteRecurringInvoice deletes a recurring invoice
func (s *RecurringInvoiceService) DeleteRecurringInvoice(id string) error {
	var recurringInvoice models.RecurringInvoice

	// Check if recurring invoice exists
	if err := s.db.First(&recurringInvoice, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("recurring invoice not found")
		}
		return fmt.Errorf("failed to fetch recurring invoice: %w", err)
	}

	// Prevent deletion of active recurring invoices (optional business rule)
	if recurringInvoice.Status == models.RecurringStatusActive {
		return fmt.Errorf("cannot delete active recurring invoice. Please pause it first")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete recurring invoice items first
		if err := tx.Where("recurring_invoice_id = ?", id).Delete(&models.RecurringInvoiceItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete recurring invoice items: %w", err)
		}

		// Delete the recurring invoice
		if err := tx.Delete(&recurringInvoice).Error; err != nil {
			return fmt.Errorf("failed to delete recurring invoice: %w", err)
		}

		return nil
	})
}

// UpdateRecurringInvoiceItems updates the items for a recurring invoice
func (s *RecurringInvoiceService) UpdateRecurringInvoiceItems(recurringInvoiceID string, items []models.RecurringInvoiceItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Verify recurring invoice exists
		var recurringInvoice models.RecurringInvoice
		if err := tx.First(&recurringInvoice, "id = ?", recurringInvoiceID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("recurring invoice not found")
			}
			return fmt.Errorf("failed to validate recurring invoice: %w", err)
		}

		// Delete existing items
		if err := tx.Where("recurring_invoice_id = ?", recurringInvoiceID).Delete(&models.RecurringInvoiceItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete existing recurring invoice items: %w", err)
		}

		// Create new items
		for i := range items {
			items[i].RecurringInvoiceID = recurringInvoiceID
			if items[i].SortOrder == 0 {
				items[i].SortOrder = i + 1
			}
		}

		if len(items) > 0 {
			if err := tx.Create(&items).Error; err != nil {
				return fmt.Errorf("failed to create recurring invoice items: %w", err)
			}
		}

		return nil
	})
}

// GenerateInvoiceFromRecurring generates a new invoice from a recurring invoice
func (s *RecurringInvoiceService) GenerateInvoiceFromRecurring(recurringInvoiceID string) (*models.Invoice, error) {
	// Get the recurring invoice with items
	recurringInvoice, err := s.GetRecurringInvoiceByID(recurringInvoiceID)
	if err != nil {
		return nil, err
	}

	// Validate that it's time to generate an invoice
	if time.Now().Before(recurringInvoice.NextInvoiceDate) {
		return nil, fmt.Errorf("not yet time to generate invoice. Next invoice date: %s", recurringInvoice.NextInvoiceDate.Format("2006-01-02"))
	}

	// Validate recurring invoice status
	if recurringInvoice.Status != models.RecurringStatusActive {
		return nil, fmt.Errorf("recurring invoice must be active to generate invoices")
	}

	// Validate that there are items to invoice
	if len(recurringInvoice.Items) == 0 {
		return nil, fmt.Errorf("no items to invoice")
	}

	var generatedInvoice *models.Invoice
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// Create invoice from recurring invoice
		dueDate := time.Now().AddDate(0, 0, 30) // Default 30 days
		invoice := &models.Invoice{
			BranchID:   recurringInvoice.BranchID,
			CustomerID: recurringInvoice.CustomerID,
			IssueDate:  time.Now(),
			DueDate:    &dueDate,
			Terms:      recurringInvoice.Terms,
			Notes:      recurringInvoice.Notes,
			Status:     models.InvoiceStatusDraft,
		}

		// Generate invoice number
		invoiceNumber := s.invoiceService.generateInvoiceNumber(recurringInvoice.BranchID)
		invoice.InvoiceNumber = invoiceNumber

		// Convert recurring invoice items to invoice items
		invoiceItems := make([]models.InvoiceItem, len(recurringInvoice.Items))
		var subtotal, taxTotal decimal.Decimal

		for i, item := range recurringInvoice.Items {
			itemTotal := item.Quantity.Mul(item.UnitPrice)
			subtotal = subtotal.Add(itemTotal)

			// Calculate tax if tax rate is provided
			var taxAmount decimal.Decimal
			if item.TaxRate != nil {
				taxAmount = itemTotal.Mul(item.TaxRate.Rate).Div(decimal.NewFromInt(100))
				taxTotal = taxTotal.Add(taxAmount)
			}

			invoiceItems[i] = models.InvoiceItem{
				Description: item.Description,
				Quantity:    item.Quantity,
				UnitPrice:   item.UnitPrice,
				TotalPrice:  itemTotal,
				TaxRateID:   item.TaxRateID,
				TaxAmount:   taxAmount,
				SortOrder:   item.SortOrder,
			}
		}

		// Set calculated amounts
		invoice.SubTotal = subtotal
		invoice.TaxAmount = taxTotal
		invoice.TotalAmount = subtotal.Add(taxTotal)
		invoice.BalanceAmount = invoice.TotalAmount

		// Create the invoice directly in the database
		if err := tx.Create(invoice).Error; err != nil {
			return fmt.Errorf("failed to create invoice: %w", err)
		}

		// Create invoice items
		for i := range invoiceItems {
			invoiceItems[i].InvoiceID = invoice.ID
		}

		if err := tx.Create(&invoiceItems).Error; err != nil {
			return fmt.Errorf("failed to create invoice items: %w", err)
		}

		// Update recurring invoice with new next invoice date and last invoice date
		nextDate, err := s.calculateNextInvoiceDate(recurringInvoice.NextInvoiceDate, recurringInvoice.Frequency, recurringInvoice.CustomInterval)
		if err != nil {
			return fmt.Errorf("failed to calculate next invoice date: %w", err)
		}

		now := time.Now()
		updates := map[string]interface{}{
			"next_invoice_date": nextDate,
			"last_invoice_date": &now,
		}

		// Check if we've reached the end date
		if recurringInvoice.EndDate != nil && nextDate.After(*recurringInvoice.EndDate) {
			updates["status"] = models.RecurringStatusCompleted
		}

		if err := tx.Model(&models.RecurringInvoice{}).Where("id = ?", recurringInvoiceID).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update recurring invoice: %w", err)
		}

		generatedInvoice = invoice
		return nil
	})

	if err != nil {
		return nil, err
	}

	return generatedInvoice, nil
}

// GetDueRecurringInvoices retrieves recurring invoices that are due for generation
func (s *RecurringInvoiceService) GetDueRecurringInvoices(branchID string) ([]models.RecurringInvoice, error) {
	var recurringInvoices []models.RecurringInvoice

	query := s.db.Model(&models.RecurringInvoice{}).
		Where("status = ? AND next_invoice_date <= ?", models.RecurringStatusActive, time.Now())

	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	}

	if err := query.Preload("Merchant").Preload("Customer").Preload("Template").
		Preload("Items").Preload("Items.TaxRate").
		Order("next_invoice_date ASC").
		Find(&recurringInvoices).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch due recurring invoices: %w", err)
	}

	return recurringInvoices, nil
}

// ProcessDueRecurringInvoices processes all due recurring invoices for a merchant
func (s *RecurringInvoiceService) ProcessDueRecurringInvoices(branchID string) ([]models.Invoice, []error) {
	dueRecurringInvoices, err := s.GetDueRecurringInvoices(branchID)
	if err != nil {
		return nil, []error{fmt.Errorf("failed to get due recurring invoices: %w", err)}
	}

	var generatedInvoices []models.Invoice
	var errors []error

	for _, recurringInvoice := range dueRecurringInvoices {
		invoice, err := s.GenerateInvoiceFromRecurring(recurringInvoice.ID)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to generate invoice for recurring invoice %s: %w", recurringInvoice.ID, err))
			continue
		}
		generatedInvoices = append(generatedInvoices, *invoice)
	}

	return generatedInvoices, errors
}

// GetRecurringInvoiceSummary gets summary statistics for recurring invoices
func (s *RecurringInvoiceService) GetRecurringInvoiceSummary(branchID string) (*RecurringInvoiceSummary, error) {
	var summary RecurringInvoiceSummary

	// Get total count
	if err := s.db.Model(&models.RecurringInvoice{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalRecurringInvoices).Error; err != nil {
		return nil, fmt.Errorf("failed to count recurring invoices: %w", err)
	}

	// Get status counts
	statusCounts := []struct {
		Status string
		Count  int64
	}{}

	if err := s.db.Model(&models.RecurringInvoice{}).
		Where("branch_id = ?", branchID).
		Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	// Map status counts
	for _, sc := range statusCounts {
		switch sc.Status {
		case string(models.RecurringStatusActive):
			summary.ActiveRecurringInvoices = sc.Count
		case string(models.RecurringStatusPaused):
			summary.PausedRecurringInvoices = sc.Count
		case string(models.RecurringStatusCompleted):
			summary.CompletedRecurringInvoices = sc.Count
		}
	}

	// Get frequency counts
	frequencyCounts := []struct {
		Frequency string
		Count     int64
	}{}

	if err := s.db.Model(&models.RecurringInvoice{}).
		Where("branch_id = ?", branchID).
		Select("frequency, COUNT(*) as count").Group("frequency").Scan(&frequencyCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get frequency counts: %w", err)
	}

	// Map frequency counts
	for _, fc := range frequencyCounts {
		switch fc.Frequency {
		case string(models.RecurringFrequencyWeekly):
			summary.WeeklyRecurringInvoices = fc.Count
		case string(models.RecurringFrequencyMonthly):
			summary.MonthlyRecurringInvoices = fc.Count
		case string(models.RecurringFrequencyQuarterly):
			summary.QuarterlyRecurringInvoices = fc.Count
		case string(models.RecurringFrequencyAnnually):
			summary.AnnuallyRecurringInvoices = fc.Count
		}
	}

	// Get due count
	if err := s.db.Model(&models.RecurringInvoice{}).
		Where("branch_id = ? AND status = ? AND next_invoice_date <= ?", branchID, models.RecurringStatusActive, time.Now()).
		Count(&summary.DueRecurringInvoices).Error; err != nil {
		return nil, fmt.Errorf("failed to count due recurring invoices: %w", err)
	}

	return &summary, nil
}

// RecurringInvoiceSummary represents summary statistics for recurring invoices
type RecurringInvoiceSummary struct {
	TotalRecurringInvoices     int64 `json:"totalRecurringInvoices"`
	ActiveRecurringInvoices    int64 `json:"activeRecurringInvoices"`
	PausedRecurringInvoices    int64 `json:"pausedRecurringInvoices"`
	CompletedRecurringInvoices int64 `json:"completedRecurringInvoices"`
	DueRecurringInvoices       int64 `json:"dueRecurringInvoices"`
	WeeklyRecurringInvoices    int64 `json:"weeklyRecurringInvoices"`
	MonthlyRecurringInvoices   int64 `json:"monthlyRecurringInvoices"`
	QuarterlyRecurringInvoices int64 `json:"quarterlyRecurringInvoices"`
	AnnuallyRecurringInvoices  int64 `json:"annuallyRecurringInvoices"`
}

// calculateNextInvoiceDate calculates the next invoice date based on frequency
func (s *RecurringInvoiceService) calculateNextInvoiceDate(currentDate time.Time, frequency models.RecurringFrequency, customInterval *int) (time.Time, error) {
	switch frequency {
	case models.RecurringFrequencyWeekly:
		return currentDate.AddDate(0, 0, 7), nil
	case models.RecurringFrequencyMonthly:
		return currentDate.AddDate(0, 1, 0), nil
	case models.RecurringFrequencyQuarterly:
		return currentDate.AddDate(0, 3, 0), nil
	case models.RecurringFrequencyBiannually:
		return currentDate.AddDate(0, 6, 0), nil
	case models.RecurringFrequencyAnnually:
		return currentDate.AddDate(1, 0, 0), nil
	case models.RecurringFrequencyCustom:
		if customInterval == nil || *customInterval <= 0 {
			return time.Time{}, fmt.Errorf("custom interval must be provided and greater than 0 for custom frequency")
		}
		// For custom frequency, we assume it's in days
		return currentDate.AddDate(0, 0, *customInterval), nil
	default:
		return time.Time{}, fmt.Errorf("unsupported frequency: %s", frequency)
	}
}

// isValidStatusTransition checks if a status transition is valid
func (s *RecurringInvoiceService) isValidStatusTransition(currentStatus, newStatus models.RecurringStatus) bool {
	validTransitions := map[models.RecurringStatus][]models.RecurringStatus{
		models.RecurringStatusActive:    {models.RecurringStatusPaused, models.RecurringStatusCompleted},
		models.RecurringStatusPaused:    {models.RecurringStatusActive, models.RecurringStatusCompleted},
		models.RecurringStatusCompleted: {}, // No transitions from completed
	}

	allowedStatuses, exists := validTransitions[currentStatus]
	if !exists {
		return false
	}

	for _, status := range allowedStatuses {
		if status == newStatus {
			return true
		}
	}

	return false
}

// ValidateRecurringInvoice validates recurring invoice data
func (s *RecurringInvoiceService) ValidateRecurringInvoice(recurringInvoice *models.RecurringInvoice) error {
	if recurringInvoice.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if recurringInvoice.CustomerID == "" {
		return fmt.Errorf("customer ID is required")
	}
	if recurringInvoice.Name == "" {
		return fmt.Errorf("recurring invoice name is required")
	}
	if recurringInvoice.StartDate.IsZero() {
		return fmt.Errorf("start date is required")
	}
	if recurringInvoice.EndDate != nil && recurringInvoice.EndDate.Before(recurringInvoice.StartDate) {
		return fmt.Errorf("end date cannot be before start date")
	}

	// Validate frequency
	validFrequencies := []models.RecurringFrequency{
		models.RecurringFrequencyWeekly,
		models.RecurringFrequencyMonthly,
		models.RecurringFrequencyQuarterly,
		models.RecurringFrequencyBiannually,
		models.RecurringFrequencyAnnually,
		models.RecurringFrequencyCustom,
	}
	isValidFrequency := false
	for _, freq := range validFrequencies {
		if recurringInvoice.Frequency == freq {
			isValidFrequency = true
			break
		}
	}
	if !isValidFrequency {
		return fmt.Errorf("invalid frequency: %s", recurringInvoice.Frequency)
	}

	// Validate custom interval for custom frequency
	if recurringInvoice.Frequency == models.RecurringFrequencyCustom {
		if recurringInvoice.CustomInterval == nil || *recurringInvoice.CustomInterval <= 0 {
			return fmt.Errorf("custom interval must be provided and greater than 0 for custom frequency")
		}
	}

	// Validate status
	validStatuses := []models.RecurringStatus{
		models.RecurringStatusActive,
		models.RecurringStatusPaused,
		models.RecurringStatusCompleted,
	}
	isValidStatus := false
	for _, status := range validStatuses {
		if recurringInvoice.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		return fmt.Errorf("invalid status: %s", recurringInvoice.Status)
	}

	return nil
}

// ValidateRecurringInvoiceItem validates recurring invoice item data
func (s *RecurringInvoiceService) ValidateRecurringInvoiceItem(item *models.RecurringInvoiceItem) error {
	if item.RecurringInvoiceID == "" {
		return fmt.Errorf("recurring invoice ID is required")
	}
	if item.Description == "" {
		return fmt.Errorf("item description is required")
	}
	if item.Quantity.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("item quantity must be greater than zero")
	}
	if item.UnitPrice.LessThan(decimal.Zero) {
		return fmt.Errorf("item unit price cannot be negative")
	}

	return nil
}
