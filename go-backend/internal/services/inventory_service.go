package services

import (
	"errors"
	"time"

	"adc-account-backend/internal/models"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type InventoryService struct {
	db *gorm.DB
}

func NewInventoryService(db *gorm.DB) *InventoryService {
	return &InventoryService{db: db}
}

// CreateInventoryItemRequest represents a request to create an inventory item
type CreateInventoryItemRequest struct {
	BranchID       string                     `json:"branchId"`
	MerchantID     string                     `json:"merchantId"` // For backward compatibility
	Name           string                     `json:"name" binding:"required"`
	SKU            string                     `json:"sku"`
	Description    *string                    `json:"description"`
	Category       *string                    `json:"category"`
	UnitOfMeasure  string                     `json:"unit_of_measure" binding:"required"`
	UnitCost       decimal.Decimal            `json:"unit_cost" binding:"required"`
	UnitPrice      decimal.Decimal            `json:"unitPrice"`
	ReorderLevel   decimal.Decimal            `json:"reorderLevel"`
	Location       *string                    `json:"location"`
	Supplier       *string                    `json:"supplier"`
	Status         models.InventoryItemStatus `json:"status"`
	Notes          *string                    `json:"notes"`
	ImageURL       *string                    `json:"image_url"`
	AssetAccountID string                     `json:"asset_account_id"`
}

// UpdateInventoryItemRequest represents a request to update an inventory item
type UpdateInventoryItemRequest struct {
	Name          *string                     `json:"name"`
	SKU           *string                     `json:"sku"`
	Description   *string                     `json:"description"`
	Category      *string                     `json:"category"`
	UnitOfMeasure *string                     `json:"unit_of_measure"`
	UnitCost      *decimal.Decimal            `json:"unit_cost"`
	UnitPrice     *decimal.Decimal            `json:"unitPrice"`
	ReorderLevel  *decimal.Decimal            `json:"reorderLevel"`
	Location      *string                     `json:"location"`
	Supplier      *string                     `json:"supplier"`
	Status        *models.InventoryItemStatus `json:"status"`
	Notes         *string                     `json:"notes"`
	ImageURL      *string                     `json:"image_url"`
}

// InventoryItemResponse represents an inventory item response
type InventoryItemResponse struct {
	ID             string                     `json:"id"`
	BranchID       string                     `json:"branchId"`
	MerchantID     string                     `json:"merchantId"` // For backward compatibility
	Name           string                     `json:"name"`
	SKU            *string                    `json:"sku"`
	Description    *string                    `json:"description"`
	Category       *string                    `json:"category"`
	UnitOfMeasure  string                     `json:"unitOfMeasure"`
	UnitCost       decimal.Decimal            `json:"unitCost"`
	UnitPrice      decimal.Decimal            `json:"unitPrice"`
	QuantityOnHand decimal.Decimal            `json:"quantityOnHand"`
	ReorderLevel   decimal.Decimal            `json:"reorderLevel"`
	Location       *string                    `json:"location"`
	Supplier       *string                    `json:"supplier"`
	Status         models.InventoryItemStatus `json:"status"`
	Notes          *string                    `json:"notes"`
	ImageURL       *string                    `json:"imageUrl"`
	CreatedAt      string                     `json:"createdAt"`
	UpdatedAt      string                     `json:"updatedAt"`
	Branch         *models.Branch             `json:"branch,omitempty"`
	Merchant       *models.Merchant           `json:"merchant,omitempty"` // For backward compatibility
}

// CreateInventoryTransactionRequest represents a request to create an inventory transaction
type CreateInventoryTransactionRequest struct {
	InventoryItemID string                          `json:"inventoryItemId" binding:"required"`
	Type            models.InventoryTransactionType `json:"type" binding:"required"`
	Quantity        decimal.Decimal                 `json:"quantity" binding:"required"`
	UnitCost        decimal.Decimal                 `json:"unitCost"`
	Reference       *string                         `json:"reference"`
	Notes           *string                         `json:"notes"`
}

// InventoryTransactionResponse represents an inventory transaction response
type InventoryTransactionResponse struct {
	ID              string                          `json:"id"`
	InventoryItemID string                          `json:"inventoryItemId"`
	Type            models.InventoryTransactionType `json:"type"`
	Quantity        decimal.Decimal                 `json:"quantity"`
	UnitCost        decimal.Decimal                 `json:"unitCost"`
	TotalCost       decimal.Decimal                 `json:"totalCost"`
	Reference       *string                         `json:"reference"`
	Notes           *string                         `json:"notes"`
	Date            string                          `json:"date"`
	CreatedAt       string                          `json:"createdAt"`
	InventoryItem   *InventoryItemResponse          `json:"inventoryItem,omitempty"`
}

// GetAllInventoryItems returns all inventory items with pagination
func (s *InventoryService) GetAllInventoryItems(page, limit int, userID string) ([]InventoryItemResponse, int64, error) {
	var items []models.InventoryItem
	var total int64

	// Build query to get inventory items from organizations user has access to
	query := s.db.Model(&models.InventoryItem{}).
		Joins("JOIN user_organization_permissions ON inventory_items.branch_id = user_organization_permissions.organization_id").
		Where("user_organization_permissions.user_id = ?", userID)

	// Count total items
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get items with pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").
		Order("name ASC").
		Offset(offset).Limit(limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]InventoryItemResponse, 0, len(items))
	for _, item := range items {
		responses = append(responses, s.toInventoryItemResponse(item))
	}

	return responses, total, nil
}

// GetInventoryItemsByMerchant returns inventory items for a specific merchant
func (s *InventoryService) GetInventoryItemsByMerchant(branchID string, page, limit int, userID string) ([]InventoryItemResponse, int64, error) {
	// Check if user has access to this merchant
	if !s.hasMerchantAccess(userID, branchID) {
		return nil, 0, errors.New("access denied to this merchant")
	}

	var items []models.InventoryItem
	var total int64

	// Count total items for this merchant
	if err := s.db.Model(&models.InventoryItem{}).Where("branch_id = ? AND status = ?", branchID, models.InventoryItemStatusActive).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get items with pagination
	offset := (page - 1) * limit
	if err := s.db.Where("branch_id = ? AND status = ?", branchID, models.InventoryItemStatusActive).
		Preload("Merchant").
		Order("name ASC").
		Offset(offset).Limit(limit).Find(&items).Error; err != nil {
		return nil, 0, err
	}

	// Convert to response format
	responses := make([]InventoryItemResponse, 0, len(items))
	for _, item := range items {
		responses = append(responses, s.toInventoryItemResponse(item))
	}

	return responses, total, nil
}

// GetInventoryItemByID returns an inventory item by ID
func (s *InventoryService) GetInventoryItemByID(id, userID string) (*InventoryItemResponse, error) {
	var item models.InventoryItem

	// Get item and check access through organization
	if err := s.db.Joins("JOIN user_organization_permissions ON inventory_items.branch_id = user_organization_permissions.organization_id").
		Where("inventory_items.id = ? AND user_organization_permissions.user_id = ?", id, userID).
		Preload("Branch").
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("inventory item not found or access denied")
		}
		return nil, err
	}

	response := s.toInventoryItemResponse(item)
	return &response, nil
}

// CreateInventoryItem creates a new inventory item
func (s *InventoryService) CreateInventoryItem(req CreateInventoryItemRequest, userID string) (*InventoryItemResponse, error) {
	// Determine which ID to use (prefer BranchID, fallback to MerchantID for backward compatibility)
	branchID := req.BranchID
	if branchID == "" {
		branchID = req.MerchantID
	}

	if branchID == "" {
		return nil, errors.New("branchId or merchantId is required")
	}

	// Check if user has access to this organization/branch
	if !s.hasOrganizationAccess(userID, branchID) {
		return nil, errors.New("access denied to this organization")
	}

	// Check if SKU already exists for this branch (if SKU is provided)
	if req.SKU != "" {
		var existingItem models.InventoryItem
		if err := s.db.Where("branch_id = ? AND sku = ?", branchID, req.SKU).First(&existingItem).Error; err == nil {
			return nil, errors.New("SKU already exists for this branch")
		}
	}

	// Validate prices
	if req.UnitCost.LessThan(decimal.Zero) {
		return nil, errors.New("unit cost cannot be negative")
	}
	if !req.UnitPrice.IsZero() && req.UnitPrice.LessThan(decimal.Zero) {
		return nil, errors.New("unit price cannot be negative")
	}

	// Set default status if not provided
	status := req.Status
	if status == "" {
		status = models.InventoryItemStatusActive
	}

	// Set SKU pointer
	var skuPtr *string
	if req.SKU != "" {
		skuPtr = &req.SKU
	}

	// Create inventory item
	item := models.InventoryItem{
		ID:             uuid.New().String(),
		BranchID:       branchID,
		Name:           req.Name,
		SKU:            skuPtr,
		Description:    req.Description,
		Category:       req.Category,
		UnitOfMeasure:  req.UnitOfMeasure,
		UnitCost:       req.UnitCost,
		UnitPrice:      req.UnitPrice,
		QuantityOnHand: decimal.Zero, // Start with zero quantity
		ReorderLevel:   req.ReorderLevel,
		Location:       req.Location,
		Supplier:       req.Supplier,
		Status:         status,
		Notes:          req.Notes,
	}

	if err := s.db.Create(&item).Error; err != nil {
		return nil, err
	}

	// Reload item with relationships
	if err := s.db.Preload("Branch").
		Where("id = ?", item.ID).First(&item).Error; err != nil {
		return nil, err
	}

	response := s.toInventoryItemResponse(item)
	return &response, nil
}

// CreateInventoryTransaction creates a new inventory transaction and updates quantity
func (s *InventoryService) CreateInventoryTransaction(req CreateInventoryTransactionRequest, userID string) (*InventoryTransactionResponse, error) {
	// Get inventory item and check access
	var item models.InventoryItem
	if err := s.db.Joins("JOIN user_organization_permissions ON inventory_items.branch_id = user_organization_permissions.organization_id").
		Where("inventory_items.id = ? AND user_organization_permissions.user_id = ?", req.InventoryItemID, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("inventory item not found or access denied")
		}
		return nil, err
	}

	// Validate quantity
	if req.Quantity.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("quantity must be greater than zero")
	}

	// For sales and waste, check if we have enough quantity
	if (req.Type == models.InventoryTransactionTypeSale || req.Type == models.InventoryTransactionTypeWaste) &&
		item.QuantityOnHand.LessThan(req.Quantity) {
		return nil, errors.New("insufficient quantity on hand")
	}

	// Set unit cost if not provided
	unitCost := req.UnitCost
	if unitCost.IsZero() {
		unitCost = item.UnitCost
	}

	// Calculate total cost
	totalCost := unitCost.Mul(req.Quantity)

	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create inventory transaction
	transaction := models.InventoryTransaction{
		ID:              uuid.New().String(),
		BranchID:        item.BranchID,
		InventoryItemID: req.InventoryItemID,
		Type:            req.Type,
		Quantity:        req.Quantity,
		UnitCost:        unitCost,
		TotalCost:       totalCost,
		Reference:       req.Reference,
		Notes:           req.Notes,
		Date:            time.Now(),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Update inventory quantity based on transaction type
	var newQuantity decimal.Decimal
	switch req.Type {
	case models.InventoryTransactionTypePurchase:
		newQuantity = item.QuantityOnHand.Add(req.Quantity)
	case models.InventoryTransactionTypeSale, models.InventoryTransactionTypeWaste:
		newQuantity = item.QuantityOnHand.Sub(req.Quantity)
	case models.InventoryTransactionTypeAdjustment:
		// For adjustments, the quantity represents the new total quantity
		newQuantity = req.Quantity
	default:
		tx.Rollback()
		return nil, errors.New("unsupported transaction type")
	}

	// Update inventory item quantity
	if err := tx.Model(&item).Update("quantity_on_hand", newQuantity).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Reload transaction with relationships
	if err := s.db.Preload("InventoryItem").Preload("InventoryItem.Branch").
		Where("id = ?", transaction.ID).First(&transaction).Error; err != nil {
		return nil, err
	}

	response := s.toInventoryTransactionResponse(transaction)
	return &response, nil
}

// Helper methods
func (s *InventoryService) hasMerchantAccess(userID, branchID string) bool {
	var count int64
	s.db.Model(&models.UserMerchantPermission{}).
		Where("user_id = ? AND branch_id = ?", userID, branchID).
		Count(&count)
	return count > 0
}

func (s *InventoryService) hasOrganizationAccess(userID, organizationID string) bool {
	var count int64
	s.db.Model(&models.UserOrganizationPermission{}).
		Where("user_id = ? AND organization_id = ?", userID, organizationID).
		Count(&count)
	return count > 0
}

// toInventoryItemResponse converts an InventoryItem model to InventoryItemResponse
func (s *InventoryService) toInventoryItemResponse(item models.InventoryItem) InventoryItemResponse {
	// Get image URL from the model (assuming it's stored in a field)
	var imageURL *string
	// Note: The model doesn't have ImageURL field yet, so we'll leave it nil for now
	// This should be added to the model in a future migration

	return InventoryItemResponse{
		ID:             item.ID,
		BranchID:       item.BranchID,
		MerchantID:     item.BranchID, // For backward compatibility
		Name:           item.Name,
		SKU:            item.SKU,
		Description:    item.Description,
		Category:       item.Category,
		UnitOfMeasure:  item.UnitOfMeasure,
		UnitCost:       item.UnitCost,
		UnitPrice:      item.UnitPrice,
		QuantityOnHand: item.QuantityOnHand,
		ReorderLevel:   item.ReorderLevel,
		Location:       item.Location,
		Supplier:       item.Supplier,
		Status:         item.Status,
		Notes:          item.Notes,
		ImageURL:       imageURL,
		CreatedAt:      item.CreatedAt.Format("2006-01-02T15:04:05Z"),
		UpdatedAt:      item.UpdatedAt.Format("2006-01-02T15:04:05Z"),
		Branch:         &item.Branch,
		Merchant:       nil, // For backward compatibility
	}
}

// toInventoryTransactionResponse converts an InventoryTransaction model to InventoryTransactionResponse
func (s *InventoryService) toInventoryTransactionResponse(transaction models.InventoryTransaction) InventoryTransactionResponse {
	response := InventoryTransactionResponse{
		ID:              transaction.ID,
		InventoryItemID: transaction.InventoryItemID,
		Type:            transaction.Type,
		Quantity:        transaction.Quantity,
		UnitCost:        transaction.UnitCost,
		TotalCost:       transaction.TotalCost,
		Reference:       transaction.Reference,
		Notes:           transaction.Notes,
		Date:            transaction.Date.Format("2006-01-02T15:04:05Z"),
		CreatedAt:       transaction.CreatedAt.Format("2006-01-02T15:04:05Z"),
	}

	// Include inventory item if loaded
	if transaction.InventoryItem.ID != "" {
		itemResponse := s.toInventoryItemResponse(transaction.InventoryItem)
		response.InventoryItem = &itemResponse
	}

	return response
}

// UpdateInventoryItem updates an existing inventory item
func (s *InventoryService) UpdateInventoryItem(id string, req UpdateInventoryItemRequest, userID string) (*InventoryItemResponse, error) {
	// Get existing item and check access
	var item models.InventoryItem
	if err := s.db.Joins("JOIN user_organization_permissions ON inventory_items.branch_id = user_organization_permissions.organization_id").
		Where("inventory_items.id = ? AND user_organization_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("inventory item not found or access denied")
		}
		return nil, err
	}

	// Check if SKU already exists for this branch (if SKU is being updated)
	if req.SKU != nil && *req.SKU != "" && (item.SKU == nil || *item.SKU != *req.SKU) {
		var existingItem models.InventoryItem
		if err := s.db.Where("branch_id = ? AND sku = ? AND id != ?", item.BranchID, *req.SKU, id).First(&existingItem).Error; err == nil {
			return nil, errors.New("SKU already exists for this branch")
		}
	}

	// Update fields if provided
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = req.Description
	}
	if req.Category != nil {
		updates["category"] = req.Category
	}
	if req.UnitOfMeasure != nil {
		updates["unit_of_measure"] = *req.UnitOfMeasure
	}
	if req.UnitCost != nil {
		if req.UnitCost.LessThan(decimal.Zero) {
			return nil, errors.New("unit cost cannot be negative")
		}
		updates["unit_cost"] = *req.UnitCost
	}
	if req.UnitPrice != nil {
		if req.UnitPrice.LessThan(decimal.Zero) {
			return nil, errors.New("unit price cannot be negative")
		}
		updates["unit_price"] = *req.UnitPrice
	}
	if req.ReorderLevel != nil {
		updates["reorder_level"] = *req.ReorderLevel
	}
	if req.Location != nil {
		updates["location"] = req.Location
	}
	if req.Supplier != nil {
		updates["supplier"] = req.Supplier
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Notes != nil {
		updates["notes"] = req.Notes
	}
	if req.SKU != nil {
		if *req.SKU == "" {
			updates["sku"] = nil
		} else {
			updates["sku"] = *req.SKU
		}
	}
	if req.ImageURL != nil {
		if *req.ImageURL == "" {
			updates["image_url"] = nil
		} else {
			updates["image_url"] = *req.ImageURL
		}
	}

	// Perform update
	if err := s.db.Model(&item).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload item with relationships
	if err := s.db.Preload("Branch").
		Where("id = ?", item.ID).First(&item).Error; err != nil {
		return nil, err
	}

	response := s.toInventoryItemResponse(item)
	return &response, nil
}

// DeleteInventoryItem deletes an inventory item
func (s *InventoryService) DeleteInventoryItem(id, userID string) error {
	// Get existing item and check access
	var item models.InventoryItem
	if err := s.db.Joins("JOIN user_organization_permissions ON inventory_items.branch_id = user_organization_permissions.organization_id").
		Where("inventory_items.id = ? AND user_organization_permissions.user_id = ?", id, userID).
		First(&item).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("inventory item not found or access denied")
		}
		return err
	}

	// Check if there are any transactions for this item
	var transactionCount int64
	if err := s.db.Model(&models.InventoryTransaction{}).
		Where("inventory_item_id = ?", id).Count(&transactionCount).Error; err != nil {
		return err
	}

	if transactionCount > 0 {
		return errors.New("cannot delete inventory item with existing transactions")
	}

	// Delete the item
	if err := s.db.Delete(&item).Error; err != nil {
		return err
	}

	return nil
}
