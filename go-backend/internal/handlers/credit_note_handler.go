package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// CreditNoteHandler handles credit note-related requests
type CreditNoteHandler struct {
	service *services.CreditNoteService
}

func NewCreditNoteHandler(service *services.CreditNoteService) *CreditNoteHandler {
	return &CreditNoteHandler{service: service}
}

// GetAllCreditNotes handles GET /api/credit-notes
func (h *CreditNoteHandler) GetAllCreditNotes(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>uer<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get credit notes from service
	creditNotes, total, err := h.service.GetAllCreditNotes(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit notes", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteListResponse(creditNotes, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetCreditNoteByID handles GET /api/credit-notes/:id
func (h *CreditNoteHandler) GetCreditNoteByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note ID is required", nil)
		return
	}

	// Get credit note from service
	creditNote, err := h.service.GetCreditNoteByID(id)
	if err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit note", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteResponse(creditNote)
	c.JSON(http.StatusOK, response)
}

// GetCreditNoteByNumber handles GET /api/merchants/:id/credit-notes/by-number/:creditNoteNumber
func (h *CreditNoteHandler) GetCreditNoteByNumber(c *gin.Context) {
	branchID := c.Param("id")
	creditNoteNumber := c.Param("creditNoteNumber")

	// Validate parameters
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}
	if creditNoteNumber == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note number is required", nil)
		return
	}

	// Get credit note from service
	creditNote, err := h.service.GetCreditNoteByNumber(branchID, creditNoteNumber)
	if err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit note", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteResponse(creditNote)
	c.JSON(http.StatusOK, response)
}

// GetCreditNotesByMerchant handles GET /api/merchants/:id/credit-notes
func (h *CreditNoteHandler) GetCreditNotesByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")
	noteType := c.Query("type")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate status if provided
	if status != "" {
		if err := dto.ValidateCreditNoteStatus(status); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note status", err)
			return
		}
	}

	// Validate type if provided
	if noteType != "" {
		if err := dto.ValidateCreditNoteType(noteType); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note type", err)
			return
		}
	}

	// Get credit notes from service
	creditNotes, total, err := h.service.GetCreditNotesByMerchant(branchID, page, limit, search, status, noteType, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit notes", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteListResponse(creditNotes, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetCreditNotesByCustomer handles GET /api/customers/:id/credit-notes
func (h *CreditNoteHandler) GetCreditNotesByCustomer(c *gin.Context) {
	customerID := c.Param("id")

	// Validate customer ID parameter
	if customerID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Customer ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate status if provided
	if status != "" {
		if err := dto.ValidateCreditNoteStatus(status); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note status", err)
			return
		}
	}

	// Get credit notes from service
	creditNotes, total, err := h.service.GetCreditNotesByCustomer(customerID, page, limit, status)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit notes", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteListResponse(creditNotes, total, page, limit, false)
	c.JSON(http.StatusOK, response)
}

// CreateCreditNote handles POST /api/merchants/:id/credit-notes
func (h *CreditNoteHandler) CreateCreditNote(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateCreditNoteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate credit note type
	if err := dto.ValidateCreditNoteType(req.Type); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note type", err)
		return
	}

	// Convert to model
	creditNote, items, err := req.ToCreateCreditNoteModel(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Generate credit note number if not provided
	if creditNote.CreditNoteNumber == "" {
		creditNoteNumber, err := h.service.GenerateCreditNoteNumber(branchID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate credit note number", err)
			return
		}
		creditNote.CreditNoteNumber = creditNoteNumber
	}

	// Validate credit note data
	if err := h.service.ValidateCreditNote(creditNote); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create credit note
	if err := h.service.CreateCreditNote(creditNote, items); err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		if err.Error() == "customer not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Customer not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "invoice not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "credit note number already exists for this merchant" {
			utils.ErrorResponse(c, http.StatusConflict, "Credit note number already exists for this merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create credit note", err)
		return
	}

	// Fetch the created credit note with relationships
	createdCreditNote, err := h.service.GetCreditNoteByID(creditNote.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created credit note", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteResponse(createdCreditNote)
	c.JSON(http.StatusCreated, response)
}

// UpdateCreditNote handles PUT /api/credit-notes/:id
func (h *CreditNoteHandler) UpdateCreditNote(c *gin.Context) {
	creditNoteID := c.Param("id")

	// Validate credit note ID parameter
	if creditNoteID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateCreditNoteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate credit note type if provided
	if req.Type != nil {
		if err := dto.ValidateCreditNoteType(*req.Type); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note type", err)
			return
		}
	}

	// Validate status if provided
	if req.Status != nil {
		if err := dto.ValidateCreditNoteStatus(*req.Status); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note status", err)
			return
		}
	}

	// Convert to model
	updates := req.ToUpdateCreditNoteModel()

	// Update credit note
	updatedCreditNote, err := h.service.UpdateCreditNote(creditNoteID, updates)
	if err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		if err.Error() == "cannot update closed or void credit note" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot update closed or void credit note", err)
			return
		}
		if err.Error() == "customer not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Customer not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "invoice not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "credit note number already exists for this merchant" {
			utils.ErrorResponse(c, http.StatusConflict, "Credit note number already exists for this merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update credit note", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteResponse(updatedCreditNote)
	c.JSON(http.StatusOK, response)
}

// UpdateCreditNoteStatus handles PATCH /api/credit-notes/:id/status
func (h *CreditNoteHandler) UpdateCreditNoteStatus(c *gin.Context) {
	creditNoteID := c.Param("id")

	// Validate credit note ID parameter
	if creditNoteID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateCreditNoteStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate status
	if err := dto.ValidateCreditNoteStatus(req.Status); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid credit note status", err)
		return
	}

	// Convert status to model enum
	var status models.CreditNoteStatus
	switch req.Status {
	case "Draft":
		status = models.CreditNoteStatusDraft
	case "Open":
		status = models.CreditNoteStatusOpen
	case "Closed":
		status = models.CreditNoteStatusClosed
	case "Void":
		status = models.CreditNoteStatusVoid
	}

	// Update credit note status
	updatedCreditNote, err := h.service.UpdateCreditNoteStatus(creditNoteID, status)
	if err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		if err.Error()[:23] == "invalid status transition" {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid status transition", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update credit note status", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteResponse(updatedCreditNote)
	c.JSON(http.StatusOK, response)
}

// DeleteCreditNote handles DELETE /api/credit-notes/:id
func (h *CreditNoteHandler) DeleteCreditNote(c *gin.Context) {
	creditNoteID := c.Param("id")

	// Validate credit note ID parameter
	if creditNoteID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note ID is required", nil)
		return
	}

	// Delete credit note
	if err := h.service.DeleteCreditNote(creditNoteID); err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		if err.Error() == "cannot delete credit note with applied amounts" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete credit note with applied amounts", err)
			return
		}
		if err.Error() == "cannot delete closed credit note" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete closed credit note", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete credit note", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// ApplyCreditToInvoice handles POST /api/credit-notes/:id/apply
func (h *CreditNoteHandler) ApplyCreditToInvoice(c *gin.Context) {
	creditNoteID := c.Param("id")

	// Validate credit note ID parameter
	if creditNoteID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Credit note ID is required", nil)
		return
	}

	// Parse request body
	var req dto.ApplyCreditRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Apply credit to invoice
	application, err := h.service.ApplyCreditToInvoice(creditNoteID, req.InvoiceID, req.Amount)
	if err != nil {
		if err.Error() == "credit note not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Credit note not found", err)
			return
		}
		if err.Error() == "invoice not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "credit note must be open to apply credit" {
			utils.ErrorResponse(c, http.StatusBadRequest, "Credit note must be open to apply credit", err)
			return
		}
		if err.Error() == "application amount must be greater than zero" {
			utils.ErrorResponse(c, http.StatusBadRequest, "Application amount must be greater than zero", err)
			return
		}
		if err.Error() == "application amount cannot exceed credit note balance" {
			utils.ErrorResponse(c, http.StatusBadRequest, "Application amount cannot exceed credit note balance", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to apply credit to invoice", err)
		return
	}

	// Return application details
	c.JSON(http.StatusCreated, gin.H{
		"id":           application.ID,
		"creditNoteId": application.CreditNoteID,
		"invoiceId":    application.InvoiceID,
		"amount":       application.Amount.String(),
		"appliedDate":  application.AppliedDate,
		"message":      "Credit successfully applied to invoice",
	})
}

// GenerateCreditNoteNumber handles POST /api/merchants/:id/credit-notes/generate-number
func (h *CreditNoteHandler) GenerateCreditNoteNumber(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Generate credit note number
	creditNoteNumber, err := h.service.GenerateCreditNoteNumber(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate credit note number", err)
		return
	}

	// Return generated credit note number
	response := dto.GenerateCreditNoteNumberResponse{
		CreditNoteNumber: creditNoteNumber,
	}
	c.JSON(http.StatusOK, response)
}

// GetCreditNoteSummary handles GET /api/merchants/:id/credit-notes/summary
func (h *CreditNoteHandler) GetCreditNoteSummary(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Get credit note summary from service
	summary, err := h.service.GetCreditNoteSummary(branchID, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch credit note summary", err)
		return
	}

	// Convert to response format
	response := dto.ToCreditNoteSummaryResponse(summary)
	c.JSON(http.StatusOK, response)
}

// GetValidStatuses handles GET /api/credit-notes/statuses
func (h *CreditNoteHandler) GetValidStatuses(c *gin.Context) {
	statuses := dto.GetValidCreditNoteStatuses()
	c.JSON(http.StatusOK, gin.H{
		"statuses": statuses,
	})
}

// GetValidTypes handles GET /api/credit-notes/types
func (h *CreditNoteHandler) GetValidTypes(c *gin.Context) {
	types := dto.GetValidCreditNoteTypes()
	c.JSON(http.StatusOK, gin.H{
		"types": types,
	})
}
