package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// PaymentReminderHandler handles payment reminder-related HTTP requests
type PaymentReminderHandler struct {
	paymentReminderService *services.PaymentReminderService
}

// NewPaymentReminderHandler creates a new PaymentReminderHandler
func NewPaymentReminderHandler(paymentReminderService *services.PaymentReminderService) *PaymentReminderHandler {
	return &PaymentReminderHandler{
		paymentReminderService: paymentReminderService,
	}
}

// CreatePaymentReminderRequest represents the request payload for creating a payment reminder
type CreatePaymentReminderRequest struct {
	CustomerID    string    `json:"customerId" binding:"required"`
	InvoiceID     string    `json:"invoiceId" binding:"required"`
	Type          string    `json:"type" binding:"required"`
	ScheduledDate time.Time `json:"scheduledDate" binding:"required"`
	Subject       string    `json:"subject" binding:"required"`
	Message       string    `json:"message" binding:"required"`
}

// UpdatePaymentReminderRequest represents the request payload for updating a payment reminder
type UpdatePaymentReminderRequest struct {
	Type          *string    `json:"type,omitempty"`
	ScheduledDate *time.Time `json:"scheduledDate,omitempty"`
	Subject       *string    `json:"subject,omitempty"`
	Message       *string    `json:"message,omitempty"`
}

// CreateAutomaticRemindersRequest represents the request payload for creating automatic reminders
type CreateAutomaticRemindersRequest struct {
	ReminderType string `json:"reminderType" binding:"required"`
	DaysAfterDue int    `json:"daysAfterDue" binding:"required"`
}

// GetAllPaymentReminders retrieves all payment reminders with pagination
func (h *PaymentReminderHandler) GetAllPaymentReminders(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	paymentReminders, total, err := h.paymentReminderService.GetAllPaymentReminders(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"paymentReminders": paymentReminders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetPaymentReminderByID retrieves a payment reminder by ID
func (h *PaymentReminderHandler) GetPaymentReminderByID(c *gin.Context) {
	id := c.Param("id")

	paymentReminder, err := h.paymentReminderService.GetPaymentReminderByID(id)
	if err != nil {
		if err.Error() == "payment reminder not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentReminder)
}

// GetPaymentRemindersByMerchant retrieves payment reminders for a specific merchant
func (h *PaymentReminderHandler) GetPaymentRemindersByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")
	reminderType := c.Query("type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	paymentReminders, total, err := h.paymentReminderService.GetPaymentRemindersByMerchant(branchID, page, limit, search, status, reminderType, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"paymentReminders": paymentReminders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetPaymentRemindersByCustomer retrieves payment reminders for a specific customer
func (h *PaymentReminderHandler) GetPaymentRemindersByCustomer(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	paymentReminders, total, err := h.paymentReminderService.GetPaymentRemindersByCustomer(customerID, page, limit, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"paymentReminders": paymentReminders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetPaymentRemindersByInvoice retrieves payment reminders for a specific invoice
func (h *PaymentReminderHandler) GetPaymentRemindersByInvoice(c *gin.Context) {
	invoiceID := c.Param("invoiceId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	paymentReminders, total, err := h.paymentReminderService.GetPaymentRemindersByInvoice(invoiceID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"paymentReminders": paymentReminders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreatePaymentReminder creates a new payment reminder
func (h *PaymentReminderHandler) CreatePaymentReminder(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreatePaymentReminderRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create payment reminder
	paymentReminder := &models.PaymentReminder{
		BranchID:      branchID, // TODO: Update to use branchID
		CustomerID:    req.CustomerID,
		InvoiceID:     req.InvoiceID,
		Type:          req.Type,
		Status:        models.PaymentReminderStatusScheduled,
		ScheduledDate: req.ScheduledDate,
		Subject:       req.Subject,
		Message:       req.Message,
	}

	// Validate payment reminder
	if err := h.paymentReminderService.ValidatePaymentReminder(paymentReminder); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create the payment reminder
	if err := h.paymentReminderService.CreatePaymentReminder(paymentReminder); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created payment reminder with relationships
	createdReminder, err := h.paymentReminderService.GetPaymentReminderByID(paymentReminder.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created payment reminder: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdReminder)
}

// UpdatePaymentReminder updates an existing payment reminder
func (h *PaymentReminderHandler) UpdatePaymentReminder(c *gin.Context) {
	id := c.Param("id")
	var req UpdatePaymentReminderRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.PaymentReminder{}
	if req.Type != nil {
		updates.Type = *req.Type
	}
	if req.ScheduledDate != nil {
		updates.ScheduledDate = *req.ScheduledDate
	}
	if req.Subject != nil {
		updates.Subject = *req.Subject
	}
	if req.Message != nil {
		updates.Message = *req.Message
	}

	updatedReminder, err := h.paymentReminderService.UpdatePaymentReminder(id, updates)
	if err != nil {
		if err.Error() == "payment reminder not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedReminder)
}

// DeletePaymentReminder deletes a payment reminder
func (h *PaymentReminderHandler) DeletePaymentReminder(c *gin.Context) {
	id := c.Param("id")

	if err := h.paymentReminderService.DeletePaymentReminder(id); err != nil {
		if err.Error() == "payment reminder not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Payment reminder deleted successfully"})
}

// MarkPaymentReminderAsSent marks a payment reminder as sent
func (h *PaymentReminderHandler) MarkPaymentReminderAsSent(c *gin.Context) {
	id := c.Param("id")

	updatedReminder, err := h.paymentReminderService.MarkPaymentReminderAsSent(id)
	if err != nil {
		if err.Error() == "payment reminder not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedReminder)
}

// CancelPaymentReminder cancels a scheduled payment reminder
func (h *PaymentReminderHandler) CancelPaymentReminder(c *gin.Context) {
	id := c.Param("id")

	updatedReminder, err := h.paymentReminderService.CancelPaymentReminder(id)
	if err != nil {
		if err.Error() == "payment reminder not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedReminder)
}

// GetDuePaymentReminders retrieves payment reminders that are due to be sent
func (h *PaymentReminderHandler) GetDuePaymentReminders(c *gin.Context) {
	branchID := c.Param("merchantId")

	paymentReminders, err := h.paymentReminderService.GetDuePaymentReminders(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"paymentReminders": paymentReminders})
}

// GetUpcomingPaymentReminders retrieves upcoming payment reminders
func (h *PaymentReminderHandler) GetUpcomingPaymentReminders(c *gin.Context) {
	branchID := c.Param("merchantId")
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))

	if days < 1 || days > 365 {
		days = 7
	}

	paymentReminders, err := h.paymentReminderService.GetUpcomingPaymentReminders(branchID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"paymentReminders": paymentReminders})
}

// CreateAutomaticReminders creates automatic payment reminders for overdue invoices
func (h *PaymentReminderHandler) CreateAutomaticReminders(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreateAutomaticRemindersRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if req.DaysAfterDue < 0 || req.DaysAfterDue > 365 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Days after due must be between 0 and 365"})
		return
	}

	createdReminders, err := h.paymentReminderService.CreateAutomaticReminders(branchID, req.ReminderType, req.DaysAfterDue)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":          "Automatic reminders created successfully",
		"createdCount":     len(createdReminders),
		"paymentReminders": createdReminders,
	})
}

// GetPaymentReminderSummary gets summary statistics for payment reminders
func (h *PaymentReminderHandler) GetPaymentReminderSummary(c *gin.Context) {
	branchID := c.Param("merchantId")

	summary, err := h.paymentReminderService.GetPaymentReminderSummary(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}
