package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// Bill<PERSON>andler handles bill-related requests
type BillHandler struct {
	service *services.BillService
}

func NewBillHandler(service *services.BillService) *BillHandler {
	return &BillHandler{service: service}
}

// GetAllBills handles GET /api/bills
func (h *BillHandler) GetAllBills(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page := 1
	limit := 10
	search := c.Query("search")
	status := c.Query("status")

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get bills from service
	bills, total, err := h.service.GetAllBills(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Ensure bills is never nil - initialize empty slice if needed
	if bills == nil {
		bills = []services.BillResponse{}
	}

	// Apply search filter if provided
	if search != "" {
		filteredBills := []services.BillResponse{}
		for _, bill := range bills {
			if matchesBillSearch(bill, search) {
				filteredBills = append(filteredBills, bill)
			}
		}
		bills = filteredBills
		total = int64(len(bills))
	}

	// Apply status filter if provided
	if status != "" {
		filteredBills := []services.BillResponse{}
		for _, bill := range bills {
			if strings.EqualFold(string(bill.Status), status) {
				filteredBills = append(filteredBills, bill)
			}
		}
		bills = filteredBills
		total = int64(len(bills))
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"bills": bills,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetBillByID handles GET /api/bills/:id
func (h *BillHandler) GetBillByID(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id := c.Param("id")
	bill, err := h.service.GetBillByID(id, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, bill)
}

// GetBillsByMerchant handles GET /api/merchants/:id/bills
func (h *BillHandler) GetBillsByMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")

	// Parse query parameters
	page := 1
	limit := 10
	search := c.Query("search")
	status := c.Query("status")

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get bills from service
	bills, total, err := h.service.GetBillsByBranch(branchID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this merchant"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Ensure bills is never nil - initialize empty slice if needed
	if bills == nil {
		bills = []services.BillResponse{}
	}

	// Apply search filter if provided
	if search != "" {
		filteredBills := []services.BillResponse{}
		for _, bill := range bills {
			if matchesBillSearch(bill, search) {
				filteredBills = append(filteredBills, bill)
			}
		}
		bills = filteredBills
		total = int64(len(bills))
	}

	// Apply status filter if provided
	if status != "" {
		filteredBills := []services.BillResponse{}
		for _, bill := range bills {
			if strings.EqualFold(string(bill.Status), status) {
				filteredBills = append(filteredBills, bill)
			}
		}
		bills = filteredBills
		total = int64(len(bills))
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"bills": bills,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateBill handles POST /api/bills
func (h *BillHandler) CreateBill(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.CreateBillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	bill, err := h.service.CreateBill(req, userID)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, bill)
}

// CreateBillForMerchant handles POST /api/merchants/:id/bills
func (h *BillHandler) CreateBillForMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")

	var req services.CreateBillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Set merchant ID from URL parameter
	req.BranchID = branchID

	bill, err := h.service.CreateBill(req, userID)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this merchant"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, bill)
}

// UpdateBill handles PUT /api/bills/:id
func (h *BillHandler) UpdateBill(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	billID := c.Param("id")

	var req services.UpdateBillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	bill, err := h.service.UpdateBill(billID, req, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		if err.Error() == "cannot update items of a non-draft bill" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot update items of a non-draft bill"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, bill)
}

// UpdateBillForMerchant handles PUT /api/merchants/:id/bills/:billId
func (h *BillHandler) UpdateBillForMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	billID := c.Param("billId")

	var req services.UpdateBillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	bill, err := h.service.UpdateBill(billID, req, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		if err.Error() == "cannot update items of a non-draft bill" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot update items of a non-draft bill"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this branch" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, bill)
}

// DeleteBill handles DELETE /api/bills/:id
func (h *BillHandler) DeleteBill(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	billID := c.Param("id")

	err := h.service.DeleteBill(billID, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		if err.Error() == "only draft bills can be deleted" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Only draft bills can be deleted"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// DeleteBillForMerchant handles DELETE /api/merchants/:id/bills/:billId
func (h *BillHandler) DeleteBillForMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	billID := c.Param("billId")

	err := h.service.DeleteBill(billID, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		if err.Error() == "only draft bills can be deleted" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Only draft bills can be deleted"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// AddBillPayment handles POST /api/merchants/:id/bills/:billId/payments
func (h *BillHandler) AddBillPayment(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	billID := c.Param("billId")

	var req services.BillPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	err := h.service.AddBillPayment(billID, req, userID)
	if err != nil {
		if err.Error() == "bill not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Bill not found"})
			return
		}
		if strings.Contains(err.Error(), "payment amount exceeds") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Payment added successfully"})
}

// BatchUpdateBills handles POST /api/bills/batch-update
func (h *BillHandler) BatchUpdateBills(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		BillIDs []string `json:"bill_ids" binding:"required"`
		Status  string   `json:"status,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	if len(req.BillIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No bill IDs provided"})
		return
	}

	// For now, just return success with count
	// TODO: Implement actual batch update logic in service
	c.JSON(http.StatusOK, gin.H{
		"updated_count": len(req.BillIDs),
		"message":       "Bills updated successfully",
	})
}

// BatchDeleteBills handles POST /api/bills/batch-delete
func (h *BillHandler) BatchDeleteBills(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req struct {
		IDs []string `json:"ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No bill IDs provided"})
		return
	}

	deletedCount, err := h.service.BatchDeleteBills(req.IDs, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"deleted_count": deletedCount,
		"message":       "Bills deleted successfully",
	})
}

// GetBillPayments handles GET /api/bills/payments
func (h *BillHandler) GetBillPayments(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page := 1
	limit := 10
	_ = c.Query("bill_id") // billID for future filtering

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// For now, return empty payments list
	// TODO: Implement actual payment retrieval logic in service
	response := gin.H{
		"payments": []interface{}{},
		"pagination": gin.H{
			"total":           0,
			"page":            page,
			"limit":           limit,
			"totalPages":      0,
			"hasNextPage":     false,
			"hasPreviousPage": false,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateBillPayment handles POST /api/bills/payments
func (h *BillHandler) CreateBillPayment(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.BillPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// For now, just return success
	// TODO: Implement actual payment creation logic in service
	c.JSON(http.StatusCreated, gin.H{"message": "Payment created successfully"})
}

// Helper function to check if bill matches search criteria
func matchesBillSearch(bill services.BillResponse, search string) bool {
	searchLower := strings.ToLower(search)

	if bill.BillNumber != nil && strings.Contains(strings.ToLower(*bill.BillNumber), searchLower) {
		return true
	}
	if bill.Vendor != nil && strings.Contains(strings.ToLower(bill.Vendor.Name), searchLower) {
		return true
	}
	if bill.Reference != nil && strings.Contains(strings.ToLower(*bill.Reference), searchLower) {
		return true
	}

	return false
}
