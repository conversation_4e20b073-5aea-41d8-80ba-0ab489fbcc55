package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// InvoiceTemplateHandler handles invoice template-related requests
type InvoiceTemplateHandler struct {
	service *services.InvoiceTemplateService
}

func NewInvoiceTemplateHandler(service *services.InvoiceTemplateService) *InvoiceTemplateHandler {
	return &InvoiceTemplateHandler{service: service}
}

// GetAllInvoiceTemplates handles GET /api/invoice-templates
func (h *InvoiceTemplateHandler) GetAllInvoiceTemplates(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get invoice templates from service
	templates, total, err := h.service.GetAllInvoiceTemplates(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch invoice templates", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateListResponse(templates, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetInvoiceTemplateByID handles GET /api/invoice-templates/:id
func (h *InvoiceTemplateHandler) GetInvoiceTemplateByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Get invoice template from service
	template, err := h.service.GetInvoiceTemplateByID(id)
	if err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch invoice template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(template)
	c.JSON(http.StatusOK, response)
}

// GetInvoiceTemplatesByMerchant handles GET /api/merchants/:id/invoice-templates
func (h *InvoiceTemplateHandler) GetInvoiceTemplatesByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	activeOnly, _ := strconv.ParseBool(c.DefaultQuery("activeOnly", "false"))

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get invoice templates from service
	templates, total, err := h.service.GetInvoiceTemplatesByMerchant(branchID, page, limit, search, activeOnly)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch invoice templates", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateListResponse(templates, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetDefaultInvoiceTemplate handles GET /api/merchants/:id/invoice-templates/default
func (h *InvoiceTemplateHandler) GetDefaultInvoiceTemplate(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Get default invoice template from service
	template, err := h.service.GetDefaultInvoiceTemplate(branchID)
	if err != nil {
		if err.Error() == "default invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Default invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch default invoice template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(template)
	c.JSON(http.StatusOK, response)
}

// CreateInvoiceTemplate handles POST /api/merchants/:id/invoice-templates
func (h *InvoiceTemplateHandler) CreateInvoiceTemplate(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateInvoiceTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate request data
	if err := dto.ValidateInvoiceTemplate(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Convert to model
	template, items, err := req.ToCreateInvoiceTemplateModel(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate template data
	if err := h.service.ValidateInvoiceTemplate(template); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Template validation failed", err)
		return
	}

	// Validate template items
	for i, item := range items {
		if err := h.service.ValidateInvoiceTemplateItem(&item); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Item %d validation failed: %s", i+1, err.Error()), err)
			return
		}
	}

	// Create invoice template
	if err := h.service.CreateInvoiceTemplate(template, items); err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create invoice template", err)
		return
	}

	// Fetch the created invoice template with relationships
	createdTemplate, err := h.service.GetInvoiceTemplateByID(template.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created invoice template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(createdTemplate)
	c.JSON(http.StatusCreated, response)
}

// UpdateInvoiceTemplate handles PUT /api/invoice-templates/:id
func (h *InvoiceTemplateHandler) UpdateInvoiceTemplate(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateInvoiceTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	updates := req.ToUpdateInvoiceTemplateModel()

	// Update invoice template
	updatedTemplate, err := h.service.UpdateInvoiceTemplate(templateID, updates)
	if err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update invoice template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(updatedTemplate)
	c.JSON(http.StatusOK, response)
}

// UpdateInvoiceTemplateItems handles PUT /api/invoice-templates/:id/items
func (h *InvoiceTemplateHandler) UpdateInvoiceTemplateItems(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateInvoiceTemplateItemsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate items
	for i, item := range req.Items {
		if err := dto.ValidateInvoiceTemplateItem(&item, i); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, fmt.Sprintf("Item %d validation failed: %s", i+1, err.Error()), err)
			return
		}
	}

	// Convert to model
	items := dto.ToInvoiceTemplateItemsModel(req.Items)

	// Update template items
	if err := h.service.UpdateInvoiceTemplateItems(templateID, items); err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update template items", err)
		return
	}

	// Fetch the updated template
	updatedTemplate, err := h.service.GetInvoiceTemplateByID(templateID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch updated template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(updatedTemplate)
	c.JSON(http.StatusOK, response)
}

// SetDefaultTemplate handles PATCH /api/invoice-templates/:id/set-default
func (h *InvoiceTemplateHandler) SetDefaultTemplate(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Set template as default
	updatedTemplate, err := h.service.SetDefaultTemplate(templateID)
	if err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to set default template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(updatedTemplate)
	c.JSON(http.StatusOK, response)
}

// DeleteInvoiceTemplate handles DELETE /api/invoice-templates/:id
func (h *InvoiceTemplateHandler) DeleteInvoiceTemplate(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Delete invoice template
	if err := h.service.DeleteInvoiceTemplate(templateID); err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete invoice template", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// CloneInvoiceTemplate handles POST /api/invoice-templates/:id/clone
func (h *InvoiceTemplateHandler) CloneInvoiceTemplate(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invoice template ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CloneInvoiceTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Clone invoice template
	clonedTemplate, err := h.service.CloneInvoiceTemplate(templateID, req.Name)
	if err != nil {
		if err.Error() == "invoice template not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Invoice template not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to clone invoice template", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateResponse(clonedTemplate)
	c.JSON(http.StatusCreated, response)
}

// GetInvoiceTemplateSummary handles GET /api/merchants/:id/invoice-templates/summary
func (h *InvoiceTemplateHandler) GetInvoiceTemplateSummary(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Get invoice template summary from service
	summary, err := h.service.GetInvoiceTemplateSummary(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch invoice template summary", err)
		return
	}

	// Convert to response format
	response := dto.ToInvoiceTemplateSummaryResponse(summary)
	c.JSON(http.StatusOK, response)
}
