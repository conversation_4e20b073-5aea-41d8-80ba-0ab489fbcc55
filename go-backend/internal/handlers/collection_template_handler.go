package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// CollectionTemplateHandler handles collection template-related HTTP requests
type CollectionTemplateHandler struct {
	collectionTemplateService *services.CollectionTemplateService
}

// NewCollectionTemplateHandler creates a new CollectionTemplateHandler
func NewCollectionTemplateHandler(collectionTemplateService *services.CollectionTemplateService) *CollectionTemplateHandler {
	return &CollectionTemplateHandler{
		collectionTemplateService: collectionTemplateService,
	}
}

// CreateCollectionTemplateRequest represents the request payload for creating a collection template
type CreateCollectionTemplateRequest struct {
	Name        string                                `json:"name" binding:"required"`
	Description *string                               `json:"description,omitempty"`
	IsActive    *bool                                 `json:"isActive,omitempty"`
	Steps       []CreateCollectionTemplateStepRequest `json:"steps,omitempty"`
}

// CreateCollectionTemplateStepRequest represents a step in the create request
type CreateCollectionTemplateStepRequest struct {
	StepNumber   int                           `json:"stepNumber,omitempty"`
	Name         string                        `json:"name" binding:"required"`
	Type         models.CollectionActivityType `json:"type" binding:"required"`
	DaysAfterDue int                           `json:"daysAfterDue" binding:"min=0"`
	Subject      *string                       `json:"subject,omitempty"`
	Message      *string                       `json:"message,omitempty"`
	IsActive     *bool                         `json:"isActive,omitempty"`
}

// UpdateCollectionTemplateRequest represents the request payload for updating a collection template
type UpdateCollectionTemplateRequest struct {
	Name        *string                               `json:"name,omitempty"`
	Description *string                               `json:"description,omitempty"`
	IsActive    *bool                                 `json:"isActive,omitempty"`
	Steps       []CreateCollectionTemplateStepRequest `json:"steps,omitempty"`
}

// GetAllCollectionTemplates retrieves all collection templates with pagination
func (h *CollectionTemplateHandler) GetAllCollectionTemplates(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	templates, total, err := h.collectionTemplateService.GetAllCollectionTemplates(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionTemplates": templates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCollectionTemplateByID retrieves a collection template by ID
func (h *CollectionTemplateHandler) GetCollectionTemplateByID(c *gin.Context) {
	id := c.Param("id")

	template, err := h.collectionTemplateService.GetCollectionTemplateByID(id)
	if err != nil {
		if err.Error() == "collection template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// GetCollectionTemplatesByMerchant retrieves collection templates for a specific merchant
func (h *CollectionTemplateHandler) GetCollectionTemplatesByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse active filter
	var isActive *bool
	if activeStr := c.Query("isActive"); activeStr != "" {
		if activeStr == "true" {
			active := true
			isActive = &active
		} else if activeStr == "false" {
			active := false
			isActive = &active
		}
	}

	templates, total, err := h.collectionTemplateService.GetCollectionTemplatesByMerchant(branchID, page, limit, search, isActive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionTemplates": templates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateCollectionTemplate creates a new collection template
func (h *CollectionTemplateHandler) CreateCollectionTemplate(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreateCollectionTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create collection template
	template := &models.CollectionTemplate{
		BranchID:    branchID, // TODO: Update to use branchID
		Name:        req.Name,
		Description: req.Description,
	}

	// Set isActive
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	} else {
		template.IsActive = true
	}

	// Convert request steps to model steps
	steps := make([]models.CollectionTemplateStep, len(req.Steps))
	for i, stepReq := range req.Steps {
		steps[i] = models.CollectionTemplateStep{
			StepNumber:   stepReq.StepNumber,
			Name:         stepReq.Name,
			Type:         stepReq.Type,
			DaysAfterDue: stepReq.DaysAfterDue,
			Subject:      stepReq.Subject,
			Message:      stepReq.Message,
		}

		// Set step isActive
		if stepReq.IsActive != nil {
			steps[i].IsActive = *stepReq.IsActive
		} else {
			steps[i].IsActive = true
		}
	}

	template.Steps = steps

	// Create the collection template
	if err := h.collectionTemplateService.CreateCollectionTemplate(template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created collection template with relationships
	createdTemplate, err := h.collectionTemplateService.GetCollectionTemplateByID(template.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created collection template: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdTemplate)
}

// UpdateCollectionTemplate updates an existing collection template
func (h *CollectionTemplateHandler) UpdateCollectionTemplate(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCollectionTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CollectionTemplate{}
	if req.Name != nil {
		updates.Name = *req.Name
	}
	if req.Description != nil {
		updates.Description = req.Description
	}
	if req.IsActive != nil {
		updates.IsActive = *req.IsActive
	}

	// Convert request steps to model steps if provided
	if len(req.Steps) > 0 {
		steps := make([]models.CollectionTemplateStep, len(req.Steps))
		for i, stepReq := range req.Steps {
			steps[i] = models.CollectionTemplateStep{
				StepNumber:   stepReq.StepNumber,
				Name:         stepReq.Name,
				Type:         stepReq.Type,
				DaysAfterDue: stepReq.DaysAfterDue,
				Subject:      stepReq.Subject,
				Message:      stepReq.Message,
			}

			// Set step isActive
			if stepReq.IsActive != nil {
				steps[i].IsActive = *stepReq.IsActive
			} else {
				steps[i].IsActive = true
			}
		}
		updates.Steps = steps
	}

	updatedTemplate, err := h.collectionTemplateService.UpdateCollectionTemplate(id, updates)
	if err != nil {
		if err.Error() == "collection template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedTemplate)
}

// DeleteCollectionTemplate deletes a collection template
func (h *CollectionTemplateHandler) DeleteCollectionTemplate(c *gin.Context) {
	id := c.Param("id")

	if err := h.collectionTemplateService.DeleteCollectionTemplate(id); err != nil {
		if err.Error() == "collection template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Collection template deleted successfully"})
}

// GetActiveCollectionTemplates gets active collection templates for a merchant
func (h *CollectionTemplateHandler) GetActiveCollectionTemplates(c *gin.Context) {
	branchID := c.Param("merchantId")

	templates, err := h.collectionTemplateService.GetActiveCollectionTemplates(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"collectionTemplates": templates})
}

// GetCollectionTemplateSteps gets steps for a specific template
func (h *CollectionTemplateHandler) GetCollectionTemplateSteps(c *gin.Context) {
	templateID := c.Param("id")

	steps, err := h.collectionTemplateService.GetCollectionTemplateSteps(templateID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"steps": steps})
}

// CreateCollectionTemplateStep creates a new step for a template
func (h *CollectionTemplateHandler) CreateCollectionTemplateStep(c *gin.Context) {
	templateID := c.Param("id")
	var req CreateCollectionTemplateStepRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create step
	step := &models.CollectionTemplateStep{
		TemplateID:   templateID,
		StepNumber:   req.StepNumber,
		Name:         req.Name,
		Type:         req.Type,
		DaysAfterDue: req.DaysAfterDue,
		Subject:      req.Subject,
		Message:      req.Message,
	}

	// Set isActive
	if req.IsActive != nil {
		step.IsActive = *req.IsActive
	} else {
		step.IsActive = true
	}

	// Create the step
	if err := h.collectionTemplateService.CreateCollectionTemplateStep(step); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, step)
}

// UpdateCollectionTemplateStep updates an existing template step
func (h *CollectionTemplateHandler) UpdateCollectionTemplateStep(c *gin.Context) {
	stepID := c.Param("stepId")
	var req CreateCollectionTemplateStepRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CollectionTemplateStep{
		StepNumber:   req.StepNumber,
		Name:         req.Name,
		Type:         req.Type,
		DaysAfterDue: req.DaysAfterDue,
		Subject:      req.Subject,
		Message:      req.Message,
	}

	// Set isActive
	if req.IsActive != nil {
		updates.IsActive = *req.IsActive
	}

	updatedStep, err := h.collectionTemplateService.UpdateCollectionTemplateStep(stepID, updates)
	if err != nil {
		if err.Error() == "collection template step not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedStep)
}

// DeleteCollectionTemplateStep deletes a template step
func (h *CollectionTemplateHandler) DeleteCollectionTemplateStep(c *gin.Context) {
	stepID := c.Param("stepId")

	if err := h.collectionTemplateService.DeleteCollectionTemplateStep(stepID); err != nil {
		if err.Error() == "collection template step not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Collection template step deleted successfully"})
}
