package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// EmailTemplateHandler handles email template-related HTTP requests
type EmailTemplateHandler struct {
	emailTemplateService *services.EmailTemplateService
}

// NewEmailTemplateHandler creates a new EmailTemplateHandler
func NewEmailTemplateHandler(emailTemplateService *services.EmailTemplateService) *EmailTemplateHandler {
	return &EmailTemplateHandler{
		emailTemplateService: emailTemplateService,
	}
}

// CreateEmailTemplateRequest represents the request payload for creating an email template
type CreateEmailTemplateRequest struct {
	Name      string `json:"name" binding:"required"`
	Type      string `json:"type" binding:"required"`
	Subject   string `json:"subject" binding:"required"`
	Body      string `json:"body" binding:"required"`
	IsDefault *bool  `json:"isDefault,omitempty"`
	IsActive  *bool  `json:"isActive,omitempty"`
}

// UpdateEmailTemplateRequest represents the request payload for updating an email template
type UpdateEmailTemplateRequest struct {
	Name      *string `json:"name,omitempty"`
	Type      *string `json:"type,omitempty"`
	Subject   *string `json:"subject,omitempty"`
	Body      *string `json:"body,omitempty"`
	IsDefault *bool   `json:"isDefault,omitempty"`
	IsActive  *bool   `json:"isActive,omitempty"`
}

// ProcessEmailTemplateRequest represents the request payload for processing an email template
type ProcessEmailTemplateRequest struct {
	Variables map[string]string `json:"variables" binding:"required"`
}

// CloneEmailTemplateRequest represents the request payload for cloning an email template
type CloneEmailTemplateRequest struct {
	NewName string `json:"newName" binding:"required"`
}

// GetAllEmailTemplates retrieves all email templates with pagination or simple list
func (h *EmailTemplateHandler) GetAllEmailTemplates(c *gin.Context) {
	// Get user ID from context
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Check if this is a simple list request (no pagination params)
	pageParam := c.Query("page")
	limitParam := c.Query("limit")
	search := c.Query("search")
	defaultOnly := c.Query("default_only") == "true"

	// If no pagination params, return simple array for frontend compatibility
	if pageParam == "" && limitParam == "" {
		emailTemplates, _, err := h.emailTemplateService.GetAllEmailTemplatesByUser(1, 1000, search, userID) // Get up to 1000 templates
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// Filter for default only if requested
		if defaultOnly {
			var defaultTemplates []models.EmailTemplate
			for _, template := range emailTemplates {
				if template.IsDefault {
					defaultTemplates = append(defaultTemplates, template)
				}
			}
			c.JSON(http.StatusOK, defaultTemplates)
			return
		}

		c.JSON(http.StatusOK, emailTemplates)
		return
	}

	// Handle paginated request
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	emailTemplates, total, err := h.emailTemplateService.GetAllEmailTemplatesByUser(page, limit, search, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"emailTemplates": emailTemplates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetEmailTemplateByID retrieves an email template by ID
func (h *EmailTemplateHandler) GetEmailTemplateByID(c *gin.Context) {
	id := c.Param("id")

	emailTemplate, err := h.emailTemplateService.GetEmailTemplateByID(id)
	if err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, emailTemplate)
}

// GetEmailTemplatesByMerchant retrieves email templates for a specific merchant
func (h *EmailTemplateHandler) GetEmailTemplatesByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	templateType := c.Query("type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse active filter
	var isActive *bool
	if activeStr := c.Query("active"); activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			isActive = &active
		}
	}

	emailTemplates, total, err := h.emailTemplateService.GetEmailTemplatesByMerchant(branchID, page, limit, search, templateType, isActive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"emailTemplates": emailTemplates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetDefaultEmailTemplate retrieves the default email template for a merchant and type
func (h *EmailTemplateHandler) GetDefaultEmailTemplate(c *gin.Context) {
	branchID := c.Param("merchantId")
	templateType := c.Query("type")

	if templateType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Template type is required"})
		return
	}

	emailTemplate, err := h.emailTemplateService.GetDefaultEmailTemplate(branchID, templateType)
	if err != nil {
		if err.Error() == "default email template not found for type: "+templateType {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, emailTemplate)
}

// CreateEmailTemplate creates a new email template
func (h *EmailTemplateHandler) CreateEmailTemplate(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreateEmailTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults
	isDefault := false
	if req.IsDefault != nil {
		isDefault = *req.IsDefault
	}
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	// Create email template
	emailTemplate := &models.EmailTemplate{
		BranchID:  branchID, // TODO: Update to use branchID
		Name:      req.Name,
		Type:      req.Type,
		Subject:   req.Subject,
		Body:      req.Body,
		IsDefault: isDefault,
		IsActive:  isActive,
	}

	// Validate email template
	if err := h.emailTemplateService.ValidateEmailTemplate(emailTemplate); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create the email template
	if err := h.emailTemplateService.CreateEmailTemplate(emailTemplate); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created email template with relationships
	createdTemplate, err := h.emailTemplateService.GetEmailTemplateByID(emailTemplate.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created email template: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdTemplate)
}

// UpdateEmailTemplate updates an existing email template
func (h *EmailTemplateHandler) UpdateEmailTemplate(c *gin.Context) {
	id := c.Param("id")
	var req UpdateEmailTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.EmailTemplate{}
	if req.Name != nil {
		updates.Name = *req.Name
	}
	if req.Type != nil {
		updates.Type = *req.Type
	}
	if req.Subject != nil {
		updates.Subject = *req.Subject
	}
	if req.Body != nil {
		updates.Body = *req.Body
	}
	if req.IsDefault != nil {
		updates.IsDefault = *req.IsDefault
	}
	if req.IsActive != nil {
		updates.IsActive = *req.IsActive
	}

	updatedTemplate, err := h.emailTemplateService.UpdateEmailTemplate(id, updates)
	if err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedTemplate)
}

// DeleteEmailTemplate deletes an email template
func (h *EmailTemplateHandler) DeleteEmailTemplate(c *gin.Context) {
	id := c.Param("id")

	if err := h.emailTemplateService.DeleteEmailTemplate(id); err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Email template deleted successfully"})
}

// SetDefaultEmailTemplate sets an email template as the default for its type
func (h *EmailTemplateHandler) SetDefaultEmailTemplate(c *gin.Context) {
	id := c.Param("id")

	updatedTemplate, err := h.emailTemplateService.SetDefaultEmailTemplate(id)
	if err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedTemplate)
}

// CloneEmailTemplate creates a copy of an existing email template
func (h *EmailTemplateHandler) CloneEmailTemplate(c *gin.Context) {
	id := c.Param("id")
	var req CloneEmailTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	clonedTemplate, err := h.emailTemplateService.CloneEmailTemplate(id, req.NewName)
	if err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, clonedTemplate)
}

// ProcessEmailTemplate processes template variables in subject and body
func (h *EmailTemplateHandler) ProcessEmailTemplate(c *gin.Context) {
	id := c.Param("id")
	var req ProcessEmailTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the email template
	emailTemplate, err := h.emailTemplateService.GetEmailTemplateByID(id)
	if err != nil {
		if err.Error() == "email template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Process the template
	processedTemplate, err := h.emailTemplateService.ProcessEmailTemplate(emailTemplate, req.Variables)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, processedTemplate)
}

// GetEmailTemplateTypes returns all unique template types for a merchant
func (h *EmailTemplateHandler) GetEmailTemplateTypes(c *gin.Context) {
	branchID := c.Param("merchantId")

	types, err := h.emailTemplateService.GetEmailTemplateTypes(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"types": types})
}

// GetEmailTemplatesByType retrieves email templates by type for a merchant
func (h *EmailTemplateHandler) GetEmailTemplatesByType(c *gin.Context) {
	branchID := c.Param("merchantId")
	templateType := c.Param("type")

	// Parse activeOnly filter
	activeOnly := c.DefaultQuery("activeOnly", "false") == "true"

	emailTemplates, err := h.emailTemplateService.GetEmailTemplatesByType(branchID, templateType, activeOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"emailTemplates": emailTemplates})
}

// GetEmailTemplateSummary gets summary statistics for email templates
func (h *EmailTemplateHandler) GetEmailTemplateSummary(c *gin.Context) {
	branchID := c.Param("merchantId")

	summary, err := h.emailTemplateService.GetEmailTemplateSummary(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}
