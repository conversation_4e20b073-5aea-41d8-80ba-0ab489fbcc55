package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// CustomerCreditHandler handles customer credit-related HTTP requests
type CustomerCreditHandler struct {
	customerCreditService *services.CustomerCreditService
}

// NewCustomerCreditHandler creates a new CustomerCreditHandler
func NewCustomerCreditHandler(customerCreditService *services.CustomerCreditService) *CustomerCreditHandler {
	return &CustomerCreditHandler{
		customerCreditService: customerCreditService,
	}
}

// CreateCustomerCreditRequest represents the request payload for creating a customer credit
type CreateCustomerCreditRequest struct {
	CustomerID string          `json:"customerId" binding:"required"`
	Amount     decimal.Decimal `json:"amount" binding:"required"`
	Source     string          `json:"source" binding:"required"`
	SourceID   *string         `json:"sourceId,omitempty"`
	Notes      *string         `json:"notes,omitempty"`
	ExpiresAt  *string         `json:"expiresAt,omitempty"` // ISO date string
}

// UpdateCustomerCreditRequest represents the request payload for updating a customer credit
type UpdateCustomerCreditRequest struct {
	Source    *string `json:"source,omitempty"`
	SourceID  *string `json:"sourceId,omitempty"`
	Notes     *string `json:"notes,omitempty"`
	ExpiresAt *string `json:"expiresAt,omitempty"` // ISO date string
}

// ApplyCreditRequest represents the request payload for applying customer credit
type ApplyCreditRequest struct {
	InvoiceID string          `json:"invoiceId" binding:"required"`
	Amount    decimal.Decimal `json:"amount" binding:"required"`
}

// GetAllCustomerCredits retrieves all customer credits with pagination
func (h *CustomerCreditHandler) GetAllCustomerCredits(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customerCredits, total, err := h.customerCreditService.GetAllCustomerCredits(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerCredits": customerCredits,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCustomerCreditByID retrieves a customer credit by ID
func (h *CustomerCreditHandler) GetCustomerCreditByID(c *gin.Context) {
	id := c.Param("id")

	customerCredit, err := h.customerCreditService.GetCustomerCreditByID(id)
	if err != nil {
		if err.Error() == "customer credit not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, customerCredit)
}

// GetCustomerCreditsByCustomer retrieves customer credits for a specific customer
func (h *CustomerCreditHandler) GetCustomerCreditsByCustomer(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	activeOnly := c.DefaultQuery("activeOnly", "false") == "true"

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customerCredits, total, err := h.customerCreditService.GetCustomerCreditsByCustomer(customerID, page, limit, activeOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerCredits": customerCredits,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCustomerCreditsByMerchant retrieves customer credits for a specific merchant
func (h *CustomerCreditHandler) GetCustomerCreditsByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	source := c.Query("source")
	activeOnly := c.DefaultQuery("activeOnly", "false") == "true"

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customerCredits, total, err := h.customerCreditService.GetCustomerCreditsByMerchant(branchID, page, limit, search, source, activeOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerCredits": customerCredits,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateCustomerCredit creates a new customer credit
func (h *CustomerCreditHandler) CreateCustomerCredit(c *gin.Context) {
	var req CreateCustomerCreditRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create customer credit
	customerCredit := &models.CustomerCredit{
		CustomerID: req.CustomerID,
		Amount:     req.Amount,
		Source:     req.Source,
		SourceID:   req.SourceID,
		Notes:      req.Notes,
	}

	// Parse expiry date if provided
	if req.ExpiresAt != nil {
		// Note: In a real implementation, you'd parse the ISO date string
		// For now, we'll skip this as it requires time parsing
	}

	// Validate customer credit
	if err := h.customerCreditService.ValidateCustomerCredit(customerCredit); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create the customer credit
	if err := h.customerCreditService.CreateCustomerCredit(customerCredit); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created customer credit with relationships
	createdCredit, err := h.customerCreditService.GetCustomerCreditByID(customerCredit.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created customer credit: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdCredit)
}

// UpdateCustomerCredit updates an existing customer credit
func (h *CustomerCreditHandler) UpdateCustomerCredit(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCustomerCreditRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CustomerCredit{}
	if req.Source != nil {
		updates.Source = *req.Source
	}
	if req.SourceID != nil {
		updates.SourceID = req.SourceID
	}
	if req.Notes != nil {
		updates.Notes = req.Notes
	}
	// Parse expiry date if provided
	if req.ExpiresAt != nil {
		// Note: In a real implementation, you'd parse the ISO date string
		// For now, we'll skip this as it requires time parsing
	}

	updatedCredit, err := h.customerCreditService.UpdateCustomerCredit(id, updates)
	if err != nil {
		if err.Error() == "customer credit not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedCredit)
}

// DeleteCustomerCredit deletes a customer credit
func (h *CustomerCreditHandler) DeleteCustomerCredit(c *gin.Context) {
	id := c.Param("id")

	if err := h.customerCreditService.DeleteCustomerCredit(id); err != nil {
		if err.Error() == "customer credit not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Customer credit deleted successfully"})
}

// ApplyCustomerCredit applies customer credit to an invoice
func (h *CustomerCreditHandler) ApplyCustomerCredit(c *gin.Context) {
	creditID := c.Param("id")
	var req ApplyCreditRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result, err := h.customerCreditService.ApplyCustomerCredit(creditID, req.InvoiceID, req.Amount)
	if err != nil {
		if err.Error() == "customer credit not found" || err.Error() == "invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetCustomerTotalCredits gets the total available credits for a customer
func (h *CustomerCreditHandler) GetCustomerTotalCredits(c *gin.Context) {
	customerID := c.Param("customerId")

	totalCredits, err := h.customerCreditService.GetCustomerTotalCredits(customerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"customerId":   customerID,
		"totalCredits": totalCredits.String(),
	})
}

// GetCustomerCreditSummary gets summary statistics for customer credits
func (h *CustomerCreditHandler) GetCustomerCreditSummary(c *gin.Context) {
	branchID := c.Param("merchantId")

	summary, err := h.customerCreditService.GetCustomerCreditSummary(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// CreateCreditFromCreditNote creates customer credit from a credit note
func (h *CustomerCreditHandler) CreateCreditFromCreditNote(c *gin.Context) {
	creditNoteID := c.Param("creditNoteId")

	customerCredit, err := h.customerCreditService.CreateCreditFromCreditNote(creditNoteID)
	if err != nil {
		if err.Error() == "credit note not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, customerCredit)
}

// GetCustomerCreditHistory gets the credit application history for a customer
func (h *CustomerCreditHandler) GetCustomerCreditHistory(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	historyItems, total, err := h.customerCreditService.GetCustomerCreditHistory(customerID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"historyItems": historyItems,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}
