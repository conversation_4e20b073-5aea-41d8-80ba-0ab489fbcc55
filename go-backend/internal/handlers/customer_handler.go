package handlers

import (
	"adc-account-backend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CustomerHandler struct {
	customerService *services.CustomerService
}

func NewCustomerHandler(service *services.CustomerService) *CustomerHandler {
	return &CustomerHandler{customerService: service}
}

// GetAllCustomers handles GET /api/customers with pagination, search, and sorting
func (h *CustomerHandler) GetAllCustomers(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	search := c.Query("search")
	sortBy := c.<PERSON>ult<PERSON>("sortBy", "created_at")
	sortOrder := c.DefaultQuery("sortOrder", "desc")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate sort order
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}

	customers, total, err := h.customerService.GetAllCustomers(page, limit, search, sortBy, sortOrder, userIDStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// Calculate pagination metadata
	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := int64(page) < totalPages
	hasPreviousPage := page > 1

	// Return standardized response format
	c.JSON(http.StatusOK, gin.H{
		"data": customers,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCustomerByID handles GET /api/customers/:id
func (h *CustomerHandler) GetCustomerByID(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	customerID := c.Param("id")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	customer, err := h.customerService.GetCustomerByID(customerID, userIDStr)
	if err != nil {
		if err.Error() == "customer not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, customer)
}

// GetCustomersByMerchant handles GET /api/merchants/:id/customers
func (h *CustomerHandler) GetCustomersByMerchant(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	branchID := c.Param("id")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customers, total, err := h.customerService.GetCustomersByBranch(branchID, page, limit, userIDStr)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       customers,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// CreateCustomer handles POST /api/merchants/:id/customers
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	branchID := c.Param("id")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	var req services.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// Set merchant ID from URL parameter
	req.BranchID = branchID

	customer, err := h.customerService.CreateCustomer(req, userIDStr)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "customer with this email already exists for this merchant" {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, customer)
}

// UpdateCustomer handles PUT /api/merchants/:id/customers/:customerId
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	customerID := c.Param("customerId")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	var req services.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	customer, err := h.customerService.UpdateCustomer(customerID, req, userIDStr)
	if err != nil {
		if err.Error() == "customer not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, customer)
}

// DeleteCustomer handles DELETE /api/merchants/:id/customers/:customerId
func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	customerID := c.Param("customerId")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	err := h.customerService.DeleteCustomer(customerID, userIDStr)
	if err != nil {
		if err.Error() == "customer not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "cannot delete customer with existing invoices" {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Customer deleted successfully"})
}

// CreateCustomerGlobal handles POST /api/customers
func (h *CustomerHandler) CreateCustomerGlobal(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	var req services.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// BranchID should be provided in the request body
	if req.BranchID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Branch ID is required"})
		return
	}

	customer, err := h.customerService.CreateCustomer(req, userIDStr)
	if err != nil {
		if err.Error() == "access denied to this branch" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "customer with this email already exists for this branch" {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, customer)
}

// UpdateCustomerGlobal handles PUT /api/customers/:id
func (h *CustomerHandler) UpdateCustomerGlobal(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	customerID := c.Param("id")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	var req services.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	customer, err := h.customerService.UpdateCustomer(customerID, req, userIDStr)
	if err != nil {
		if err.Error() == "customer not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		if err.Error() == "customer with this email already exists for this branch" {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, customer)
}

// DeleteCustomerGlobal handles DELETE /api/customers/:id
func (h *CustomerHandler) DeleteCustomerGlobal(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid user ID"})
		return
	}

	customerID := c.Param("id")
	if customerID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Customer ID is required"})
		return
	}

	err := h.customerService.DeleteCustomer(customerID, userIDStr)
	if err != nil {
		if err.Error() == "customer not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}
