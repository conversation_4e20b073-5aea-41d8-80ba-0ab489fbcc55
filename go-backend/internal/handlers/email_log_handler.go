package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// EmailLogHandler handles email log-related requests
type EmailLogHandler struct {
	service *services.EmailLogService
}

func NewEmailLogHandler(service *services.EmailLogService) *EmailLogHandler {
	return &EmailLogHandler{service: service}
}

// GetAllEmailLogs handles GET /api/email-logs
func (h *EmailLogHandler) GetAllEmailLogs(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get email logs from service
	logs, total, err := h.service.GetAllEmailLogs(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch email logs", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogListResponse(logs, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetEmailLogByID handles GET /api/email-logs/:id
func (h *EmailLogHandler) GetEmailLogByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Get email log from service
	log, err := h.service.GetEmailLogByID(id)
	if err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch email log", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(log)
	c.JSON(http.StatusOK, response)
}

// GetEmailLogsByMerchant handles GET /api/merchants/:id/email-logs
func (h *EmailLogHandler) GetEmailLogsByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate status if provided
	if status != "" {
		if err := dto.ValidateEmailStatus(status); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid email status", err)
			return
		}
	}

	// Get email logs from service
	logs, total, err := h.service.GetEmailLogsByMerchant(branchID, page, limit, search, status, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch email logs", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogListResponse(logs, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetEmailLogsByTemplate handles GET /api/email-templates/:id/logs
func (h *EmailLogHandler) GetEmailLogsByTemplate(c *gin.Context) {
	templateID := c.Param("id")

	// Validate template ID parameter
	if templateID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email template ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate status if provided
	if status != "" {
		if err := dto.ValidateEmailStatus(status); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid email status", err)
			return
		}
	}

	// Get email logs from service
	logs, total, err := h.service.GetEmailLogsByTemplate(templateID, page, limit, status)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch email logs", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogListResponse(logs, total, page, limit, false)
	c.JSON(http.StatusOK, response)
}

// CreateEmailLog handles POST /api/merchants/:id/email-logs
func (h *EmailLogHandler) CreateEmailLog(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateEmailLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate email addresses
	if err := dto.ValidateEmailAddress(req.ToEmail); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid to email address", err)
		return
	}
	if err := dto.ValidateEmailAddress(req.FromEmail); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid from email address", err)
		return
	}

	// Convert to model
	log, err := req.ToCreateEmailLogModel(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate email log data
	if err := h.service.ValidateEmailLog(log); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create email log
	if err := h.service.CreateEmailLog(log); err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		if err.Error() == "email template not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email template not found or does not belong to merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create email log", err)
		return
	}

	// Fetch the created email log with relationships
	createdLog, err := h.service.GetEmailLogByID(log.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created email log", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(createdLog)
	c.JSON(http.StatusCreated, response)
}

// UpdateEmailLog handles PUT /api/email-logs/:id
func (h *EmailLogHandler) UpdateEmailLog(c *gin.Context) {
	logID := c.Param("id")

	// Validate log ID parameter
	if logID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateEmailLogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate email addresses if provided
	if req.ToEmail != nil {
		if err := dto.ValidateEmailAddress(*req.ToEmail); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid to email address", err)
			return
		}
	}
	if req.FromEmail != nil {
		if err := dto.ValidateEmailAddress(*req.FromEmail); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid from email address", err)
			return
		}
	}

	// Convert to model
	updates := req.ToUpdateEmailLogModel()

	// Update email log
	updatedLog, err := h.service.UpdateEmailLog(logID, updates)
	if err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		if err.Error() == "email template not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email template not found or does not belong to merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update email log", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(updatedLog)
	c.JSON(http.StatusOK, response)
}

// UpdateEmailLogStatus handles PATCH /api/email-logs/:id/status
func (h *EmailLogHandler) UpdateEmailLogStatus(c *gin.Context) {
	logID := c.Param("id")

	// Validate log ID parameter
	if logID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateEmailLogStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate status
	if err := dto.ValidateEmailStatus(req.Status); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid email status", err)
		return
	}

	// Convert status to model enum
	var status models.EmailStatus
	switch req.Status {
	case "Sent":
		status = models.EmailStatusSent
	case "Failed":
		status = models.EmailStatusFailed
	case "Scheduled":
		status = models.EmailStatusScheduled
	}

	// Update email log status
	updatedLog, err := h.service.UpdateEmailLogStatus(logID, status, req.ErrorMessage)
	if err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update email log status", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(updatedLog)
	c.JSON(http.StatusOK, response)
}

// DeleteEmailLog handles DELETE /api/email-logs/:id
func (h *EmailLogHandler) DeleteEmailLog(c *gin.Context) {
	logID := c.Param("id")

	// Validate log ID parameter
	if logID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Delete email log
	if err := h.service.DeleteEmailLog(logID); err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete email log", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GetEmailLogSummary handles GET /api/merchants/:id/email-logs/summary
func (h *EmailLogHandler) GetEmailLogSummary(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Get email log summary from service
	summary, err := h.service.GetEmailLogSummary(branchID, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch email log summary", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogSummaryResponse(summary)
	c.JSON(http.StatusOK, response)
}

// GetValidStatuses handles GET /api/email-logs/statuses
func (h *EmailLogHandler) GetValidStatuses(c *gin.Context) {
	statuses := dto.GetValidEmailStatuses()
	c.JSON(http.StatusOK, gin.H{
		"statuses": statuses,
	})
}

// MarkEmailAsSent handles POST /api/email-logs/:id/mark-sent
func (h *EmailLogHandler) MarkEmailAsSent(c *gin.Context) {
	logID := c.Param("id")

	// Validate log ID parameter
	if logID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Mark email as sent
	updatedLog, err := h.service.MarkEmailAsSent(logID)
	if err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to mark email as sent", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(updatedLog)
	c.JSON(http.StatusOK, response)
}

// MarkEmailAsFailed handles POST /api/email-logs/:id/mark-failed
func (h *EmailLogHandler) MarkEmailAsFailed(c *gin.Context) {
	logID := c.Param("id")

	// Validate log ID parameter
	if logID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Email log ID is required", nil)
		return
	}

	// Parse request body for error message
	var req struct {
		ErrorMessage string `json:"errorMessage" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Mark email as failed
	updatedLog, err := h.service.MarkEmailAsFailed(logID, req.ErrorMessage)
	if err != nil {
		if err.Error() == "email log not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Email log not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to mark email as failed", err)
		return
	}

	// Convert to response format
	response := dto.ToEmailLogResponse(updatedLog)
	c.JSON(http.StatusOK, response)
}

// GetRecentEmailLogs handles GET /api/merchants/:id/email-logs/recent
func (h *EmailLogHandler) GetRecentEmailLogs(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse limit parameter
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 50 {
		limit = 10
	}

	// Get recent email logs from service
	logs, err := h.service.GetRecentEmailLogs(branchID, limit)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch recent email logs", err)
		return
	}

	// Convert to response format
	responses := make([]dto.EmailLogResponse, len(logs))
	for i, log := range logs {
		responses[i] = *dto.ToEmailLogResponse(&log)
	}

	c.JSON(http.StatusOK, gin.H{
		"emailLogs": responses,
		"count":     len(responses),
	})
}
