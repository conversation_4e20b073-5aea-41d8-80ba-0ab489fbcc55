package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// BankTransactionHandler handles bank transaction-related requests
type BankTransactionHandler struct {
	service *services.BankTransactionService
}

func NewBankTransactionHandler(service *services.BankTransactionService) *BankTransactionHandler {
	return &BankTransactionHandler{service: service}
}

// GetAllBankTransactions handles GET /api/bank-transactions
func (h *BankTransactionHandler) GetAllBankTransactions(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get bank transactions from service
	transactions, total, err := h.service.GetAllBankTransactions(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank transactions", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionListResponse(transactions, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetBankTransactionByID handles GET /api/bank-transactions/:id
func (h *BankTransactionHandler) GetBankTransactionByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank transaction ID is required", nil)
		return
	}

	// Get bank transaction from service
	transaction, err := h.service.GetBankTransactionByID(id)
	if err != nil {
		if err.Error() == "bank transaction not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank transaction not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank transaction", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionResponse(transaction)
	c.JSON(http.StatusOK, response)
}

// GetBankTransactionsByMerchant handles GET /api/merchants/:id/bank-transactions
func (h *BankTransactionHandler) GetBankTransactionsByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get bank transactions from service
	transactions, total, err := h.service.GetBankTransactionsByMerchant(branchID, page, limit, search, status, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank transactions", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionListResponse(transactions, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetBankTransactionsByBankAccount handles GET /api/bank-accounts/:id/transactions
func (h *BankTransactionHandler) GetBankTransactionsByBankAccount(c *gin.Context) {
	bankAccountID := c.Param("id")

	// Validate bank account ID parameter
	if bankAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get bank transactions from service
	transactions, total, err := h.service.GetBankTransactionsByBankAccount(bankAccountID, page, limit, search, status, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank transactions", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionListResponse(transactions, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// CreateBankTransaction handles POST /api/bank-accounts/:id/transactions
func (h *BankTransactionHandler) CreateBankTransaction(c *gin.Context) {
	bankAccountID := c.Param("id")

	// Validate bank account ID parameter
	if bankAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateBankTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Override bank account ID from URL parameter
	req.BankAccountID = bankAccountID

	// Convert to model
	transaction, err := req.ToCreateBankTransactionModel()
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate bank transaction data
	if err := h.service.ValidateBankTransaction(transaction); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create bank transaction
	if err := h.service.CreateBankTransaction(transaction); err != nil {
		if err.Error() == "bank account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create bank transaction", err)
		return
	}

	// Fetch the created bank transaction with relationships
	createdTransaction, err := h.service.GetBankTransactionByID(transaction.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created bank transaction", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionResponse(createdTransaction)
	c.JSON(http.StatusCreated, response)
}

// CreateBankTransactionBatch handles POST /api/bank-accounts/:id/transactions/batch
func (h *BankTransactionHandler) CreateBankTransactionBatch(c *gin.Context) {
	bankAccountID := c.Param("id")

	// Validate bank account ID parameter
	if bankAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateBankTransactionBatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Override bank account ID from URL parameter
	req.BankAccountID = bankAccountID

	// Convert to models
	transactions, err := req.ToCreateBankTransactionModels()
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate all bank transaction data
	for _, transaction := range transactions {
		if err := h.service.ValidateBankTransaction(transaction); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
			return
		}
	}

	// Create bank transactions in batch
	if err := h.service.CreateBankTransactions(transactions); err != nil {
		if err.Error() == "one or more bank accounts not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create bank transactions", err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Bank transactions created successfully",
		"count":   len(transactions),
	})
}

// UpdateBankTransaction handles PUT /api/bank-transactions/:id
func (h *BankTransactionHandler) UpdateBankTransaction(c *gin.Context) {
	transactionID := c.Param("id")

	// Validate transaction ID parameter
	if transactionID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank transaction ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateBankTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	updates := req.ToUpdateBankTransactionModel()

	// Update bank transaction
	updatedTransaction, err := h.service.UpdateBankTransaction(transactionID, updates)
	if err != nil {
		if err.Error() == "bank transaction not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank transaction not found", err)
			return
		}
		if err.Error() == "bank account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update bank transaction", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionResponse(updatedTransaction)
	c.JSON(http.StatusOK, response)
}

// DeleteBankTransaction handles DELETE /api/bank-transactions/:id
func (h *BankTransactionHandler) DeleteBankTransaction(c *gin.Context) {
	transactionID := c.Param("id")

	// Validate transaction ID parameter
	if transactionID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank transaction ID is required", nil)
		return
	}

	// Delete bank transaction
	if err := h.service.DeleteBankTransaction(transactionID); err != nil {
		if err.Error() == "bank transaction not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank transaction not found", err)
			return
		}
		if err.Error() == "cannot delete reconciled bank transaction" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete reconciled bank transaction", err)
			return
		}
		if err.Error() == "cannot delete bank transaction with existing matches" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete bank transaction with existing matches", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete bank transaction", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// ReconcileBankTransaction handles POST /api/bank-transactions/:id/reconcile
func (h *BankTransactionHandler) ReconcileBankTransaction(c *gin.Context) {
	transactionID := c.Param("id")

	// Validate transaction ID parameter
	if transactionID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank transaction ID is required", nil)
		return
	}

	// Reconcile bank transaction
	reconciledTransaction, err := h.service.ReconcileBankTransaction(transactionID)
	if err != nil {
		if err.Error() == "bank transaction not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank transaction not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to reconcile bank transaction", err)
		return
	}

	// Convert to response format
	response := dto.ToBankTransactionResponse(reconciledTransaction)
	c.JSON(http.StatusOK, response)
}
