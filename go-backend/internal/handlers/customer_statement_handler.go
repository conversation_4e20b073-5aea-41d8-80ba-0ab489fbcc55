package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// CustomerStatementHandler handles customer statement-related HTTP requests
type CustomerStatementHandler struct {
	customerStatementService *services.CustomerStatementService
}

// NewCustomerStatementHandler creates a new CustomerStatementHandler
func NewCustomerStatementHandler(customerStatementService *services.CustomerStatementService) *CustomerStatementHandler {
	return &CustomerStatementHandler{
		customerStatementService: customerStatementService,
	}
}

// CreateCustomerStatementRequest represents the request payload for creating a customer statement
type CreateCustomerStatementRequest struct {
	CustomerID string    `json:"customerId" binding:"required"`
	StartDate  time.Time `json:"startDate" binding:"required"`
	EndDate    time.Time `json:"endDate" binding:"required"`
}

// UpdateCustomerStatementRequest represents the request payload for updating a customer statement
type UpdateCustomerStatementRequest struct {
	StartDate *time.Time `json:"startDate,omitempty"`
	EndDate   *time.Time `json:"endDate,omitempty"`
}

// GenerateStatementRequest represents the request payload for generating a customer statement
type GenerateStatementRequest struct {
	CustomerID string    `json:"customerId" binding:"required"`
	StartDate  time.Time `json:"startDate" binding:"required"`
	EndDate    time.Time `json:"endDate" binding:"required"`
}

// GetAllCustomerStatements retrieves all customer statements with pagination
func (h *CustomerStatementHandler) GetAllCustomerStatements(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customerStatements, total, err := h.customerStatementService.GetAllCustomerStatements(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerStatements": customerStatements,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCustomerStatementByID retrieves a customer statement by ID
func (h *CustomerStatementHandler) GetCustomerStatementByID(c *gin.Context) {
	id := c.Param("id")

	customerStatement, err := h.customerStatementService.GetCustomerStatementByID(id)
	if err != nil {
		if err.Error() == "customer statement not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, customerStatement)
}

// GetCustomerStatementsByMerchant retrieves customer statements for a specific merchant
func (h *CustomerStatementHandler) GetCustomerStatementsByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	customerID := c.Query("customerId")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	customerStatements, total, err := h.customerStatementService.GetCustomerStatementsByMerchant(branchID, page, limit, search, customerID, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerStatements": customerStatements,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCustomerStatementsByCustomer retrieves customer statements for a specific customer
func (h *CustomerStatementHandler) GetCustomerStatementsByCustomer(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	customerStatements, total, err := h.customerStatementService.GetCustomerStatementsByCustomer(customerID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"customerStatements": customerStatements,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateCustomerStatement creates a new customer statement
func (h *CustomerStatementHandler) CreateCustomerStatement(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreateCustomerStatementRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create customer statement
	customerStatement := &models.CustomerStatement{
		BranchID:   branchID, // TODO: Update to use branchID
		CustomerID: req.CustomerID,
		StartDate:  req.StartDate,
		EndDate:    req.EndDate,
	}

	// Validate customer statement
	if err := h.customerStatementService.ValidateCustomerStatement(customerStatement); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create the customer statement
	if err := h.customerStatementService.CreateCustomerStatement(customerStatement); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created customer statement with relationships
	createdStatement, err := h.customerStatementService.GetCustomerStatementByID(customerStatement.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created customer statement: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdStatement)
}

// UpdateCustomerStatement updates an existing customer statement
func (h *CustomerStatementHandler) UpdateCustomerStatement(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCustomerStatementRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CustomerStatement{}
	if req.StartDate != nil {
		updates.StartDate = *req.StartDate
	}
	if req.EndDate != nil {
		updates.EndDate = *req.EndDate
	}

	updatedStatement, err := h.customerStatementService.UpdateCustomerStatement(id, updates)
	if err != nil {
		if err.Error() == "customer statement not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedStatement)
}

// DeleteCustomerStatement deletes a customer statement
func (h *CustomerStatementHandler) DeleteCustomerStatement(c *gin.Context) {
	id := c.Param("id")

	if err := h.customerStatementService.DeleteCustomerStatement(id); err != nil {
		if err.Error() == "customer statement not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Customer statement deleted successfully"})
}

// GenerateCustomerStatement generates a customer statement for a specific period
func (h *CustomerStatementHandler) GenerateCustomerStatement(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req GenerateStatementRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate date range
	if req.StartDate.After(req.EndDate) || req.StartDate.Equal(req.EndDate) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start date must be before end date"})
		return
	}

	// Check if date range is reasonable (not more than 1 year)
	if req.EndDate.Sub(req.StartDate) > 365*24*time.Hour {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Statement period cannot exceed 1 year"})
		return
	}

	generatedStatement, err := h.customerStatementService.GenerateCustomerStatement(branchID, req.CustomerID, req.StartDate, req.EndDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, generatedStatement)
}

// GetCustomerBalance gets the current balance for a customer
func (h *CustomerStatementHandler) GetCustomerBalance(c *gin.Context) {
	customerID := c.Param("customerId")

	balance, err := h.customerStatementService.GetCustomerBalance(customerID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"customerId": customerID,
		"balance":    balance.String(),
	})
}

// GetCustomerStatementSummary gets summary statistics for customer statements
func (h *CustomerStatementHandler) GetCustomerStatementSummary(c *gin.Context) {
	branchID := c.Param("merchantId")

	summary, err := h.customerStatementService.GetCustomerStatementSummary(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}
