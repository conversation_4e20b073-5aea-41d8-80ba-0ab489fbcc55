package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// TaxReportHandler handles tax report-related requests
type TaxReportHandler struct {
	service *services.TaxReportService
}

func NewTaxReportHandler(service *services.TaxReportService) *TaxReportHandler {
	return &TaxReportHandler{service: service}
}

// GetAllTaxReports handles GET /api/tax-reports
func (h *TaxReportHandler) GetAllTaxReports(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get tax reports from service
	reports, total, err := h.service.GetAllTaxReports(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch tax reports", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportListResponse(reports, total, page, limit, true)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax reports", err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTaxReportByID handles GET /api/tax-reports/:id
func (h *TaxReportHandler) GetTaxReportByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Tax report ID is required", nil)
		return
	}

	// Get tax report from service
	report, err := h.service.GetTaxReportByID(id)
	if err != nil {
		if err.Error() == "tax report not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Tax report not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch tax report", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportResponse(report)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax report", err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetTaxReportsByMerchant handles GET /api/merchants/:id/tax-reports
func (h *TaxReportHandler) GetTaxReportsByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	reportType := c.Query("reportType")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate report type if provided
	if reportType != "" {
		if err := dto.ValidateReportType(reportType); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid report type", err)
			return
		}
	}

	// Get tax reports from service
	reports, total, err := h.service.GetTaxReportsByMerchant(branchID, page, limit, reportType, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch tax reports", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportListResponse(reports, total, page, limit, true)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax reports", err)
		return
	}

	c.JSON(http.StatusOK, response)
}

// GenerateTaxReport handles POST /api/merchants/:id/tax-reports/generate
func (h *TaxReportHandler) GenerateTaxReport(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.GenerateTaxReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate report type
	if err := dto.ValidateReportType(req.ReportType); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid report type", err)
		return
	}

	// Validate date range
	if err := dto.ValidateDateRange(req.StartDate, req.EndDate); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid date range", err)
		return
	}

	// Convert to model parameters
	reportType, startDate, endDate, err := req.ToGenerateTaxReportModel()
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Generate tax report
	report, err := h.service.GenerateTaxReport(branchID, reportType, startDate, endDate)
	if err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate tax report", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportResponse(report)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax report", err)
		return
	}

	c.JSON(http.StatusCreated, response)
}

// DeleteTaxReport handles DELETE /api/tax-reports/:id
func (h *TaxReportHandler) DeleteTaxReport(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Tax report ID is required", nil)
		return
	}

	// Delete tax report
	if err := h.service.DeleteTaxReport(id); err != nil {
		if err.Error() == "tax report not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Tax report not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete tax report", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GetTaxReportSummary handles GET /api/merchants/:id/tax-reports/summary
func (h *TaxReportHandler) GetTaxReportSummary(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	reportType := c.Query("reportType")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate report type if provided
	if reportType != "" {
		if err := dto.ValidateReportType(reportType); err != nil {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid report type", err)
			return
		}
	}

	// Get tax report summary from service
	summary, err := h.service.GetTaxReportSummary(branchID, reportType, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch tax report summary", err)
		return
	}

	// Convert to response format
	response := dto.ToTaxReportSummaryResponse(summary)
	c.JSON(http.StatusOK, response)
}

// GetValidReportTypes handles GET /api/tax-reports/types
func (h *TaxReportHandler) GetValidReportTypes(c *gin.Context) {
	types := dto.GetValidReportTypes()
	c.JSON(http.StatusOK, gin.H{
		"reportTypes": types,
	})
}

// ParseTaxReportData handles GET /api/tax-reports/:id/data
func (h *TaxReportHandler) ParseTaxReportData(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Tax report ID is required", nil)
		return
	}

	// Get tax report from service
	report, err := h.service.GetTaxReportByID(id)
	if err != nil {
		if err.Error() == "tax report not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Tax report not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch tax report", err)
		return
	}

	// Parse the tax report data
	data, err := dto.ParseTaxReportData(report.Data)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to parse tax report data", err)
		return
	}

	c.JSON(http.StatusOK, data)
}

// GenerateSalesTaxReport handles POST /api/merchants/:id/tax-reports/sales
func (h *TaxReportHandler) GenerateSalesTaxReport(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body for date range
	var req struct {
		StartDate time.Time `json:"startDate" binding:"required"`
		EndDate   time.Time `json:"endDate" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate date range
	if err := dto.ValidateDateRange(req.StartDate, req.EndDate); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid date range", err)
		return
	}

	// Generate sales tax report
	report, err := h.service.GenerateSalesTaxReport(branchID, req.StartDate, req.EndDate)
	if err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate sales tax report", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportResponse(report)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax report", err)
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GenerateIncomeTaxReport handles POST /api/merchants/:id/tax-reports/income
func (h *TaxReportHandler) GenerateIncomeTaxReport(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body for date range
	var req struct {
		StartDate time.Time `json:"startDate" binding:"required"`
		EndDate   time.Time `json:"endDate" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate date range
	if err := dto.ValidateDateRange(req.StartDate, req.EndDate); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid date range", err)
		return
	}

	// Generate income tax report
	report, err := h.service.GenerateIncomeTaxReport(branchID, req.StartDate, req.EndDate)
	if err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate income tax report", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportResponse(report)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax report", err)
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GeneratePayrollTaxReport handles POST /api/merchants/:id/tax-reports/payroll
func (h *TaxReportHandler) GeneratePayrollTaxReport(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body for date range
	var req struct {
		StartDate time.Time `json:"startDate" binding:"required"`
		EndDate   time.Time `json:"endDate" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Validate date range
	if err := dto.ValidateDateRange(req.StartDate, req.EndDate); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid date range", err)
		return
	}

	// Generate payroll tax report
	report, err := h.service.GeneratePayrollTaxReport(branchID, req.StartDate, req.EndDate)
	if err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate payroll tax report", err)
		return
	}

	// Convert to response format
	response, err := dto.ToTaxReportResponse(report)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to format tax report", err)
		return
	}

	c.JSON(http.StatusCreated, response)
}
