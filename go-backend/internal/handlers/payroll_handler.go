package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type PayrollHandler struct {
	payrollService *services.PayrollService
}

func NewPayrollHandler(payrollService *services.PayrollService) *PayrollHandler {
	return &PayrollHandler{
		payrollService: payrollService,
	}
}

// GetEmployees godoc
// @Summary Get all employees
// @Description Get all employees with pagination
// @Tags employees
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.EmployeeResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /employees [get]
func (h *PayrollHandler) GetEmployees(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	employees, total, err := h.payrollService.GetAllEmployees(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       employees,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetEmployeesByMerchant godoc
// @Summary Get employees by merchant
// @Description Get employees for a specific merchant with pagination
// @Tags employees
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.EmployeeResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/employees [get]
func (h *PayrollHandler) GetEmployeesByMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	branchID := c.Param("merchantId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	employees, total, err := h.payrollService.GetEmployeesByMerchant(branchID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       employees,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetEmployee godoc
// @Summary Get employee by ID
// @Description Get a specific employee by ID
// @Tags employees
// @Accept json
// @Produce json
// @Param id path string true "Employee ID"
// @Success 200 {object} services.EmployeeResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /employees/{id} [get]
func (h *PayrollHandler) GetEmployee(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Employee ID is required"})
		return
	}

	employee, err := h.payrollService.GetEmployeeByID(id, userID)
	if err != nil {
		if err.Error() == "employee not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, employee)
}

// CreateEmployee godoc
// @Summary Create a new employee
// @Description Create a new employee record
// @Tags employees
// @Accept json
// @Produce json
// @Param employee body services.CreateEmployeeRequest true "Employee data"
// @Success 201 {object} services.EmployeeResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /employees [post]
func (h *PayrollHandler) CreateEmployee(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateEmployeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	employee, err := h.payrollService.CreateEmployee(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, employee)
}

// CreatePayrollRun godoc
// @Summary Create a new payroll run
// @Description Create a new payroll run for a pay period
// @Tags payroll
// @Accept json
// @Produce json
// @Param payrollRun body services.CreatePayrollRunRequest true "Payroll Run data"
// @Success 201 {object} services.PayrollRunResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /payroll-runs [post]
func (h *PayrollHandler) CreatePayrollRun(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreatePayrollRunRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	payrollRun, err := h.payrollService.CreatePayrollRun(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, payrollRun)
}

// GetAllEmployees is an alias for GetEmployees for route compatibility
func (h *PayrollHandler) GetAllEmployees(c *gin.Context) {
	h.GetEmployees(c)
}

// GetEmployeeByID is an alias for GetEmployee for route compatibility
func (h *PayrollHandler) GetEmployeeByID(c *gin.Context) {
	h.GetEmployee(c)
}

// UpdateEmployee placeholder for route compatibility
func (h *PayrollHandler) UpdateEmployee(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "UpdateEmployee - Not implemented yet"})
}

// DeleteEmployee placeholder for route compatibility
func (h *PayrollHandler) DeleteEmployee(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "DeleteEmployee - Not implemented yet"})
}

// GetAllPayrollRuns placeholder for route compatibility
func (h *PayrollHandler) GetAllPayrollRuns(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetAllPayrollRuns - Not implemented yet"})
}

// GetPayrollRunByID placeholder for route compatibility
func (h *PayrollHandler) GetPayrollRunByID(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetPayrollRunByID - Not implemented yet"})
}

// GetPayrollRunsByMerchant placeholder for route compatibility
func (h *PayrollHandler) GetPayrollRunsByMerchant(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetPayrollRunsByMerchant - Not implemented yet"})
}

// UpdatePayrollRun placeholder for route compatibility
func (h *PayrollHandler) UpdatePayrollRun(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "UpdatePayrollRun - Not implemented yet"})
}

// DeletePayrollRun placeholder for route compatibility
func (h *PayrollHandler) DeletePayrollRun(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "DeletePayrollRun - Not implemented yet"})
}

// GetAllPayrollDetails placeholder for route compatibility
func (h *PayrollHandler) GetAllPayrollDetails(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetAllPayrollDetails - Not implemented yet"})
}

// GetPayrollDetailByID placeholder for route compatibility
func (h *PayrollHandler) GetPayrollDetailByID(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetPayrollDetailByID - Not implemented yet"})
}

// GetPayrollDetailsByMerchant placeholder for route compatibility
func (h *PayrollHandler) GetPayrollDetailsByMerchant(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "GetPayrollDetailsByMerchant - Not implemented yet"})
}

// CreatePayrollDetail placeholder for route compatibility
func (h *PayrollHandler) CreatePayrollDetail(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "CreatePayrollDetail - Not implemented yet"})
}

// UpdatePayrollDetail placeholder for route compatibility
func (h *PayrollHandler) UpdatePayrollDetail(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "UpdatePayrollDetail - Not implemented yet"})
}

// DeletePayrollDetail placeholder for route compatibility
func (h *PayrollHandler) DeletePayrollDetail(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "DeletePayrollDetail - Not implemented yet"})
}
