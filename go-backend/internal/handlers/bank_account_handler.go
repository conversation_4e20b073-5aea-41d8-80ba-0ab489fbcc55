package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// BankAccountHandler handles bank account-related requests
type BankAccountHandler struct {
	service *services.BankAccountService
}

func NewBankAccountHandler(service *services.BankAccountService) *BankAccountHandler {
	return &BankAccountHandler{service: service}
}

// GetAllBankAccounts handles GET /api/bank-accounts
func (h *BankAccountHandler) GetAllBankAccounts(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	accountType := c.Query("accountType")
	bankName := c.Query("bankName")
	sortBy := c.DefaultQuery("sortBy", "accountName")
	sortOrder := c.DefaultQuery("sortOrder", "asc")

	// Parse isActive parameter
	var isActive *bool
	if isActiveStr := c.Query("isActive"); isActiveStr != "" {
		if isActiveStr == "true" {
			isActiveVal := true
			isActive = &isActiveVal
		} else if isActiveStr == "false" {
			isActiveVal := false
			isActive = &isActiveVal
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Validate sort order
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "asc"
	}

	// Get bank accounts from service
	bankAccounts, total, err := h.service.GetAllBankAccounts(page, limit, search, accountType, bankName, isActive, sortBy, sortOrder, userID.(string))
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank accounts", err)
		return
	}

	// Convert to response format - use consistent format like organizations
	bankAccountResponses := make([]interface{}, len(bankAccounts))
	for i, bankAccount := range bankAccounts {
		bankAccountResponses[i] = dto.ToBankAccountResponse(&bankAccount)
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  bankAccountResponses,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// GetBankAccountByID handles GET /api/bank-accounts/:id
func (h *BankAccountHandler) GetBankAccountByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Get bank account from service
	bankAccount, err := h.service.GetBankAccountByID(id)
	if err != nil {
		if err.Error() == "bank account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank account", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(bankAccount)
	c.JSON(http.StatusOK, response)
}

// GetBankAccountsByMerchant handles GET /api/merchants/:id/bank-accounts
func (h *BankAccountHandler) GetBankAccountsByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get bank accounts from service
	bankAccounts, total, err := h.service.GetBankAccountsByMerchant(branchID, page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch bank accounts", err)
		return
	}

	// Convert to response format - use consistent format like organizations
	bankAccountResponses := make([]interface{}, len(bankAccounts))
	for i, bankAccount := range bankAccounts {
		bankAccountResponses[i] = dto.ToBankAccountResponse(&bankAccount)
	}

	c.JSON(http.StatusOK, gin.H{
		"data":  bankAccountResponses,
		"total": total,
		"page":  page,
		"limit": limit,
	})
}

// CreateBankAccount handles POST /api/merchants/:id/bank-accounts
func (h *BankAccountHandler) CreateBankAccount(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateBankAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	bankAccount, err := req.ToCreateBankAccountModel(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate bank account data
	if err := h.service.ValidateBankAccount(bankAccount); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create bank account
	if err := h.service.CreateBankAccount(bankAccount); err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		if err.Error() == "chart of account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Chart of account not found", err)
			return
		}
		if err.Error() == "chart of account is already linked to another bank account" {
			utils.ErrorResponse(c, http.StatusConflict, "Chart of account is already linked to another bank account", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create bank account", err)
		return
	}

	// Fetch the created bank account with relationships
	createdBankAccount, err := h.service.GetBankAccountByID(bankAccount.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created bank account", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(createdBankAccount)
	c.JSON(http.StatusCreated, response)
}

// UpdateBankAccount handles PUT /api/merchants/:id/bank-accounts/:bankAccountId
func (h *BankAccountHandler) UpdateBankAccount(c *gin.Context) {
	branchID := c.Param("id")
	bankAccountID := c.Param("bankAccountId")

	// Validate parameters
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}
	if bankAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateBankAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	updates := req.ToUpdateBankAccountModel()

	// Update bank account
	updatedBankAccount, err := h.service.UpdateBankAccount(bankAccountID, updates)
	if err != nil {
		if err.Error() == "bank account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		if err.Error() == "chart of account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Chart of account not found", err)
			return
		}
		if err.Error() == "chart of account is already linked to another bank account" {
			utils.ErrorResponse(c, http.StatusConflict, "Chart of account is already linked to another bank account", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update bank account", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(updatedBankAccount)
	c.JSON(http.StatusOK, response)
}

// DeleteBankAccount handles DELETE /api/merchants/:id/bank-accounts/:bankAccountId
func (h *BankAccountHandler) DeleteBankAccount(c *gin.Context) {
	branchID := c.Param("id")
	bankAccountID := c.Param("bankAccountId")

	// Validate parameters
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}
	if bankAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Bank account ID is required", nil)
		return
	}

	// Delete bank account
	if err := h.service.DeleteBankAccount(bankAccountID); err != nil {
		if err.Error() == "bank account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Bank account not found", err)
			return
		}
		if err.Error() == "cannot delete bank account with existing transactions" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete bank account with existing transactions", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete bank account", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GetLinkedBankAccount handles GET /api/accounts/:id/bank-account
func (h *BankAccountHandler) GetLinkedBankAccount(c *gin.Context) {
	accountID := c.Param("id")

	// Validate account ID parameter
	if accountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Account ID is required", nil)
		return
	}

	// Get linked bank account from service
	bankAccount, err := h.service.GetBankAccountByChartOfAccountID(accountID)
	if err != nil {
		if err.Error() == "bank account not found" {
			// Return a response indicating no bank account is linked
			c.JSON(http.StatusOK, map[string]interface{}{
				"linked":      false,
				"bankAccount": nil,
			})
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch linked bank account", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(bankAccount)
	c.JSON(http.StatusOK, map[string]interface{}{
		"linked":      true,
		"bankAccount": response,
	})
}

// CheckCoaLinked handles GET /api/banking/check-coa-linked
func (h *BankAccountHandler) CheckCoaLinked(c *gin.Context) {
	coaID := c.Query("coaId")

	// Validate coaId parameter
	if coaID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "coaId query parameter is required", nil)
		return
	}

	// Get linked bank account from service
	bankAccount, err := h.service.GetBankAccountByChartOfAccountID(coaID)
	if err != nil {
		if err.Error() == "bank account not found" {
			// Return a response indicating no bank account is linked
			c.JSON(http.StatusOK, map[string]interface{}{
				"isLinked":    false,
				"bankAccount": nil,
			})
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to check COA linkage", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(bankAccount)
	c.JSON(http.StatusOK, map[string]interface{}{
		"isLinked": true,
		"bankAccount": map[string]interface{}{
			"id":          response.ID,
			"accountName": response.AccountName,
		},
	})
}

// CreateBankAccountForUser handles POST /api/banking/accounts
func (h *BankAccountHandler) CreateBankAccountForUser(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("userID")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated", nil)
		return
	}

	// Parse request body
	var req dto.CreateBankAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Get user's default branch
	branchID, err := h.service.GetUserDefaultBranchID(userID.(string))
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "User has no default branch", err)
		return
	}

	// Convert to model
	bankAccount, err := req.ToCreateBankAccountModelForUser(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Validate the bank account
	if err := h.service.ValidateBankAccount(bankAccount); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid bank account data", err)
		return
	}

	// Create bank account
	if err := h.service.CreateBankAccount(bankAccount); err != nil {
		if err.Error() == "branch not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Branch not found", err)
			return
		}
		if err.Error() == "chart of account not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Chart of account not found", err)
			return
		}
		if err.Error() == "chart of account is already linked to another bank account" {
			utils.ErrorResponse(c, http.StatusConflict, "Chart of account is already linked to another bank account", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create bank account", err)
		return
	}

	// Convert to response format
	response := dto.ToBankAccountResponse(bankAccount)
	c.JSON(http.StatusCreated, response)
}
