package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/dto"
	"adc-account-backend/internal/services"
	"adc-account-backend/internal/utils"

	"github.com/gin-gonic/gin"
)

// SalesOrderHandler handles sales order-related requests
type SalesOrderHandler struct {
	service *services.SalesOrderService
}

func NewSalesOrderHandler(service *services.SalesOrderService) *SalesOrderHandler {
	return &SalesOrderHandler{service: service}
}

// GetAllSalesOrders handles GET /api/sales-orders
func (h *SalesOrderHandler) GetAllSalesOrders(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.Default<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("limit", "10"))
	search := c.Query("search")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get sales orders from service
	orders, total, err := h.service.GetAllSalesOrders(page, limit, search)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales orders", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderListResponse(orders, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetSalesOrderByID handles GET /api/sales-orders/:id
func (h *SalesOrderHandler) GetSalesOrderByID(c *gin.Context) {
	id := c.Param("id")

	// Validate ID parameter
	if id == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Sales order ID is required", nil)
		return
	}

	// Get sales order from service
	order, err := h.service.GetSalesOrderByID(id)
	if err != nil {
		if err.Error() == "sales order not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Sales order not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales order", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderResponse(order)
	c.JSON(http.StatusOK, response)
}

// GetSalesOrderByOrderNumber handles GET /api/merchants/:id/sales-orders/by-number/:orderNumber
func (h *SalesOrderHandler) GetSalesOrderByOrderNumber(c *gin.Context) {
	branchID := c.Param("id")
	orderNumber := c.Param("orderNumber")

	// Validate parameters
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}
	if orderNumber == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Order number is required", nil)
		return
	}

	// Get sales order from service
	order, err := h.service.GetSalesOrderByOrderNumber(branchID, orderNumber)
	if err != nil {
		if err.Error() == "sales order not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Sales order not found", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales order", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderResponse(order)
	c.JSON(http.StatusOK, response)
}

// GetSalesOrdersByMerchant handles GET /api/merchants/:id/sales-orders
func (h *SalesOrderHandler) GetSalesOrdersByMerchant(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get sales orders from service
	orders, total, err := h.service.GetSalesOrdersByMerchant(branchID, page, limit, search, status, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales orders", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderListResponse(orders, total, page, limit, true)
	c.JSON(http.StatusOK, response)
}

// GetSalesOrdersByCustomer handles GET /api/customers/:id/sales-orders
func (h *SalesOrderHandler) GetSalesOrdersByCustomer(c *gin.Context) {
	customerID := c.Param("id")

	// Validate customer ID parameter
	if customerID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Customer ID is required", nil)
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Get sales orders from service
	orders, total, err := h.service.GetSalesOrdersByCustomer(customerID, page, limit, status)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales orders", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderListResponse(orders, total, page, limit, false)
	c.JSON(http.StatusOK, response)
}

// CreateSalesOrder handles POST /api/merchants/:id/sales-orders
func (h *SalesOrderHandler) CreateSalesOrder(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse request body
	var req dto.CreateSalesOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	order, err := req.ToCreateSalesOrderModel(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request data", err)
		return
	}

	// Generate order number if not provided
	if order.OrderNumber == "" {
		orderNumber, err := h.service.GenerateOrderNumber(branchID)
		if err != nil {
			utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate order number", err)
			return
		}
		order.OrderNumber = orderNumber
	}

	// Validate sales order data
	if err := h.service.ValidateSalesOrder(order); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Validation failed", err)
		return
	}

	// Create sales order
	if err := h.service.CreateSalesOrder(order); err != nil {
		if err.Error() == "merchant not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Merchant not found", err)
			return
		}
		if err.Error() == "customer not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Customer not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "order number already exists for this merchant" {
			utils.ErrorResponse(c, http.StatusConflict, "Order number already exists for this merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create sales order", err)
		return
	}

	// Fetch the created sales order with relationships
	createdOrder, err := h.service.GetSalesOrderByID(order.ID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch created sales order", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderResponse(createdOrder)
	c.JSON(http.StatusCreated, response)
}

// UpdateSalesOrder handles PUT /api/sales-orders/:id
func (h *SalesOrderHandler) UpdateSalesOrder(c *gin.Context) {
	orderID := c.Param("id")

	// Validate order ID parameter
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Sales order ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateSalesOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Convert to model
	updates := req.ToUpdateSalesOrderModel()

	// Update sales order
	updatedOrder, err := h.service.UpdateSalesOrder(orderID, updates)
	if err != nil {
		if err.Error() == "sales order not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Sales order not found", err)
			return
		}
		if err.Error() == "cannot update completed or cancelled sales order" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot update completed or cancelled sales order", err)
			return
		}
		if err.Error() == "customer not found or does not belong to merchant" {
			utils.ErrorResponse(c, http.StatusNotFound, "Customer not found or does not belong to merchant", err)
			return
		}
		if err.Error() == "order number already exists for this merchant" {
			utils.ErrorResponse(c, http.StatusConflict, "Order number already exists for this merchant", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update sales order", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderResponse(updatedOrder)
	c.JSON(http.StatusOK, response)
}

// UpdateSalesOrderStatus handles PATCH /api/sales-orders/:id/status
func (h *SalesOrderHandler) UpdateSalesOrderStatus(c *gin.Context) {
	orderID := c.Param("id")

	// Validate order ID parameter
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Sales order ID is required", nil)
		return
	}

	// Parse request body
	var req dto.UpdateSalesOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request body", err)
		return
	}

	// Update sales order status
	updatedOrder, err := h.service.UpdateSalesOrderStatus(orderID, req.Status)
	if err != nil {
		if err.Error() == "sales order not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Sales order not found", err)
			return
		}
		if err.Error()[:23] == "invalid status transition" {
			utils.ErrorResponse(c, http.StatusBadRequest, "Invalid status transition", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to update sales order status", err)
		return
	}

	// Convert to response format
	response := dto.ToSalesOrderResponse(updatedOrder)
	c.JSON(http.StatusOK, response)
}

// DeleteSalesOrder handles DELETE /api/sales-orders/:id
func (h *SalesOrderHandler) DeleteSalesOrder(c *gin.Context) {
	orderID := c.Param("id")

	// Validate order ID parameter
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Sales order ID is required", nil)
		return
	}

	// Delete sales order
	if err := h.service.DeleteSalesOrder(orderID); err != nil {
		if err.Error() == "sales order not found" {
			utils.ErrorResponse(c, http.StatusNotFound, "Sales order not found", err)
			return
		}
		if err.Error() == "cannot delete completed sales order" {
			utils.ErrorResponse(c, http.StatusConflict, "Cannot delete completed sales order", err)
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete sales order", err)
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// GenerateOrderNumber handles POST /api/merchants/:id/sales-orders/generate-number
func (h *SalesOrderHandler) GenerateOrderNumber(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Generate order number
	orderNumber, err := h.service.GenerateOrderNumber(branchID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate order number", err)
		return
	}

	// Return generated order number
	response := dto.GenerateOrderNumberResponse{
		OrderNumber: orderNumber,
	}
	c.JSON(http.StatusOK, response)
}

// GetSalesOrderSummary handles GET /api/merchants/:id/sales-orders/summary
func (h *SalesOrderHandler) GetSalesOrderSummary(c *gin.Context) {
	branchID := c.Param("id")

	// Validate merchant ID parameter
	if branchID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Merchant ID is required", nil)
		return
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	// Get sales order summary from service
	summary, err := h.service.GetSalesOrderSummary(branchID, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch sales order summary", err)
		return
	}

	// Convert to response format
	response := &dto.SalesOrderSummary{
		TotalOrders:      summary.TotalOrders,
		DraftOrders:      summary.DraftOrders,
		ConfirmedOrders:  summary.ConfirmedOrders,
		ProcessingOrders: summary.ProcessingOrders,
		CompletedOrders:  summary.CompletedOrders,
		CancelledOrders:  summary.CancelledOrders,
	}
	c.JSON(http.StatusOK, response)
}
