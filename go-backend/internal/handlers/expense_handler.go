package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// ExpenseHandler handles expense-related requests
type ExpenseHandler struct {
	service *services.ExpenseService
}

func NewExpenseHandler(service *services.ExpenseService) *ExpenseHandler {
	return &ExpenseHandler{service: service}
}

// GetAllExpenses handles GET /api/expenses
func (h *ExpenseHandler) GetAllExpenses(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Parse query parameters
	page := 1
	limit := 10
	search := c.Query("search")
	status := c.Query("status")
	category := c.Query("category")

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get expenses from service
	expenses, total, err := h.service.GetAllExpenses(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Apply search filter if provided
	if search != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			if matchesExpenseSearch(expense, search) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Apply status filter if provided
	if status != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			if strings.EqualFold(string(expense.Status), status) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Apply category filter if provided (this would need to be implemented based on your category system)
	if category != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			// For now, we'll match against description since there's no explicit category field
			if strings.Contains(strings.ToLower(expense.Description), strings.ToLower(category)) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"data": expenses,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetExpenseByID handles GET /api/expenses/:id
func (h *ExpenseHandler) GetExpenseByID(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	id := c.Param("id")
	expense, err := h.service.GetExpenseByID(id, userID)
	if err != nil {
		if err.Error() == "expense not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, expense)
}

// GetExpensesByMerchant handles GET /api/merchants/:id/expenses
func (h *ExpenseHandler) GetExpensesByMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")

	// Parse query parameters
	page := 1
	limit := 10
	search := c.Query("search")
	status := c.Query("status")
	category := c.Query("category")

	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Get expenses from service
	expenses, total, err := h.service.GetExpensesByMerchant(branchID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this merchant"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Apply search filter if provided
	if search != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			if matchesExpenseSearch(expense, search) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Apply status filter if provided
	if status != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			if strings.EqualFold(string(expense.Status), status) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Apply category filter if provided
	if category != "" {
		filteredExpenses := []services.ExpenseResponse{}
		for _, expense := range expenses {
			if strings.Contains(strings.ToLower(expense.Description), strings.ToLower(category)) {
				filteredExpenses = append(filteredExpenses, expense)
			}
		}
		expenses = filteredExpenses
		total = int64(len(expenses))
	}

	// Calculate pagination
	totalPages := (int(total) + limit - 1) / limit

	response := gin.H{
		"expenses": expenses,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     page < totalPages,
			"hasPreviousPage": page > 1,
		},
	}

	c.JSON(http.StatusOK, response)
}

// CreateExpense handles POST /api/merchants/:id/expenses
func (h *ExpenseHandler) CreateExpense(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID := c.Param("id")

	var req services.CreateExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Set branch ID from URL parameter
	req.BranchID = branchID

	expense, err := h.service.CreateExpense(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this merchant"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this merchant" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		if err.Error() == "account not found or does not belong to this merchant" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, expense)
}

// UpdateExpense handles PUT /api/merchants/:id/expenses/:expenseId
func (h *ExpenseHandler) UpdateExpense(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	expenseID := c.Param("expenseId")

	var req services.UpdateExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	expense, err := h.service.UpdateExpense(expenseID, req, userID)
	if err != nil {
		if err.Error() == "expense not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense not found"})
			return
		}
		if err.Error() == "cannot update amount of an approved expense" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot update amount of an approved expense"})
			return
		}
		if strings.Contains(err.Error(), "not found or does not belong to this merchant") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor or customer"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, expense)
}

// DeleteExpense handles DELETE /api/merchants/:id/expenses/:expenseId
func (h *ExpenseHandler) DeleteExpense(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	expenseID := c.Param("expenseId")

	err := h.service.DeleteExpense(expenseID, userID)
	if err != nil {
		if err.Error() == "expense not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense not found"})
			return
		}
		if err.Error() == "only pending expenses can be deleted" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Only pending expenses can be deleted"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// CreateExpenseGlobal handles POST /api/expenses
func (h *ExpenseHandler) CreateExpenseGlobal(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.CreateExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// BranchID should be provided in the request body
	if req.BranchID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch ID is required"})
		return
	}

	expense, err := h.service.CreateExpense(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied to this branch"})
			return
		}
		if err.Error() == "vendor not found or does not belong to this merchant" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor"})
			return
		}
		if err.Error() == "account not found or does not belong to this merchant" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, expense)
}

// UpdateExpenseGlobal handles PUT /api/expenses/:id
func (h *ExpenseHandler) UpdateExpenseGlobal(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	expenseID := c.Param("id")
	if expenseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Expense ID is required"})
		return
	}

	var req services.UpdateExpenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	expense, err := h.service.UpdateExpense(expenseID, req, userID)
	if err != nil {
		if err.Error() == "expense not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense not found"})
			return
		}
		if err.Error() == "cannot update amount of an approved expense" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot update amount of an approved expense"})
			return
		}
		if strings.Contains(err.Error(), "not found or does not belong to this merchant") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid vendor or customer"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, expense)
}

// DeleteExpenseGlobal handles DELETE /api/expenses/:id
func (h *ExpenseHandler) DeleteExpenseGlobal(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	expenseID := c.Param("id")
	if expenseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Expense ID is required"})
		return
	}

	err := h.service.DeleteExpense(expenseID, userID)
	if err != nil {
		if err.Error() == "expense not found or access denied" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Expense not found"})
			return
		}
		if err.Error() == "only pending expenses can be deleted" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Only pending expenses can be deleted"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Helper function to check if expense matches search criteria
func matchesExpenseSearch(expense services.ExpenseResponse, search string) bool {
	searchLower := strings.ToLower(search)

	if strings.Contains(strings.ToLower(expense.Description), searchLower) {
		return true
	}
	if expense.Reference != nil && strings.Contains(strings.ToLower(*expense.Reference), searchLower) {
		return true
	}
	if expense.Vendor != nil && strings.Contains(strings.ToLower(expense.Vendor.Name), searchLower) {
		return true
	}

	return false
}
