package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// PurchaseOrderHandler handles purchase order-related HTTP requests
type PurchaseOrderHandler struct {
	purchaseOrderService *services.PurchaseOrderService
}

// NewPurchaseOrderHandler creates a new PurchaseOrderHandler
func NewPurchaseOrderHandler(purchaseOrderService *services.PurchaseOrderService) *PurchaseOrderHandler {
	return &PurchaseOrderHandler{
		purchaseOrderService: purchaseOrderService,
	}
}

// CreatePurchaseOrderRequest represents the request payload for creating a purchase order
type CreatePurchaseOrderRequest struct {
	VendorID        string                           `json:"vendorId" binding:"required"`
	OrderNumber     *string                          `json:"orderNumber,omitempty"`
	OrderDate       time.Time                        `json:"orderDate" binding:"required"`
	ExpectedDate    *time.Time                       `json:"expectedDate,omitempty"`
	Terms           *string                          `json:"terms,omitempty"`
	Notes           *string                          `json:"notes,omitempty"`
	ShippingAddress *string                          `json:"shippingAddress,omitempty"`
	BillingAddress  *string                          `json:"billingAddress,omitempty"`
	Reference       *string                          `json:"reference,omitempty"`
	Currency        *string                          `json:"currency,omitempty"`
	ExchangeRate    *decimal.Decimal                 `json:"exchangeRate,omitempty"`
	Items           []CreatePurchaseOrderItemRequest `json:"items" binding:"required,min=1"`
}

// CreatePurchaseOrderItemRequest represents a purchase order item in the request
type CreatePurchaseOrderItemRequest struct {
	Description     string           `json:"description" binding:"required"`
	Quantity        decimal.Decimal  `json:"quantity" binding:"required"`
	UnitPrice       decimal.Decimal  `json:"unitPrice" binding:"required"`
	TaxRateID       *string          `json:"taxRateId,omitempty"`
	TaxAmount       *decimal.Decimal `json:"taxAmount,omitempty"`
	InventoryItemID *string          `json:"inventoryItemId,omitempty"`
}

// UpdatePurchaseOrderRequest represents the request payload for updating a purchase order
type UpdatePurchaseOrderRequest struct {
	VendorID        *string          `json:"vendorId,omitempty"`
	OrderNumber     *string          `json:"orderNumber,omitempty"`
	OrderDate       *time.Time       `json:"orderDate,omitempty"`
	ExpectedDate    *time.Time       `json:"expectedDate,omitempty"`
	Terms           *string          `json:"terms,omitempty"`
	Notes           *string          `json:"notes,omitempty"`
	ShippingAddress *string          `json:"shippingAddress,omitempty"`
	BillingAddress  *string          `json:"billingAddress,omitempty"`
	Reference       *string          `json:"reference,omitempty"`
	Currency        *string          `json:"currency,omitempty"`
	ExchangeRate    *decimal.Decimal `json:"exchangeRate,omitempty"`
}

// UpdatePurchaseOrderStatusRequest represents the request payload for updating purchase order status
type UpdatePurchaseOrderStatusRequest struct {
	Status models.PurchaseOrderStatus `json:"status" binding:"required"`
}

// ReceivePurchaseOrderItemsRequest represents the request payload for receiving purchase order items
type ReceivePurchaseOrderItemsRequest struct {
	ReceivedItems map[string]decimal.Decimal `json:"receivedItems" binding:"required"`
}

// GetAllPurchaseOrders retrieves all purchase orders with pagination
func (h *PurchaseOrderHandler) GetAllPurchaseOrders(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	purchaseOrders, total, err := h.purchaseOrderService.GetAllPurchaseOrders(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"purchaseOrders": purchaseOrders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetPurchaseOrderByID retrieves a purchase order by ID
func (h *PurchaseOrderHandler) GetPurchaseOrderByID(c *gin.Context) {
	id := c.Param("id")

	purchaseOrder, err := h.purchaseOrderService.GetPurchaseOrderByID(id)
	if err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, purchaseOrder)
}

// GetPurchaseOrdersByMerchant retrieves purchase orders for a specific merchant
func (h *PurchaseOrderHandler) GetPurchaseOrdersByMerchant(c *gin.Context) {
	branchID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	purchaseOrders, total, err := h.purchaseOrderService.GetPurchaseOrdersByMerchant(branchID, page, limit, search, status, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"purchaseOrders": purchaseOrders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetPurchaseOrdersByVendor retrieves purchase orders for a specific vendor
func (h *PurchaseOrderHandler) GetPurchaseOrdersByVendor(c *gin.Context) {
	vendorID := c.Param("vendorId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	purchaseOrders, total, err := h.purchaseOrderService.GetPurchaseOrdersByVendor(vendorID, page, limit, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"purchaseOrders": purchaseOrders,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreatePurchaseOrder creates a new purchase order
func (h *PurchaseOrderHandler) CreatePurchaseOrder(c *gin.Context) {
	branchID := c.Param("merchantId")
	var req CreatePurchaseOrderRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate order number if not provided
	orderNumber := ""
	if req.OrderNumber != nil {
		orderNumber = *req.OrderNumber
	} else {
		generated, err := h.purchaseOrderService.GeneratePurchaseOrderNumber(branchID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate order number: " + err.Error()})
			return
		}
		orderNumber = generated
	}

	// Set defaults
	currency := "USD"
	if req.Currency != nil {
		currency = *req.Currency
	}
	exchangeRate := decimal.NewFromInt(1)
	if req.ExchangeRate != nil {
		exchangeRate = *req.ExchangeRate
	}

	// Create purchase order
	purchaseOrder := &models.PurchaseOrder{
		BranchID:        branchID, // TODO: Update to use branchID
		VendorID:        req.VendorID,
		OrderNumber:     orderNumber,
		Status:          models.PurchaseOrderStatusDraft,
		OrderDate:       req.OrderDate,
		ExpectedDate:    req.ExpectedDate,
		Terms:           req.Terms,
		Notes:           req.Notes,
		ShippingAddress: req.ShippingAddress,
		BillingAddress:  req.BillingAddress,
		Reference:       req.Reference,
		Currency:        currency,
		ExchangeRate:    exchangeRate,
	}

	// Validate purchase order
	if err := h.purchaseOrderService.ValidatePurchaseOrder(purchaseOrder); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create purchase order items
	items := make([]models.PurchaseOrderItem, len(req.Items))
	for i, itemReq := range req.Items {
		taxAmount := decimal.Zero
		if itemReq.TaxAmount != nil {
			taxAmount = *itemReq.TaxAmount
		}

		items[i] = models.PurchaseOrderItem{
			Description:     itemReq.Description,
			Quantity:        itemReq.Quantity,
			UnitPrice:       itemReq.UnitPrice,
			TaxRateID:       itemReq.TaxRateID,
			TaxAmount:       taxAmount,
			InventoryItemID: itemReq.InventoryItemID,
		}

		// Validate item
		if err := h.purchaseOrderService.ValidatePurchaseOrderItem(&items[i]); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Item " + strconv.Itoa(i+1) + ": " + err.Error()})
			return
		}
	}

	// Create the purchase order
	if err := h.purchaseOrderService.CreatePurchaseOrder(purchaseOrder, items); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created purchase order with relationships
	createdPO, err := h.purchaseOrderService.GetPurchaseOrderByID(purchaseOrder.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created purchase order: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdPO)
}

// UpdatePurchaseOrder updates an existing purchase order
func (h *PurchaseOrderHandler) UpdatePurchaseOrder(c *gin.Context) {
	id := c.Param("id")
	var req UpdatePurchaseOrderRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.PurchaseOrder{}
	if req.VendorID != nil {
		updates.VendorID = *req.VendorID
	}
	if req.OrderNumber != nil {
		updates.OrderNumber = *req.OrderNumber
	}
	if req.OrderDate != nil {
		updates.OrderDate = *req.OrderDate
	}
	if req.ExpectedDate != nil {
		updates.ExpectedDate = req.ExpectedDate
	}
	if req.Terms != nil {
		updates.Terms = req.Terms
	}
	if req.Notes != nil {
		updates.Notes = req.Notes
	}
	if req.ShippingAddress != nil {
		updates.ShippingAddress = req.ShippingAddress
	}
	if req.BillingAddress != nil {
		updates.BillingAddress = req.BillingAddress
	}
	if req.Reference != nil {
		updates.Reference = req.Reference
	}
	if req.Currency != nil {
		updates.Currency = *req.Currency
	}
	if req.ExchangeRate != nil {
		updates.ExchangeRate = *req.ExchangeRate
	}

	updatedPO, err := h.purchaseOrderService.UpdatePurchaseOrder(id, updates)
	if err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedPO)
}

// UpdatePurchaseOrderStatus updates the status of a purchase order
func (h *PurchaseOrderHandler) UpdatePurchaseOrderStatus(c *gin.Context) {
	id := c.Param("id")
	var req UpdatePurchaseOrderStatusRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedPO, err := h.purchaseOrderService.UpdatePurchaseOrderStatus(id, req.Status)
	if err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedPO)
}

// DeletePurchaseOrder deletes a purchase order
func (h *PurchaseOrderHandler) DeletePurchaseOrder(c *gin.Context) {
	id := c.Param("id")

	if err := h.purchaseOrderService.DeletePurchaseOrder(id); err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Purchase order deleted successfully"})
}

// UpdatePurchaseOrderItems updates the items for a purchase order
func (h *PurchaseOrderHandler) UpdatePurchaseOrderItems(c *gin.Context) {
	purchaseOrderID := c.Param("id")
	var req []CreatePurchaseOrderItemRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert request items to model items
	items := make([]models.PurchaseOrderItem, len(req))
	for i, itemReq := range req {
		taxAmount := decimal.Zero
		if itemReq.TaxAmount != nil {
			taxAmount = *itemReq.TaxAmount
		}

		items[i] = models.PurchaseOrderItem{
			Description:     itemReq.Description,
			Quantity:        itemReq.Quantity,
			UnitPrice:       itemReq.UnitPrice,
			TaxRateID:       itemReq.TaxRateID,
			TaxAmount:       taxAmount,
			InventoryItemID: itemReq.InventoryItemID,
		}

		// Validate item
		if err := h.purchaseOrderService.ValidatePurchaseOrderItem(&items[i]); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Item " + strconv.Itoa(i+1) + ": " + err.Error()})
			return
		}
	}

	if err := h.purchaseOrderService.UpdatePurchaseOrderItems(purchaseOrderID, items); err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Purchase order items updated successfully"})
}

// ReceivePurchaseOrderItems marks items as received and updates inventory
func (h *PurchaseOrderHandler) ReceivePurchaseOrderItems(c *gin.Context) {
	purchaseOrderID := c.Param("id")
	var req ReceivePurchaseOrderItemsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.purchaseOrderService.ReceivePurchaseOrderItems(purchaseOrderID, req.ReceivedItems); err != nil {
		if err.Error() == "purchase order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Purchase order items received successfully"})
}

// GeneratePurchaseOrderNumber generates a unique purchase order number
func (h *PurchaseOrderHandler) GeneratePurchaseOrderNumber(c *gin.Context) {
	branchID := c.Param("merchantId")

	orderNumber, err := h.purchaseOrderService.GeneratePurchaseOrderNumber(branchID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"orderNumber": orderNumber})
}

// GetPurchaseOrderSummary gets summary statistics for purchase orders
func (h *PurchaseOrderHandler) GetPurchaseOrderSummary(c *gin.Context) {
	branchID := c.Param("merchantId")

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		}
	}

	summary, err := h.purchaseOrderService.GetPurchaseOrderSummary(branchID, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}
