package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
)

type JournalEntryHandler struct {
	journalEntryService *services.JournalEntryService
}

func NewJournalEntryHandler(journalEntryService *services.JournalEntryService) *JournalEntryHandler {
	return &JournalEntryHandler{
		journalEntryService: journalEntryService,
	}
}

// GetJournalEntries godoc
// @Summary Get all journal entries
// @Description Get all journal entries with pagination
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.JournalEntryResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /journal-entries [get]
func (h *JournalEntryHandler) GetJournalEntries(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	entries, total, err := h.journalEntryService.GetAllJournalEntries(page, limit, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       entries,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetJournalEntriesByMerchant godoc
// @Summary Get journal entries by merchant
// @Description Get journal entries for a specific merchant with pagination
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} PaginatedResponse{data=[]services.JournalEntryResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /merchants/{merchantId}/journal-entries [get]
func (h *JournalEntryHandler) GetJournalEntriesByMerchant(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	branchID := c.Param("merchantId")
	if branchID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Merchant ID is required"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	entries, total, err := h.journalEntryService.GetJournalEntriesByMerchant(branchID, page, limit, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Data:       entries,
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: (total + int64(limit) - 1) / int64(limit),
	})
}

// GetJournalEntry godoc
// @Summary Get journal entry by ID
// @Description Get a specific journal entry by ID
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param id path string true "Journal Entry ID"
// @Success 200 {object} services.JournalEntryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /journal-entries/{id} [get]
func (h *JournalEntryHandler) GetJournalEntry(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Journal Entry ID is required"})
		return
	}

	entry, err := h.journalEntryService.GetJournalEntryByID(id, userID)
	if err != nil {
		if err.Error() == "journal entry not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, entry)
}

// CreateJournalEntry godoc
// @Summary Create a new journal entry
// @Description Create a new journal entry with double-entry bookkeeping validation
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param journalEntry body services.CreateJournalEntryRequest true "Journal Entry data"
// @Success 201 {object} services.JournalEntryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /journal-entries [post]
func (h *JournalEntryHandler) CreateJournalEntry(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req services.CreateJournalEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	entry, err := h.journalEntryService.CreateJournalEntry(req, userID)
	if err != nil {
		if err.Error() == "access denied to this merchant" {
			c.JSON(http.StatusForbidden, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, entry)
}

// UpdateJournalEntry godoc
// @Summary Update a journal entry
// @Description Update an existing journal entry
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param id path string true "Journal Entry ID"
// @Param journalEntry body services.UpdateJournalEntryRequest true "Journal Entry data"
// @Success 200 {object} services.JournalEntryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /journal-entries/{id} [put]
func (h *JournalEntryHandler) UpdateJournalEntry(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Journal Entry ID is required"})
		return
	}

	var req services.UpdateJournalEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	entry, err := h.journalEntryService.UpdateJournalEntry(id, req, userID)
	if err != nil {
		if err.Error() == "journal entry not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, entry)
}

// DeleteJournalEntry godoc
// @Summary Delete a journal entry
// @Description Delete a journal entry and all its lines
// @Tags journal-entries
// @Accept json
// @Produce json
// @Param id path string true "Journal Entry ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Security BearerAuth
// @Router /journal-entries/{id} [delete]
func (h *JournalEntryHandler) DeleteJournalEntry(c *gin.Context) {
	userID := c.GetString("userID")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Journal Entry ID is required"})
		return
	}

	err := h.journalEntryService.DeleteJournalEntry(id, userID)
	if err != nil {
		if err.Error() == "journal entry not found or access denied" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetAllJournalEntries is an alias for GetJournalEntries for route compatibility
func (h *JournalEntryHandler) GetAllJournalEntries(c *gin.Context) {
	h.GetJournalEntries(c)
}

// GetJournalEntryByID is an alias for GetJournalEntry for route compatibility
func (h *JournalEntryHandler) GetJournalEntryByID(c *gin.Context) {
	h.GetJournalEntry(c)
}
