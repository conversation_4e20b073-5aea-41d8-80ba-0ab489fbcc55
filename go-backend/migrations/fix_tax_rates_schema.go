package main

import (
	"fmt"
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"

	"gorm.io/gorm"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Run the migration
	FixTaxRatesSchema(db)
}

// FixTaxRatesSchema migrates tax_rates table from merchant_id to branch_id
func FixTaxRatesSchema(db *gorm.DB) {
	fmt.Println("Starting tax_rates schema migration...")

	// Check if merchant_id column exists
	var merchantIdExists bool
	err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tax_rates' AND column_name = 'merchant_id')").Scan(&merchantIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check merchant_id column: %v", err)
	}

	// Check if branch_id column exists
	var branchIdExists bool
	err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tax_rates' AND column_name = 'branch_id')").Scan(&branchIdExists).Error
	if err != nil {
		log.Fatalf("Failed to check branch_id column: %v", err)
	}

	if merchantIdExists && !branchIdExists {
		fmt.Println("Migrating tax_rates from merchant_id to branch_id...")

		// Add branch_id column
		if err := db.Exec("ALTER TABLE tax_rates ADD COLUMN branch_id VARCHAR(36)").Error; err != nil {
			log.Fatalf("Failed to add branch_id column: %v", err)
		}

		// Copy data from merchant_id to branch_id (assuming 1:1 mapping for now)
		if err := db.Exec("UPDATE tax_rates SET branch_id = merchant_id").Error; err != nil {
			log.Fatalf("Failed to copy merchant_id to branch_id: %v", err)
		}

		// Make branch_id NOT NULL
		if err := db.Exec("ALTER TABLE tax_rates ALTER COLUMN branch_id SET NOT NULL").Error; err != nil {
			log.Fatalf("Failed to make branch_id NOT NULL: %v", err)
		}

		// Add index for branch_id
		if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_tax_rates_branch_id ON tax_rates(branch_id)").Error; err != nil {
			log.Fatalf("Failed to create branch_id index: %v", err)
		}

		// Drop foreign key constraint for merchant_id
		if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS fk_tax_rates_merchant").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant foreign key constraint: %v", err)
		}

		// Drop unique constraint for merchant_id
		if err := db.Exec("ALTER TABLE tax_rates DROP CONSTRAINT IF EXISTS uk_tax_rates_merchant_name").Error; err != nil {
			log.Printf("Warning: Failed to drop merchant unique constraint: %v", err)
		}

		// Drop merchant_id column
		if err := db.Exec("ALTER TABLE tax_rates DROP COLUMN merchant_id").Error; err != nil {
			log.Fatalf("Failed to drop merchant_id column: %v", err)
		}

		// Add foreign key constraint for branch_id
		if err := db.Exec("ALTER TABLE tax_rates ADD CONSTRAINT fk_tax_rates_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE").Error; err != nil {
			log.Printf("Warning: Failed to add branch foreign key constraint: %v", err)
		}

		// Add unique constraint for branch_id and name
		if err := db.Exec("ALTER TABLE tax_rates ADD CONSTRAINT uk_tax_rates_branch_name UNIQUE (branch_id, name)").Error; err != nil {
			log.Printf("Warning: Failed to add branch unique constraint: %v", err)
		}

		// Add is_default column if it doesn't exist
		var isDefaultExists bool
		err = db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tax_rates' AND column_name = 'is_default')").Scan(&isDefaultExists).Error
		if err != nil {
			log.Printf("Warning: Failed to check is_default column: %v", err)
		}

		if !isDefaultExists {
			if err := db.Exec("ALTER TABLE tax_rates ADD COLUMN is_default BOOLEAN DEFAULT false").Error; err != nil {
				log.Printf("Warning: Failed to add is_default column: %v", err)
			}
		}

		fmt.Println("Successfully migrated tax_rates table to use branch_id")
	} else if branchIdExists {
		fmt.Println("tax_rates table already uses branch_id")
	} else {
		fmt.Println("tax_rates table structure is unexpected")
	}

	fmt.Println("Tax rates migration completed successfully!")
}
