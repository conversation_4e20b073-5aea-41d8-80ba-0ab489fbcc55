'use client';

import React, { useState } from 'react';
import {
  useGetTaxRatesQuery,
  useDeleteTaxRateMutation
} from '@/redux/services/taxRatesApi';
import { TaxRateFormDialog } from './_components';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { TaxRate } from '@/lib/types';

export default function TaxRatesPage() {
  const { data: taxRates, isLoading, error } = useGetTaxRatesQuery();
  const [deleteTaxRate, { isLoading: isDeleting }] = useDeleteTaxRateMutation();
  const { toast } = useToast();

  // Ensure taxRates is always an array
  const taxRatesArray = Array.isArray(taxRates) ? taxRates : [];

  // Modal state
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTaxRate, setSelectedTaxRate] = useState<TaxRate | null>(null);

  const handleEdit = (taxRate: TaxRate) => {
    setSelectedTaxRate(taxRate);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete the tax rate "${name}"?`)) {
      try {
        await deleteTaxRate(id).unwrap();
        toast({
          title: 'Success',
          description: 'Tax rate deleted successfully.',
        });
      } catch (error: any) {
        console.error('Failed to delete tax rate:', error);
        const errorMessage = error?.data?.message || error?.message || 'Failed to delete tax rate';
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    }
  };

  const handleModalSuccess = () => {
    // Refresh will happen automatically due to RTK Query cache invalidation
    toast({
      title: 'Success',
      description: 'Tax rates updated successfully.',
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading tax rates...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Error loading tax rates. Please try again.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Tax Rates</h1>
          <p className="text-muted-foreground">
            Manage tax rates for your business transactions
          </p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Tax Rate
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Tax Rates</CardTitle>
          <CardDescription>
            {taxRatesArray.length} tax rate{taxRatesArray.length !== 1 ? 's' : ''} configured
          </CardDescription>
        </CardHeader>
        <CardContent>
          {taxRatesArray.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium">Name</th>
                    <th className="text-left py-3 px-4 font-medium">Rate</th>
                    <th className="text-left py-3 px-4 font-medium">Description</th>
                    <th className="text-left py-3 px-4 font-medium">Status</th>
                    <th className="text-right py-3 px-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {taxRatesArray.map((taxRate) => (
                    <tr key={taxRate.id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4 font-medium">{taxRate.name}</td>
                      <td className="py-3 px-4">
                        <Badge variant="secondary">
                          {(Number(taxRate.rate) * 100).toFixed(2)}%
                        </Badge>
                      </td>
                      <td className="py-3 px-4 text-muted-foreground">
                        {taxRate.description || '-'}
                      </td>
                      <td className="py-3 px-4">
                        <Badge variant={taxRate.is_active ? "default" : "secondary"}>
                          {taxRate.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(taxRate)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(taxRate.id, taxRate.name)}
                            disabled={isDeleting}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-muted-foreground mb-4">
                No tax rates configured yet.
              </div>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Tax Rate
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Tax Rate Modal */}
      <TaxRateFormDialog
        isOpen={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
        onSuccess={handleModalSuccess}
      />

      {/* Edit Tax Rate Modal */}
      <TaxRateFormDialog
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        taxRate={selectedTaxRate}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
}
