'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useGetTaxRatesQuery } from '@/redux/services/taxRatesApi';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Calculator,
  ArrowRight,
  ArrowLeft,
  Plus,
  Trash2,
  FileText,
  Receipt,
  Save,
  Copy,
  Check,
  History
} from "lucide-react";
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { useToast } from "@/components/ui/use-toast";

// Calculation types
enum CalculationType {
  AddTax = 'add-tax',
  RemoveTax = 'remove-tax',
  CustomRate = 'custom-rate',
  MultipleItems = 'multiple-items',
}

// Interface for line items in multiple items calculation
interface LineItem {
  id: string;
  description: string;
  amount: number;
  taxRateId: string;
  taxRate: number;
  taxAmount: number;
  total: number;
}

// Interface for saved calculations
interface SavedCalculation {
  id: string;
  date: string;
  type: CalculationType;
  subtotal: number;
  taxAmount: number;
  total: number;
  effectiveRate: number;
  items?: LineItem[];
  notes?: string;
}

export default function TaxCalculatorPage() {
  const router = useRouter();
  const { toast } = useToast();

  // Fetch tax rates
  const { data: taxRates, isLoading: isLoadingTaxRates } = useGetTaxRatesQuery();

  // Ensure taxRates is always an array
  const taxRatesArray = Array.isArray(taxRates) ? taxRates : [];

  // State for calculator
  const [calculationType, setCalculationType] = useState<CalculationType>(CalculationType.AddTax);
  const [selectedTaxRateId, setSelectedTaxRateId] = useState<string>('');
  const [customRate, setCustomRate] = useState<number>(0);
  const [amount, setAmount] = useState<number>(0);
  const [results, setResults] = useState<{
    subtotal: number;
    taxAmount: number;
    total: number;
    effectiveRate: number;
    items?: LineItem[];
  } | null>(null);

  // State for multiple items
  const [lineItems, setLineItems] = useState<LineItem[]>([]);
  const [newItemDescription, setNewItemDescription] = useState<string>('');
  const [newItemAmount, setNewItemAmount] = useState<number>(0);
  const [newItemTaxRateId, setNewItemTaxRateId] = useState<string>('');

  // State for saved calculations
  const [savedCalculations, setSavedCalculations] = useState<SavedCalculation[]>([]);
  const [showSavedCalculations, setShowSavedCalculations] = useState<boolean>(false);
  const [calculationNotes, setCalculationNotes] = useState<string>('');
  const [showSaveDialog, setShowSaveDialog] = useState<boolean>(false);

  // State for action feedback
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [saveSuccess, setSaveSuccess] = useState<boolean>(false);
  const [invoiceRedirect, setInvoiceRedirect] = useState<boolean>(false);

  // Load saved calculations from localStorage on component mount
  useEffect(() => {
    const savedCalcs = localStorage.getItem('taxCalculations');
    if (savedCalcs) {
      try {
        setSavedCalculations(JSON.parse(savedCalcs));
      } catch (error) {
        console.error('Failed to parse saved calculations:', error);
      }
    }
  }, []);

  // Set first tax rate as default when data loads
  useEffect(() => {
    if (taxRatesArray.length > 0) {
      if (!selectedTaxRateId) {
        setSelectedTaxRateId(taxRatesArray[0].id);
      }
      if (!newItemTaxRateId) {
        setNewItemTaxRateId(taxRatesArray[0].id);
      }
    }
  }, [taxRatesArray, selectedTaxRateId, newItemTaxRateId]);

  // Get the selected tax rate
  const getSelectedTaxRate = () => {
    if (calculationType === CalculationType.CustomRate) {
      return customRate;
    }

    if (taxRatesArray.length === 0) return 0;

    const selectedRate = taxRatesArray.find(rate => rate.id === selectedTaxRateId);
    return selectedRate ? Number(selectedRate.rate) : 0;
  };

  // Add a line item
  const addLineItem = () => {
    if (!newItemDescription || newItemAmount <= 0 || !newItemTaxRateId) {
      return;
    }

    const selectedRate = taxRatesArray.find(rate => rate.id === newItemTaxRateId);
    const taxRate = selectedRate ? Number(selectedRate.rate) : 0;
    const taxAmount = newItemAmount * taxRate;
    const total = newItemAmount + taxAmount;

    const newItem: LineItem = {
      id: Date.now().toString(),
      description: newItemDescription,
      amount: newItemAmount,
      taxRateId: newItemTaxRateId,
      taxRate: taxRate,
      taxAmount: taxAmount,
      total: total
    };

    setLineItems([...lineItems, newItem]);

    // Reset form
    setNewItemDescription('');
    setNewItemAmount(0);
  };

  // Remove a line item
  const removeLineItem = (id: string) => {
    setLineItems(lineItems.filter(item => item.id !== id));
  };

  // Calculate taxes
  const calculateTaxes = () => {
    if (calculationType === CalculationType.MultipleItems) {
      if (lineItems.length === 0) {
        return;
      }

      const subtotal = lineItems.reduce((sum, item) => sum + item.amount, 0);
      const taxAmount = lineItems.reduce((sum, item) => sum + item.taxAmount, 0);
      const total = lineItems.reduce((sum, item) => sum + item.total, 0);

      // Calculate effective tax rate
      const effectiveRate = subtotal > 0 ? taxAmount / subtotal : 0;

      setResults({
        subtotal,
        taxAmount,
        total,
        effectiveRate,
        items: lineItems
      });
    } else {
      const rate = getSelectedTaxRate();

      if (calculationType === CalculationType.AddTax) {
        // Add tax to amount (amount is subtotal)
        const subtotal = amount;
        const taxAmount = subtotal * rate;
        const total = subtotal + taxAmount;

        setResults({
          subtotal,
          taxAmount,
          total,
          effectiveRate: rate,
        });
      } else if (calculationType === CalculationType.RemoveTax) {
        // Remove tax from amount (amount is total)
        const total = amount;
        const subtotal = total / (1 + rate);
        const taxAmount = total - subtotal;

        setResults({
          subtotal,
          taxAmount,
          total,
          effectiveRate: rate,
        });
      }
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    calculateTaxes();
  };

  // Copy results to clipboard
  const handleCopyResults = async () => {
    if (!results) return;

    try {
      let textToCopy = `Tax Calculation Results\n`;
      textToCopy += `Subtotal: ${formatCurrency(results.subtotal)}\n`;
      textToCopy += `Tax (${formatPercentage(results.effectiveRate)}): ${formatCurrency(results.taxAmount)}\n`;
      textToCopy += `Total: ${formatCurrency(results.total)}\n\n`;

      if (calculationType === CalculationType.MultipleItems && results.items) {
        textToCopy += `Line Items:\n`;
        results.items.forEach((item, index) => {
          textToCopy += `${index + 1}. ${item.description}: ${formatCurrency(item.amount)} + ${formatCurrency(item.taxAmount)} tax = ${formatCurrency(item.total)}\n`;
        });
      }

      await navigator.clipboard.writeText(textToCopy);

      // Show success feedback
      setCopySuccess(true);
      toast({
        title: "Copied to clipboard",
        description: "Tax calculation results have been copied to your clipboard.",
        duration: 3000,
      });

      // Reset success state after a delay
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast({
        title: "Copy failed",
        description: "Could not copy to clipboard. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Save calculation
  const handleSaveCalculation = () => {
    if (!results) return;

    const newSavedCalculation: SavedCalculation = {
      id: Date.now().toString(),
      date: new Date().toISOString(),
      type: calculationType,
      subtotal: results.subtotal,
      taxAmount: results.taxAmount,
      total: results.total,
      effectiveRate: results.effectiveRate,
      notes: calculationNotes,
    };

    if (calculationType === CalculationType.MultipleItems && results.items) {
      newSavedCalculation.items = [...results.items];
    }

    const updatedCalculations = [newSavedCalculation, ...savedCalculations];
    setSavedCalculations(updatedCalculations);

    // Save to localStorage
    localStorage.setItem('taxCalculations', JSON.stringify(updatedCalculations));

    // Show success feedback
    setSaveSuccess(true);
    setShowSaveDialog(false);
    setCalculationNotes('');

    toast({
      title: "Calculation saved",
      description: "Your tax calculation has been saved for future reference.",
      duration: 3000,
    });

    // Reset success state after a delay
    setTimeout(() => setSaveSuccess(false), 2000);
  };

  // Create invoice from calculation
  const handleCreateInvoice = () => {
    if (!results) return;

    // Store calculation in localStorage to be used in invoice creation
    localStorage.setItem('invoiceFromCalculation', JSON.stringify({
      subtotal: results.subtotal,
      taxAmount: results.taxAmount,
      total: results.total,
      effectiveRate: results.effectiveRate,
      items: calculationType === CalculationType.MultipleItems ? results.items : undefined,
      date: new Date().toISOString()
    }));

    // Show feedback before redirecting
    setInvoiceRedirect(true);

    toast({
      title: "Creating invoice",
      description: "Redirecting to invoice creation page...",
      duration: 2000,
    });

    // Redirect to invoice creation page after a short delay
    setTimeout(() => {
      router.push('/invoices/new');
    }, 1000);
  };

  // Load a saved calculation
  const handleLoadCalculation = (calculation: SavedCalculation) => {
    setCalculationType(calculation.type);

    if (calculation.type === CalculationType.MultipleItems && calculation.items) {
      setLineItems(calculation.items);
    } else {
      // For single item calculations, set the amount based on the calculation type
      if (calculation.type === CalculationType.AddTax) {
        setAmount(calculation.subtotal);
      } else if (calculation.type === CalculationType.RemoveTax) {
        setAmount(calculation.total);
      }

      // Try to find a matching tax rate
      if (taxRatesArray.length > 0) {
        const matchingRate = taxRatesArray.find(rate =>
          Math.abs(Number(rate.rate) - calculation.effectiveRate) < 0.0001
        );

        if (matchingRate) {
          setSelectedTaxRateId(matchingRate.id);
        } else {
          // If no matching rate, set as custom rate
          setCustomRate(calculation.effectiveRate);
          setCalculationType(CalculationType.CustomRate);
        }
      }
    }

    // Calculate taxes to show results
    setTimeout(() => calculateTaxes(), 100);

    // Hide saved calculations panel
    setShowSavedCalculations(false);

    toast({
      title: "Calculation loaded",
      description: "The saved calculation has been loaded.",
      duration: 3000,
    });
  };

  // Delete a saved calculation
  const handleDeleteCalculation = (id: string) => {
    const updatedCalculations = savedCalculations.filter(calc => calc.id !== id);
    setSavedCalculations(updatedCalculations);

    // Update localStorage
    localStorage.setItem('taxCalculations', JSON.stringify(updatedCalculations));

    toast({
      title: "Calculation deleted",
      description: "The saved calculation has been deleted.",
      duration: 3000,
    });
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Tax Calculator</h1>
      </div>

      {showSavedCalculations && savedCalculations.length > 0 && (
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle>Saved Calculations</CardTitle>
              <CardDescription>
                Your previously saved tax calculations
              </CardDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSavedCalculations(false)}
            >
              Close
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savedCalculations.map((calc) => (
                <div key={calc.id} className="border rounded-md p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-medium flex items-center">
                        {calc.type === CalculationType.AddTax && "Add Tax"}
                        {calc.type === CalculationType.RemoveTax && "Remove Tax"}
                        {calc.type === CalculationType.MultipleItems && "Multiple Items"}
                        {calc.type === CalculationType.CustomRate && "Custom Rate"}
                        <Badge variant="outline" className="ml-2">
                          {formatCurrency(calc.total)}
                        </Badge>
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(calc.date).toLocaleString()}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleLoadCalculation(calc)}
                      >
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteCalculation(calc.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Subtotal:</span>
                      <div>{formatCurrency(calc.subtotal)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Tax ({formatPercentage(calc.effectiveRate)}):</span>
                      <div>{formatCurrency(calc.taxAmount)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total:</span>
                      <div className="font-medium">{formatCurrency(calc.total)}</div>
                    </div>
                  </div>

                  {calc.notes && (
                    <div className="mt-2 text-sm">
                      <span className="text-muted-foreground">Notes:</span>
                      <p className="mt-1 italic">{calc.notes}</p>
                    </div>
                  )}

                  {calc.type === CalculationType.MultipleItems && calc.items && calc.items.length > 0 && (
                    <div className="mt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs"
                        onClick={() => handleLoadCalculation(calc)}
                      >
                        {calc.items.length} items - Click to load
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Calculate Taxes</CardTitle>
            <CardDescription>
              Calculate taxes for different scenarios
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="calculation-type">Calculation Type</Label>
                  <Tabs
                    defaultValue={calculationType}
                    value={calculationType}
                    onValueChange={(value) => {
                      setCalculationType(value as CalculationType);
                      // Reset results when changing calculation type
                      setResults(null);
                    }}
                    className="w-full mt-2"
                  >
                    <TabsList className="grid grid-cols-3 w-full">
                      <TabsTrigger value={CalculationType.AddTax}>
                        <ArrowRight className="mr-2 h-4 w-4" />
                        Add Tax
                      </TabsTrigger>
                      <TabsTrigger value={CalculationType.RemoveTax}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Remove Tax
                      </TabsTrigger>
                      <TabsTrigger value={CalculationType.MultipleItems}>
                        <Plus className="mr-2 h-4 w-4" />
                        Multiple Items
                      </TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <div>
                  <Label htmlFor="tax-rate">Tax Rate</Label>
                  <Tabs
                    defaultValue="existing-rate"
                    className="w-full mt-2"
                    onValueChange={(value) => {
                      if (value === 'custom-rate') {
                        setCalculationType(CalculationType.CustomRate);
                      } else {
                        setCalculationType(calculationType === CalculationType.CustomRate
                          ? CalculationType.AddTax
                          : calculationType);
                      }
                    }}
                  >
                    <TabsList className="grid grid-cols-2 w-full">
                      <TabsTrigger value="existing-rate">Existing Rate</TabsTrigger>
                      <TabsTrigger value="custom-rate">Custom Rate</TabsTrigger>
                    </TabsList>
                    <TabsContent value="existing-rate" className="mt-4">
                      {isLoadingTaxRates ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <Select
                          value={selectedTaxRateId}
                          onValueChange={setSelectedTaxRateId}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a tax rate" />
                          </SelectTrigger>
                          <SelectContent>
                            {taxRatesArray.map((rate) => (
                              <SelectItem key={rate.id} value={rate.id}>
                                {rate.name} ({formatPercentage(Number(rate.rate))})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </TabsContent>
                    <TabsContent value="custom-rate" className="mt-4">
                      <div className="flex items-center space-x-2">
                        <Input
                          type="number"
                          step="0.0001"
                          min="0"
                          max="1"
                          value={customRate}
                          onChange={(e) => setCustomRate(parseFloat(e.target.value) || 0)}
                          className="flex-1"
                        />
                        <span className="text-sm text-muted-foreground">
                          {formatPercentage(customRate)}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Enter a value between 0 and 1 (e.g., 0.08 for 8%)
                      </p>
                    </TabsContent>
                  </Tabs>
                </div>

                {calculationType !== CalculationType.MultipleItems && (
                  <div>
                    <Label htmlFor="amount">
                      {calculationType === CalculationType.AddTax ? 'Subtotal Amount' : 'Total Amount'}
                    </Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-sm">$</span>
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        min="0"
                        value={amount}
                        onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                )}

                {calculationType === CalculationType.MultipleItems && (
                  <div className="space-y-4">
                    <div className="bg-muted p-4 rounded-md">
                      <h3 className="font-medium mb-2">Add Line Item</h3>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="item-description">Description</Label>
                          <Input
                            id="item-description"
                            value={newItemDescription}
                            onChange={(e) => setNewItemDescription(e.target.value)}
                            className="mt-1"
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Label htmlFor="item-amount">Amount</Label>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-sm">$</span>
                              <Input
                                id="item-amount"
                                type="number"
                                step="0.01"
                                min="0"
                                value={newItemAmount}
                                onChange={(e) => setNewItemAmount(parseFloat(e.target.value) || 0)}
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="item-tax-rate">Tax Rate</Label>
                            {isLoadingTaxRates ? (
                              <Skeleton className="h-10 w-full mt-1" />
                            ) : (
                              <Select
                                value={newItemTaxRateId}
                                onValueChange={setNewItemTaxRateId}
                              >
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Select a tax rate" />
                                </SelectTrigger>
                                <SelectContent>
                                  {taxRatesArray.map((rate) => (
                                    <SelectItem key={rate.id} value={rate.id}>
                                      {rate.name} ({formatPercentage(Number(rate.rate))})
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                          </div>
                        </div>

                        <Button
                          type="button"
                          onClick={addLineItem}
                          className="w-full"
                          disabled={!newItemDescription || newItemAmount <= 0 || !newItemTaxRateId}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add Item
                        </Button>
                      </div>
                    </div>

                    {lineItems.length > 0 && (
                      <div>
                        <h3 className="font-medium mb-2">Line Items</h3>
                        <div className="border rounded-md overflow-hidden">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Description</TableHead>
                                <TableHead className="text-right">Amount</TableHead>
                                <TableHead className="text-right">Tax</TableHead>
                                <TableHead className="text-right">Total</TableHead>
                                <TableHead className="w-[50px]"></TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {lineItems.map((item) => (
                                <TableRow key={item.id}>
                                  <TableCell>{item.description}</TableCell>
                                  <TableCell className="text-right">{formatCurrency(item.amount)}</TableCell>
                                  <TableCell className="text-right">
                                    {formatCurrency(item.taxAmount)}
                                    <div className="text-xs text-muted-foreground">
                                      {formatPercentage(item.taxRate)}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-right">{formatCurrency(item.total)}</TableCell>
                                  <TableCell>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => removeLineItem(item.id)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={
                  (calculationType === CalculationType.MultipleItems && lineItems.length === 0) ||
                  (calculationType !== CalculationType.MultipleItems && amount <= 0)
                }
              >
                <Calculator className="mr-2 h-4 w-4" />
                Calculate
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Results</CardTitle>
            <CardDescription>
              Tax calculation results
            </CardDescription>
          </CardHeader>
          <CardContent>
            {results ? (
              <div className="space-y-6">
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Subtotal</TableCell>
                      <TableCell className="text-right">{formatCurrency(results.subtotal)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">
                        Tax {calculationType === CalculationType.MultipleItems
                          ? `(${formatPercentage(results.effectiveRate)} effective)`
                          : `(${formatPercentage(results.effectiveRate)})`}
                      </TableCell>
                      <TableCell className="text-right">{formatCurrency(results.taxAmount)}</TableCell>
                    </TableRow>
                    <TableRow className="font-bold">
                      <TableCell>Total</TableCell>
                      <TableCell className="text-right">{formatCurrency(results.total)}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>

                {calculationType === CalculationType.MultipleItems && results.items && (
                  <div className="bg-muted p-4 rounded-md">
                    <h3 className="font-medium mb-2">Tax Breakdown by Item</h3>
                    <div className="space-y-2">
                      {results.items.map((item) => (
                        <div key={item.id} className="flex justify-between items-center text-sm">
                          <div className="flex-1">{item.description}</div>
                          <div className="flex space-x-4">
                            <div className="w-24 text-right">{formatCurrency(item.amount)}</div>
                            <div className="w-24 text-right">
                              {formatCurrency(item.taxAmount)}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({formatPercentage(item.taxRate)})
                              </span>
                            </div>
                            <div className="w-24 text-right font-medium">{formatCurrency(item.total)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {calculationType !== CalculationType.MultipleItems && (
                  <div className="bg-muted p-4 rounded-md">
                    <h3 className="font-medium mb-2">Calculation Details</h3>
                    {calculationType === CalculationType.AddTax ? (
                      <p className="text-sm">
                        Tax amount ({formatCurrency(results.taxAmount)}) =
                        Subtotal ({formatCurrency(results.subtotal)}) ×
                        Rate ({formatPercentage(results.effectiveRate)})
                      </p>
                    ) : (
                      <p className="text-sm">
                        Subtotal ({formatCurrency(results.subtotal)}) =
                        Total ({formatCurrency(results.total)}) ÷
                        (1 + Rate ({formatPercentage(results.effectiveRate)}))
                      </p>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <Calculator className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Enter values and click Calculate to see results
                </p>
              </div>
            )}
          </CardContent>
          {results && (
            <CardFooter className="flex justify-between gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={handleCopyResults}
                      disabled={copySuccess}
                    >
                      {copySuccess ? (
                        <><Check className="mr-2 h-4 w-4 text-green-500" />Copied</>
                      ) : (
                        <><Copy className="mr-2 h-4 w-4" />Copy Results</>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Copy calculation results to clipboard</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={handleCreateInvoice}
                      disabled={invoiceRedirect}
                    >
                      {invoiceRedirect ? (
                        <><Check className="mr-2 h-4 w-4 text-green-500" />Redirecting...</>
                      ) : (
                        <><Receipt className="mr-2 h-4 w-4" />Create Invoice</>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Create a new invoice with these values</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <AlertDialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="flex-1"
                    disabled={saveSuccess}
                  >
                    {saveSuccess ? (
                      <><Check className="mr-2 h-4 w-4 text-green-500" />Saved</>
                    ) : (
                      <><Save className="mr-2 h-4 w-4" />Save Calculation</>
                    )}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Save Calculation</AlertDialogTitle>
                    <AlertDialogDescription>
                      Add optional notes to this calculation before saving.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="py-4">
                    <Label htmlFor="calculation-notes">Notes (optional)</Label>
                    <Input
                      id="calculation-notes"
                      value={calculationNotes}
                      onChange={(e) => setCalculationNotes(e.target.value)}
                      placeholder="Enter any notes about this calculation"
                      className="mt-2"
                    />
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleSaveCalculation}>Save</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => setShowSavedCalculations(!showSavedCalculations)}
                    >
                      <History className="mr-2 h-4 w-4" />
                      History
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View saved calculation history</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  );
}
