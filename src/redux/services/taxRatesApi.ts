import { createApi } from '@reduxjs/toolkit/query/react';
import { authenticatedBaseQuery } from '../baseQuery';
import { TaxRate } from '@/lib/types';

// Define the response type for paginated tax rates (matches Go backend response)
export interface TaxRatesResponse {
  data: TaxRate[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const taxRatesApi = createApi({
  reducerPath: 'taxRatesApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['TaxRate'],
  endpoints: (builder) => ({
    // Get all tax rates
    getTaxRates: builder.query<TaxRate[], void>({
      query: () => 'taxes/rates',
      transformResponse: (response: any) => {
        console.log('Tax Rates API raw response:', response);

        // Handle different response formats
        if (response.data && Array.isArray(response.data)) {
          // New standardized format with data array and pagination
          return response.data;
        } else if (response.taxRates && Array.isArray(response.taxRates)) {
          // Legacy format with taxRates array
          return response.taxRates;
        } else if (Array.isArray(response)) {
          // If the API returns just an array of tax rates
          return response;
        } else {
          // Fallback for unexpected format
          console.error('Unexpected tax rates API response format:', response);
          return [];
        }
      },
      providesTags: ['TaxRate'],
    }),

    // Get a specific tax rate by ID
    getTaxRateById: builder.query<TaxRate, string>({
      query: (id) => `taxes/rates/${id}`,
      providesTags: (result, error, id) => [{ type: 'TaxRate', id }],
    }),

    // Create a new tax rate
    createTaxRate: builder.mutation<TaxRate, Partial<TaxRate>>({
      query: (body) => ({
        url: 'taxes/rates',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['TaxRate'],
    }),

    // Update a tax rate
    updateTaxRate: builder.mutation<TaxRate, { id: string; body: Partial<TaxRate> }>({
      query: ({ id, body }) => ({
        url: `taxes/rates/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'TaxRate', id }],
    }),

    // Delete a tax rate
    deleteTaxRate: builder.mutation<void, string>({
      query: (id) => ({
        url: `taxes/rates/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TaxRate'],
    }),

    // Calculate tax amount
    calculateTax: builder.query<
      { baseAmount: number; taxAmount: number; totalAmount: number },
      { taxRateId: string; amount: number }
    >({
      query: ({ taxRateId, amount }) => `taxes/calculate?taxRateId=${taxRateId}&amount=${amount}`,
    }),
  }),
});

export const {
  useGetTaxRatesQuery,
  useGetTaxRateByIdQuery,
  useCreateTaxRateMutation,
  useUpdateTaxRateMutation,
  useDeleteTaxRateMutation,
  useCalculateTaxQuery,
} = taxRatesApi;
