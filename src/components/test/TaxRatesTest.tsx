'use client';

import React from 'react';
import { useGetTaxRatesQuery } from '@/redux/services/taxRatesApi';

export default function TaxRatesTest() {
  const { data: taxRates, error, isLoading } = useGetTaxRatesQuery();

  if (isLoading) {
    return <div className="p-4">Loading tax rates...</div>;
  }

  if (error) {
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded">
        <h3 className="text-red-800 font-semibold">Error loading tax rates:</h3>
        <pre className="text-red-600 text-sm mt-2">
          {JSON.stringify(error, null, 2)}
        </pre>
      </div>
    );
  }

  return (
    <div className="p-4 border border-green-300 bg-green-50 rounded">
      <h3 className="text-green-800 font-semibold mb-3">Tax Rates API Test - Success!</h3>
      <p className="text-green-700 mb-3">
        Found {taxRates?.length || 0} tax rates
      </p>
      {taxRates && taxRates.length > 0 && (
        <div className="space-y-2">
          {taxRates.map((rate, index) => (
            <div key={rate.id || index} className="bg-white p-2 rounded border">
              <div className="font-medium">{rate.name}</div>
              <div className="text-sm text-gray-600">
                Rate: {rate.rate}% | Active: {rate.isActive ? 'Yes' : 'No'}
              </div>
              {rate.description && (
                <div className="text-sm text-gray-500">{rate.description}</div>
              )}
            </div>
          ))}
        </div>
      )}
      {(!taxRates || taxRates.length === 0) && (
        <div className="text-green-700">
          No tax rates found, but API connection is working!
        </div>
      )}
    </div>
  );
}
